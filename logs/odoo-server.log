2025-09-05 08:55:20,767 250688 INFO ? odoo.modules.loading: loading 1 modules... 
2025-09-05 08:55:20,776 250688 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-09-05 08:55:20,788 250688 INFO ? odoo.modules.loading: loading 142 modules... 
2025-09-05 08:55:20,871 250688 INFO ? odoo.modules.loading: Loading module canbrax_configmatrix (137/142) 
2025-09-05 08:55:21,129 250688 CRITICAL ? odoo.modules.module: Couldn't load module canbrax_configmatrix 
2025-09-05 08:55:21,131 250688 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-09-05 08:55:21,132 250688 ERROR ? odoo.modules.registry: Failed to load registry 
2025-09-05 08:55:21,132 250688 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/registry.py", line 104, in __new__
    return cls.registries[db_name]
  File "<decorator-gen-6>", line 2, in __getitem__
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/tools/func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/tools/lru.py", line 33, in __getitem__
    a = self.d[obj]
KeyError: 'canbrax_sb_2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/http.py", line 2373, in __call__
    response = request._serve_db()
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/http.py", line 1877, in _serve_db
    self.registry, cr_readwrite = self._open_registry()
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/http.py", line 1487, in _open_registry
    registry = Registry(self.db)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/registry.py", line 106, in __new__
    return cls.new(db_name)
  File "<decorator-gen-13>", line 2, in new
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/tools/func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "/home/<USER>/odoo/itms/canbrax18/canbrax-odoo/canbrax_configmatrix/__init__.py", line 5, in <module>
    from . import controllers
  File "/home/<USER>/odoo/itms/canbrax18/canbrax-odoo/canbrax_configmatrix/controllers/__init__.py", line 5, in <module>
    from . import configuration_controller
  File "/home/<USER>/odoo/itms/canbrax18/canbrax-odoo/canbrax_configmatrix/controllers/configuration_controller.py", line 474
    def _calculate_operation_costs_field_option_mapping(self, template, field_values, quantity_multiplier):
SyntaxError: expected 'except' or 'finally' block
2025-09-05 08:55:21,********** INFO ? werkzeug: 127.0.0.1 - - [05/Sep/2025 08:55:21] "POST /web/dataset/call_button/sale.order.line/action_configure_product HTTP/1.1" 500 - 14 0.009 0.456
2025-09-05 08:55:21,********** INFO ? odoo.modules.loading: loading 1 modules... 
2025-09-05 08:55:21,********** INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-09-05 08:55:21,********** INFO ? odoo.modules.loading: loading 142 modules... 
2025-09-05 08:55:21,********** INFO ? odoo.modules.loading: Loading module canbrax_configmatrix (137/142) 
2025-09-05 08:55:21,********** CRITICAL ? odoo.modules.module: Couldn't load module canbrax_configmatrix 
2025-09-05 08:55:21,********** WARNING ? odoo.modules.loading: Transient module states were reset 
2025-09-05 08:55:21,********** ERROR ? odoo.modules.registry: Failed to load registry 
2025-09-05 08:55:21,********** ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/registry.py", line 104, in __new__
    return cls.registries[db_name]
  File "<decorator-gen-6>", line 2, in __getitem__
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/tools/func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/tools/lru.py", line 33, in __getitem__
    a = self.d[obj]
KeyError: 'canbrax_sb_2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/http.py", line 2373, in __call__
    response = request._serve_db()
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/http.py", line 1877, in _serve_db
    self.registry, cr_readwrite = self._open_registry()
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/http.py", line 1487, in _open_registry
    registry = Registry(self.db)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/registry.py", line 106, in __new__
    return cls.new(db_name)
  File "<decorator-gen-13>", line 2, in new
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/tools/func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "/home/<USER>/odoo/itms/canbrax18/canbrax-odoo/canbrax_configmatrix/__init__.py", line 5, in <module>
    from . import controllers
  File "/home/<USER>/odoo/itms/canbrax18/canbrax-odoo/canbrax_configmatrix/controllers/__init__.py", line 5, in <module>
    from . import configuration_controller
  File "/home/<USER>/odoo/itms/canbrax18/canbrax-odoo/canbrax_configmatrix/controllers/configuration_controller.py", line 474
    def _calculate_operation_costs_field_option_mapping(self, template, field_values, quantity_multiplier):
SyntaxError: expected 'except' or 'finally' block
2025-09-05 08:55:21,********** INFO ? werkzeug: 127.0.0.1 - - [05/Sep/2025 08:55:21] "GET /websocket?version=18.0-4 HTTP/1.1" 500 - 14 0.010 0.801
2025-09-05 08:55:23,********** INFO ? odoo.modules.loading: loading 1 modules... 
2025-09-05 08:55:23,********** INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-09-05 08:55:23,********** INFO ? odoo.modules.loading: loading 142 modules... 
2025-09-05 08:55:23,********** INFO ? odoo.modules.loading: Loading module canbrax_configmatrix (137/142) 
2025-09-05 08:55:23,********** CRITICAL ? odoo.modules.module: Couldn't load module canbrax_configmatrix 
2025-09-05 08:55:23,********** WARNING ? odoo.modules.loading: Transient module states were reset 
2025-09-05 08:55:23,********** ERROR ? odoo.modules.registry: Failed to load registry 
2025-09-05 08:55:23,********** ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/registry.py", line 104, in __new__
    return cls.registries[db_name]
  File "<decorator-gen-6>", line 2, in __getitem__
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/tools/func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/tools/lru.py", line 33, in __getitem__
    a = self.d[obj]
KeyError: 'canbrax_sb_2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/http.py", line 2373, in __call__
    response = request._serve_db()
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/http.py", line 1877, in _serve_db
    self.registry, cr_readwrite = self._open_registry()
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/http.py", line 1487, in _open_registry
    registry = Registry(self.db)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/registry.py", line 106, in __new__
    return cls.new(db_name)
  File "<decorator-gen-13>", line 2, in new
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/tools/func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "/home/<USER>/odoo/itms/canbrax18/canbrax-odoo/canbrax_configmatrix/__init__.py", line 5, in <module>
    from . import controllers
  File "/home/<USER>/odoo/itms/canbrax18/canbrax-odoo/canbrax_configmatrix/controllers/__init__.py", line 5, in <module>
    from . import configuration_controller
  File "/home/<USER>/odoo/itms/canbrax18/canbrax-odoo/canbrax_configmatrix/controllers/configuration_controller.py", line 474
    def _calculate_operation_costs_field_option_mapping(self, template, field_values, quantity_multiplier):
SyntaxError: expected 'except' or 'finally' block
2025-09-05 08:55:23,********** INFO ? werkzeug: 127.0.0.1 - - [05/Sep/2025 08:55:23] "POST /web/webclient/version_info HTTP/1.1" 500 - 14 0.006 0.239
2025-09-05 08:55:27,********** INFO ? odoo.modules.loading: loading 1 modules... 
2025-09-05 08:55:27,********** INFO ? odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-09-05 08:55:27,********** INFO ? odoo.modules.loading: loading 142 modules... 
2025-09-05 08:55:27,********** INFO ? odoo.modules.loading: Loading module canbrax_configmatrix (137/142) 
2025-09-05 08:55:27,********** CRITICAL ? odoo.modules.module: Couldn't load module canbrax_configmatrix 
2025-09-05 08:55:27,********** WARNING ? odoo.modules.loading: Transient module states were reset 
2025-09-05 08:55:27,********** ERROR ? odoo.modules.registry: Failed to load registry 
2025-09-05 08:55:27,********** ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/registry.py", line 104, in __new__
    return cls.registries[db_name]
  File "<decorator-gen-6>", line 2, in __getitem__
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/tools/func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/tools/lru.py", line 33, in __getitem__
    a = self.d[obj]
KeyError: 'canbrax_sb_2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/http.py", line 2373, in __call__
    response = request._serve_db()
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/http.py", line 1877, in _serve_db
    self.registry, cr_readwrite = self._open_registry()
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/http.py", line 1487, in _open_registry
    registry = Registry(self.db)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/registry.py", line 106, in __new__
    return cls.new(db_name)
  File "<decorator-gen-13>", line 2, in new
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/tools/func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "/home/<USER>/odoo/itms/canbrax18/canbrax-odoo/canbrax_configmatrix/__init__.py", line 5, in <module>
    from . import controllers
  File "/home/<USER>/odoo/itms/canbrax18/canbrax-odoo/canbrax_configmatrix/controllers/__init__.py", line 5, in <module>
    from . import configuration_controller
  File "/home/<USER>/odoo/itms/canbrax18/canbrax-odoo/canbrax_configmatrix/controllers/configuration_controller.py", line 474
    def _calculate_operation_costs_field_option_mapping(self, template, field_values, quantity_multiplier):
SyntaxError: expected 'except' or 'finally' block
2025-09-05 08:55:27,********** INFO ? werkzeug: 127.0.0.1 - - [05/Sep/2025 08:55:27] "POST /web/webclient/version_info HTTP/1.1" 500 - 14 0.006 0.344
2025-09-05 08:55:33,********** INFO ? odoo.modules.loading: loading 1 modules... 
2025-09-05 08:55:33,********** INFO ? odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-09-05 08:55:33,********** INFO ? odoo.modules.loading: loading 142 modules... 
2025-09-05 08:55:33,********** INFO ? odoo.modules.loading: Loading module canbrax_configmatrix (137/142) 
2025-09-05 08:55:33,********** CRITICAL ? odoo.modules.module: Couldn't load module canbrax_configmatrix 
2025-09-05 08:55:33,********** WARNING ? odoo.modules.loading: Transient module states were reset 
2025-09-05 08:55:33,********** ERROR ? odoo.modules.registry: Failed to load registry 
2025-09-05 08:55:33,********** ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/registry.py", line 104, in __new__
    return cls.registries[db_name]
  File "<decorator-gen-6>", line 2, in __getitem__
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/tools/func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/tools/lru.py", line 33, in __getitem__
    a = self.d[obj]
KeyError: 'canbrax_sb_2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/http.py", line 2373, in __call__
    response = request._serve_db()
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/http.py", line 1877, in _serve_db
    self.registry, cr_readwrite = self._open_registry()
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/http.py", line 1487, in _open_registry
    registry = Registry(self.db)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/registry.py", line 106, in __new__
    return cls.new(db_name)
  File "<decorator-gen-13>", line 2, in new
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/tools/func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "/home/<USER>/odoo/itms/canbrax18/canbrax-odoo/canbrax_configmatrix/__init__.py", line 5, in <module>
    from . import controllers
  File "/home/<USER>/odoo/itms/canbrax18/canbrax-odoo/canbrax_configmatrix/controllers/__init__.py", line 5, in <module>
    from . import configuration_controller
  File "/home/<USER>/odoo/itms/canbrax18/canbrax-odoo/canbrax_configmatrix/controllers/configuration_controller.py", line 474
    def _calculate_operation_costs_field_option_mapping(self, template, field_values, quantity_multiplier):
SyntaxError: expected 'except' or 'finally' block
2025-09-05 08:55:33,********** INFO ? werkzeug: 127.0.0.1 - - [05/Sep/2025 08:55:33] "POST /web/webclient/version_info HTTP/1.1" 500 - 14 0.006 0.206
2025-09-05 08:55:35,********** INFO ? odoo.modules.loading: loading 1 modules... 
2025-09-05 08:55:35,********** INFO ? odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-09-05 08:55:35,********** INFO ? odoo.modules.loading: loading 142 modules... 
2025-09-05 08:55:35,********** INFO ? odoo.modules.loading: Loading module canbrax_configmatrix (137/142) 
2025-09-05 08:55:35,********** CRITICAL ? odoo.modules.module: Couldn't load module canbrax_configmatrix 
2025-09-05 08:55:35,********** WARNING ? odoo.modules.loading: Transient module states were reset 
2025-09-05 08:55:35,********** ERROR ? odoo.modules.registry: Failed to load registry 
2025-09-05 08:55:35,********** ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/registry.py", line 104, in __new__
    return cls.registries[db_name]
  File "<decorator-gen-6>", line 2, in __getitem__
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/tools/func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/tools/lru.py", line 33, in __getitem__
    a = self.d[obj]
KeyError: 'canbrax_sb_2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/http.py", line 2373, in __call__
    response = request._serve_db()
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/http.py", line 1877, in _serve_db
    self.registry, cr_readwrite = self._open_registry()
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/http.py", line 1487, in _open_registry
    registry = Registry(self.db)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/registry.py", line 106, in __new__
    return cls.new(db_name)
  File "<decorator-gen-13>", line 2, in new
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/tools/func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "/home/<USER>/odoo/itms/canbrax18/canbrax-odoo/canbrax_configmatrix/__init__.py", line 5, in <module>
    from . import controllers
  File "/home/<USER>/odoo/itms/canbrax18/canbrax-odoo/canbrax_configmatrix/controllers/__init__.py", line 5, in <module>
    from . import configuration_controller
  File "/home/<USER>/odoo/itms/canbrax18/canbrax-odoo/canbrax_configmatrix/controllers/configuration_controller.py", line 474
    def _calculate_operation_costs_field_option_mapping(self, template, field_values, quantity_multiplier):
SyntaxError: expected 'except' or 'finally' block
2025-09-05 08:55:35,********** INFO ? werkzeug: 127.0.0.1 - - [05/Sep/2025 08:55:35] "GET /websocket?version=18.0-4 HTTP/1.1" 500 - 14 0.005 0.242
2025-09-05 08:55:41,********** INFO ? odoo.modules.loading: loading 1 modules... 
2025-09-05 08:55:41,********** INFO ? odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-09-05 08:55:41,********** INFO ? odoo.modules.loading: loading 142 modules... 
2025-09-05 08:55:42,********** INFO ? odoo.modules.loading: Loading module canbrax_configmatrix (137/142) 
2025-09-05 08:55:42,********** CRITICAL ? odoo.modules.module: Couldn't load module canbrax_configmatrix 
2025-09-05 08:55:42,********** WARNING ? odoo.modules.loading: Transient module states were reset 
2025-09-05 08:55:42,********** ERROR ? odoo.modules.registry: Failed to load registry 
2025-09-05 08:55:42,********** ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/registry.py", line 104, in __new__
    return cls.registries[db_name]
  File "<decorator-gen-6>", line 2, in __getitem__
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/tools/func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/tools/lru.py", line 33, in __getitem__
    a = self.d[obj]
KeyError: 'canbrax_sb_2'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/http.py", line 2373, in __call__
    response = request._serve_db()
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/http.py", line 1877, in _serve_db
    self.registry, cr_readwrite = self._open_registry()
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/http.py", line 1487, in _open_registry
    registry = Registry(self.db)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/registry.py", line 106, in __new__
    return cls.new(db_name)
  File "<decorator-gen-13>", line 2, in new
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/tools/func.py", line 97, in locked
    return func(inst, *args, **kwargs)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/loading.py", line 480, in load_modules
    processed_modules += load_marked_modules(env, graph,
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/loading.py", line 186, in load_module_graph
    load_openerp_module(package.name)
  File "/home/<USER>/odoo/itms/canbrax18/odoo/odoo/modules/module.py", line 384, in load_openerp_module
    __import__(qualname)
  File "/home/<USER>/odoo/itms/canbrax18/canbrax-odoo/canbrax_configmatrix/__init__.py", line 5, in <module>
    from . import controllers
  File "/home/<USER>/odoo/itms/canbrax18/canbrax-odoo/canbrax_configmatrix/controllers/__init__.py", line 5, in <module>
    from . import configuration_controller
  File "/home/<USER>/odoo/itms/canbrax18/canbrax-odoo/canbrax_configmatrix/controllers/configuration_controller.py", line 474
    def _calculate_operation_costs_field_option_mapping(self, template, field_values, quantity_multiplier):
SyntaxError: expected 'except' or 'finally' block
2025-09-05 08:55:42,********** INFO ? werkzeug: 127.0.0.1 - - [05/Sep/2025 08:55:42] "POST /web/webclient/version_info HTTP/1.1" 500 - 14 0.005 0.321
