operation_costs_handler.js?v=1757062690:235 [OPERATION_COSTS_BUG] ===== FRONTEND OPERATION COST REQUEST =====
operation_costs_handler.js?v=1757062690:236 [OPERATION_COSTS_BUG] Template ID: 24
operation_costs_handler.js?v=1757062690:237 [OPERATION_COSTS_BUG] Config ID: null
operation_costs_handler.js?v=1757062690:238 [OPERATION_COSTS_BUG] Field values count: 300
operation_costs_handler.js?v=1757062690:239 [OPERATION_COSTS_BUG] Will use: Field/option mapping
operation_costs_handler.js?v=1757062690:286 [OPERATION_COSTS_BUG] ===== FRONTEND OPERATION COST RESULT =====
operation_costs_handler.js?v=1757062690:287 [OPERATION_COSTS_BUG] Operations found: 41
operation_costs_handler.js?v=1757062690:288 [OPERATION_COSTS_BUG] Total cost: $95.92
operation_costs_handler.js?v=1757062690:289 [OPERATION_COSTS_BUG] Calculation method: Field/option mapping
operation_costs_handler.js?v=1757062690:290 [OPERATION_COSTS_BUG] ===== END RESULT =====
operation_costs_handler.js?v=1757062690:235 [OPERATION_COSTS_BUG] ===== FRONTEND OPERATION COST REQUEST =====
operation_costs_handler.js?v=1757062690:236 [OPERATION_COSTS_BUG] Template ID: 24
operation_costs_handler.js?v=1757062690:237 [OPERATION_COSTS_BUG] Config ID: null
operation_costs_handler.js?v=1757062690:238 [OPERATION_COSTS_BUG] Field values count: 300
operation_costs_handler.js?v=1757062690:239 [OPERATION_COSTS_BUG] Will use: Field/option mapping
operation_costs_handler.js?v=1757062690:286 [OPERATION_COSTS_BUG] ===== FRONTEND OPERATION COST RESULT =====
operation_costs_handler.js?v=1757062690:287 [OPERATION_COSTS_BUG] Operations found: 41
operation_costs_handler.js?v=1757062690:288 [OPERATION_COSTS_BUG] Total cost: $95.92
operation_costs_handler.js?v=1757062690:289 [OPERATION_COSTS_BUG] Calculation method: Field/option mapping
operation_costs_handler.js?v=1757062690:290 [OPERATION_COSTS_BUG] ===== END RESULT =====
operation_costs_handler.js?v=1757062690:235 [OPERATION_COSTS_BUG] ===== FRONTEND OPERATION COST REQUEST =====
operation_costs_handler.js?v=1757062690:236 [OPERATION_COSTS_BUG] Template ID: 24
operation_costs_handler.js?v=1757062690:237 [OPERATION_COSTS_BUG] Config ID: null
operation_costs_handler.js?v=1757062690:238 [OPERATION_COSTS_BUG] Field values count: 300
operation_costs_handler.js?v=1757062690:239 [OPERATION_COSTS_BUG] Will use: Field/option mapping
operation_costs_handler.js?v=1757062690:286 [OPERATION_COSTS_BUG] ===== FRONTEND OPERATION COST RESULT =====
operation_costs_handler.js?v=1757062690:287 [OPERATION_COSTS_BUG] Operations found: 41
operation_costs_handler.js?v=1757062690:288 [OPERATION_COSTS_BUG] Total cost: $95.92
operation_costs_handler.js?v=1757062690:289 [OPERATION_COSTS_BUG] Calculation method: Field/option mapping
operation_costs_handler.js?v=1757062690:290 [OPERATION_COSTS_BUG] ===== END RESULT =====
 [OPERATION_COSTS_BUG] ===== FRONTEND OPERATION COST REQUEST =====
 [OPERATION_COSTS_BUG] Template ID: 24
 [OPERATION_COSTS_BUG] Config ID: null
 [OPERATION_COSTS_BUG] Field values count: 300
 [OPERATION_COSTS_BUG] Will use: Field/option mapping
 [OPERATION_COSTS_BUG] ===== FRONTEND OPERATION COST RESULT =====
 [OPERATION_COSTS_BUG] Operations found: 50
 [OPERATION_COSTS_BUG] Total cost: $221.871536
 [OPERATION_COSTS_BUG] Calculation method: Field/option mapping
 [OPERATION_COSTS_BUG] ===== END RESULT =====
 [OPERATION_COSTS_BUG] ===== FRONTEND OPERATION COST REQUEST =====
 [OPERATION_COSTS_BUG] Template ID: 24
 [OPERATION_COSTS_BUG] Config ID: null
 [OPERATION_COSTS_BUG] Field values count: 300
 [OPERATION_COSTS_BUG] Will use: Field/option mapping
 [OPERATION_COSTS_BUG] ===== FRONTEND OPERATION COST RESULT =====
 [OPERATION_COSTS_BUG] Operations found: 50
 [OPERATION_COSTS_BUG] Total cost: $221.871536
 [OPERATION_COSTS_BUG] Calculation method: Field/option mapping
 [OPERATION_COSTS_BUG] ===== END RESULT =====
operation_costs_handler.js?v=1757062690:235 [OPERATION_COSTS_BUG] ===== FRONTEND OPERATION COST REQUEST =====
operation_costs_handler.js?v=1757062690:236 [OPERATION_COSTS_BUG] Template ID: 24
operation_costs_handler.js?v=1757062690:237 [OPERATION_COSTS_BUG] Config ID: null
operation_costs_handler.js?v=1757062690:238 [OPERATION_COSTS_BUG] Field values count: 300
operation_costs_handler.js?v=1757062690:239 [OPERATION_COSTS_BUG] Will use: Field/option mapping
operation_costs_handler.js?v=1757062690:286 [OPERATION_COSTS_BUG] ===== FRONTEND OPERATION COST RESULT =====
operation_costs_handler.js?v=1757062690:287 [OPERATION_COSTS_BUG] Operations found: 50
operation_costs_handler.js?v=1757062690:288 [OPERATION_COSTS_BUG] Total cost: $221.871536
operation_costs_handler.js?v=1757062690:289 [OPERATION_COSTS_BUG] Calculation method: Field/option mapping
operation_costs_handler.js?v=1757062690:290 [OPERATION_COSTS_BUG] ===== END RESULT =====
