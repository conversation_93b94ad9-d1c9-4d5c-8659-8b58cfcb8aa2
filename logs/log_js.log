web.assets_web.min.js:24 The following modules are needed by other modules but have not been defined, they may not be present in the correct asset bundle: ['@stock_barcode_mrp/models/barcode_mrp_model']
reportErrors @ web.assets_web.min.js:24
(anonymous) @ web.assets_web.min.js:10
Promise.then
define @ web.assets_web.min.js:10
(anonymous) @ web.assets_web.min.js:1641
web.assets_web.min.js:26 The following modules could not be loaded because they have unmet dependencies, this is a secondary error which is likely caused by one of the above problems: ['@stock_barcode_quality_mrp/models/barcode_mrp_model']
reportErrors @ web.assets_web.min.js:26
(anonymous) @ web.assets_web.min.js:10
Promise.then
define @ web.assets_web.min.js:10
(anonymous) @ web.assets_web.min.js:1641
web.assets_web.min.js:22251 [MANUAL] Cleared manual edit state for new form session
web.assets_web.min.js:22186 No template ID found for loading calculated fields
loadCalculatedFields @ web.assets_web.min.js:22186
(anonymous) @ web.assets_web.min.js:22541
web.assets_web.min.js:22216 [MIDRAIL-JS] Calculating midrail height...
web.assets_web.min.js:22218 [MIDRAIL-JS] No midrail height field found
web.assets_web.min.js:22216 [MIDRAIL-JS] Result: 0
web.assets_web.min.js:22272 [09:20:28.556] updateFieldVisibility >> (called from: at HTMLDocument.<anonymous> (http://localhost:8069/web/assets/87f359f/web.assets_web.min.js:22541:374) )
web.assets_web.min.js:22272 [09:20:28.556] updateFieldVisibility >> (called from: at HTMLDocument.<anonymous> (http://localhost:8069/web/assets/87f359f/web.assets_web.min.js:22544:1) )
web.assets_web.min.js:10757 click button.o_list_button_add
shim.js:1 (Marker.io) Successfully loaded! (v2.32.0)
3.v2.32.0.efdbf172205a0ca5bb98.js:1  POST https://api.marker.io/widget/ping net::ERR_BLOCKED_BY_CLIENT
I @ 3.v2.32.0.efdbf172205a0ca5bb98.js:1
pingWidget @ 3.v2.32.0.efdbf172205a0ca5bb98.js:1
bootstrap @ 3.v2.32.0.efdbf172205a0ca5bb98.js:1
(anonymous) @ 2.v2.32.0.46af2d13218c237a79f6.js:1
f.dispatch @ 2.v2.32.0.46af2d13218c237a79f6.js:7
dispatch @ 2.v2.32.0.46af2d13218c237a79f6.js:1
We @ 3.v2.32.0.efdbf172205a0ca5bb98.js:1
await in We
(anonymous) @ shim.js:1
await in (anonymous)
(anonymous) @ shim.js:1
o @ shim.js:1
(anonymous) @ shim.js:1
(anonymous) @ shim.js:1
shim.js:1 (Marker.io) SR not loaded
h @ shim.js:1
(anonymous) @ 3.v2.32.0.efdbf172205a0ca5bb98.js:1
unload @ 3.v2.32.0.efdbf172205a0ca5bb98.js:1
bootstrap @ 3.v2.32.0.efdbf172205a0ca5bb98.js:1
await in bootstrap
(anonymous) @ 2.v2.32.0.46af2d13218c237a79f6.js:1
f.dispatch @ 2.v2.32.0.46af2d13218c237a79f6.js:7
dispatch @ 2.v2.32.0.46af2d13218c237a79f6.js:1
We @ 3.v2.32.0.efdbf172205a0ca5bb98.js:1
await in We
(anonymous) @ shim.js:1
await in (anonymous)
(anonymous) @ shim.js:1
o @ shim.js:1
(anonymous) @ shim.js:1
(anonymous) @ shim.js:1
web.assets_web.min.js:7695 Missing widget: res_partner_many2one for field of type many2one
getFieldFromRegistry @ web.assets_web.min.js:7695
parseFieldNode @ web.assets_web.min.js:7704
(anonymous) @ web.assets_web.min.js:8629
visit @ web.assets_web.min.js:4751
visitChildren @ web.assets_web.min.js:4750
visit @ web.assets_web.min.js:4751
visitChildren @ web.assets_web.min.js:4750
visit @ web.assets_web.min.js:4751
visitChildren @ web.assets_web.min.js:4750
visit @ web.assets_web.min.js:4751
visitChildren @ web.assets_web.min.js:4750
visit @ web.assets_web.min.js:4751
visitXML @ web.assets_web.min.js:4751
parse @ web.assets_web.min.js:8629
parse @ web.assets_web.min.js:16448
props @ web.assets_web.min.js:8834
loadView @ web.assets_web.min.js:9603
await in loadView
(anonymous) @ web.assets_web.min.js:9586
(anonymous) @ web.assets_web.min.js:1064
initiateRender @ web.assets_web.min.js:1064
(anonymous) @ web.assets_web.min.js:1618
template @ VM324575:9
_render @ web.assets_web.min.js:997
render @ web.assets_web.min.js:996
initiateRender @ web.assets_web.min.js:1066
await in initiateRender
(anonymous) @ web.assets_web.min.js:1618
template @ VM324544:14
_render @ web.assets_web.min.js:997
render @ web.assets_web.min.js:996
render @ web.assets_web.min.js:1074
await in render
render @ web.assets_web.min.js:1121
onActionManagerUpdate @ web.assets_web.min.js:9890
trigger @ web.assets_web.min.js:765
_updateUI @ web.assets_web.min.js:10003
_executeActWindowAction @ web.assets_web.min.js:10015
doAction @ web.assets_web.min.js:10034
await in doAction
loadState @ web.assets_web.min.js:10063
await in loadState
loadRouterState @ web.assets_web.min.js:10496
(anonymous) @ web.assets_web.min.js:10493
complete @ web.assets_web.min.js:1011
processFiber @ web.assets_web.min.js:1590
processTasks @ web.assets_web.min.js:1584
(anonymous) @ web.assets_web.min.js:1581
requestAnimationFrame
flush @ web.assets_web.min.js:1581
setCounter @ web.assets_web.min.js:1006
_render @ web.assets_web.min.js:999
render @ web.assets_web.min.js:996
initiateRender @ web.assets_web.min.js:1066
await in initiateRender
(anonymous) @ web.assets_web.min.js:1618
template @ VM324573:12
_render @ web.assets_web.min.js:997
render @ web.assets_web.min.js:996
initiateRender @ web.assets_web.min.js:1066
await in initiateRender
(anonymous) @ web.assets_web.min.js:1618
slot1 @ VM324560:12
callSlot @ web.assets_web.min.js:1167
template @ VM324546:8
_render @ web.assets_web.min.js:997
render @ web.assets_web.min.js:996
initiateRender @ web.assets_web.min.js:1066
await in initiateRender
(anonymous) @ web.assets_web.min.js:1618
template @ VM324560:27
_render @ web.assets_web.min.js:997
render @ web.assets_web.min.js:996
initiateRender @ web.assets_web.min.js:1066
await in initiateRender
(anonymous) @ web.assets_web.min.js:1618
slot1 @ VM324545:13
callSlot @ web.assets_web.min.js:1167
template @ VM324546:8
_render @ web.assets_web.min.js:997
render @ web.assets_web.min.js:996
initiateRender @ web.assets_web.min.js:1066
await in initiateRender
(anonymous) @ web.assets_web.min.js:1618
template @ VM324545:25
_render @ web.assets_web.min.js:997
render @ web.assets_web.min.js:996
initiateRender @ web.assets_web.min.js:1066
await in initiateRender
(anonymous) @ web.assets_web.min.js:1618
template @ VM324537:15
_render @ web.assets_web.min.js:997
render @ web.assets_web.min.js:996
initiateRender @ web.assets_web.min.js:1066
await in initiateRender
mountComponent @ web.assets_web.min.js:1062
mountNode @ web.assets_web.min.js:1603
mount @ web.assets_web.min.js:1599
mount @ web.assets_web.min.js:1595
mountComponent @ web.assets_web.min.js:1660
await in mountComponent
startWebClient @ web.assets_web.min.js:25210
await in startWebClient
(anonymous) @ web.assets_web.min.js:25207
startModule @ web.assets_web.min.js:41
startModules @ web.assets_web.min.js:40
addJob @ web.assets_web.min.js:5
define @ web.assets_web.min.js:10
(anonymous) @ web.assets_web.min.js:25210
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
[Violation] Added non-passive event listener to a scroll-blocking <some> event. Consider marking event handler as 'passive' to make the page more responsive. See <URL>
web.assets_web.min.js:1581 [Violation] 'requestAnimationFrame' handler took 153ms
[Violation] Forced reflow while executing JavaScript took 75ms
