2025-09-05 11:16:38,109 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 11:16:38,110 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 11:16:38,110 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 11:16:38,110 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 11:16:38,111 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 240 
2025-09-05 11:16:38,394 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 11:16:38,394 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 11:16:38,394 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 11:16:38,394 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 11:16:38,394 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 11:16:38,394 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 11:16:38,394 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 11:16:38,394 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Key field values - hinge_pickup: 3_per_door_attached, hinge_deliver: 3_per_door_loose_drilled 
2025-09-05 11:16:38,412 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Receive Material Time' from field 'Frame Colour' - Cost: $5.6000000000000005 
2025-09-05 11:16:38,414 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Load / Unload Extrusion & Set Up' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:16:38,419 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Centre Lock Cut Out' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:16:38,437 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Install Centre Lock' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $6.48 
2025-09-05 11:16:38,440 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Pick Centre Lock Site Kit' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $1.36 
2025-09-05 11:16:38,441 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC Picked Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:16:38,444 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Wrap Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:16:38,459 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Delivery Time - Time allocated to CD1 even for pick up orders' from field 'Number of Security Hinges (Pick Up)' - Cost: $5.6000000000000005 
2025-09-05 11:16:38,465 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:16:38,467 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:16:38,468 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:16:38,469 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:16:38,471 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:16:38,473 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:16:38,505 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Cut Extrusions on Upcut Saw' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:16:38,505 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up 2' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:16:38,506 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Extra Time to Cut French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:16:38,507 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Notch Slots for Centre Lock & French Door Mullion Caps in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:16:38,508 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Remove Breakaway Sections for Centre Notch & French Door Mullion Cap Notches in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:16:38,509 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert French Door Mullion Mohair (Schlegel PB48753B) into French Door Mullion or T-Section Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:16:38,510 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert Top & Bottom French Door Mullion Caps' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:16:38,512 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet French Door Mullion onto Lock Door or T-Section Mullion onto Non-Lock Door' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:16:38,516 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hardware' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $1.36 
2025-09-05 11:16:38,518 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:16:38,520 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:16:38,537 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Non-Lock Door Centre Striker Cut Out' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $1.36 
2025-09-05 11:16:38,540 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $2.64 
2025-09-05 11:16:38,545 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:16:38,546 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:16:38,551 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 11:16:38,551 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet Flush Bolts into Non-Lock Door' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $18.720000000000002 
2025-09-05 11:16:38,552 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:16:38,553 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:16:38,555 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:16:38,556 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:16:38,559 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:16:38,563 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:38,563 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:38,564 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:38,565 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:38,567 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $0.96 
2025-09-05 11:16:38,567 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:38,567 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:38,568 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:38,568 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:38,569 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $0.96 
2025-09-05 11:16:38,570 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:38,570 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:38,572 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:38,573 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:38,574 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Install Hinges' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $8.639999999999999 
2025-09-05 11:16:38,575 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:38,575 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:38,576 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:38,577 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:38,578 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hinges' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.4 
2025-09-05 11:16:38,580 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.64 
2025-09-05 11:16:38,581 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.64 
2025-09-05 11:16:38,583 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:38,583 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:38,585 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:38,585 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:38,586 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:16:38,587 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:38,588 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:38,589 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:38,589 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:38,590 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:16:38,591 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:38,592 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:38,592 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:38,593 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:38,594 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hinges' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:16:38,595 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:38,596 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:38,597 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:38,597 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:38,599 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:16:38,599 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:38,599 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:38,600 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:38,601 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:38,602 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:16:38,603 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:16:38,605 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:16:38,624 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 11:16:38,625 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 41 
2025-09-05 11:16:38,625 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $95.92 
2025-09-05 11:16:38,625 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 11:16:38,625 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 11:16:38,626 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 from 41 operations 
2025-09-05 11:16:38,626 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 
2025-09-05 11:16:38,858 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:16:38,876 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:38,876 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:38,877 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:38,878 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:38,879 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:38,879 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:38,880 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:38,881 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:38,883 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:38,884 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:38,885 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:38,885 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:38,886 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:38,886 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:38,887 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:38,888 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:38,889 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:38,889 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:38,896 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:16:38,897 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:16:38,897 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:16:42,958 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:16:42,968 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:42,968 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:42,970 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:42,970 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:42,972 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:42,972 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:42,974 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:42,974 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:42,976 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:42,976 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:42,977 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:42,978 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:42,979 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:42,979 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:42,980 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:42,980 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:42,981 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:42,981 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:42,984 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:16:42,985 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:16:42,986 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:16:49,640 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 11:16:49,641 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 11:16:49,641 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 11:16:49,641 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 11:16:49,642 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 11:16:50,128 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 11:16:50,129 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 11:16:50,129 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 11:16:50,130 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 11:16:50,130 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 11:16:50,130 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 11:16:50,131 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 11:16:50,131 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Key field values - hinge_pickup: 3_per_door_attached, hinge_deliver: 3_per_door_loose_drilled 
2025-09-05 11:16:50,161 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Receive Material Time' from field 'Frame Colour' - Cost: $5.6000000000000005 
2025-09-05 11:16:50,166 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Load / Unload Extrusion & Set Up' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:16:50,190 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Centre Lock Cut Out' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:16:50,455 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Install Centre Lock' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $6.48 
2025-09-05 11:16:50,460 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Pick Centre Lock Site Kit' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $1.36 
2025-09-05 11:16:50,463 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC Picked Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:16:50,467 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Wrap Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:16:50,486 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Delivery Time - Time allocated to CD1 even for pick up orders' from field 'Number of Security Hinges (Pick Up)' - Cost: $5.6000000000000005 
2025-09-05 11:16:50,495 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:16:50,497 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:16:50,500 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:16:50,501 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:16:50,506 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:16:50,508 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:16:50,561 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Cut Extrusions on Upcut Saw' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:16:50,564 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up 2' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:16:50,566 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Extra Time to Cut French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:16:50,568 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Notch Slots for Centre Lock & French Door Mullion Caps in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:16:50,570 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Remove Breakaway Sections for Centre Notch & French Door Mullion Cap Notches in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:16:50,573 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert French Door Mullion Mohair (Schlegel PB48753B) into French Door Mullion or T-Section Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:16:50,574 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert Top & Bottom French Door Mullion Caps' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:16:50,576 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet French Door Mullion onto Lock Door or T-Section Mullion onto Non-Lock Door' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:16:50,583 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hardware' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $1.36 
2025-09-05 11:16:50,587 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:16:50,589 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:16:50,607 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Non-Lock Door Centre Striker Cut Out' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $1.36 
2025-09-05 11:16:50,610 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $2.64 
2025-09-05 11:16:50,617 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:16:50,618 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:16:50,625 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 11:16:50,627 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet Flush Bolts into Non-Lock Door' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $18.720000000000002 
2025-09-05 11:16:50,630 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:16:50,632 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:16:50,635 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:16:50,638 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:16:50,640 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:16:50,648 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:50,649 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:50,651 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:50,652 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:50,655 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $0.96 
2025-09-05 11:16:50,656 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:50,656 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:50,657 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:50,658 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:50,661 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $0.96 
2025-09-05 11:16:50,661 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:50,662 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:50,663 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:50,664 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:50,666 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Install Hinges' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $8.639999999999999 
2025-09-05 11:16:50,666 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:50,666 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:50,671 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:50,672 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:50,673 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hinges' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.4 
2025-09-05 11:16:50,675 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.64 
2025-09-05 11:16:50,677 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.64 
2025-09-05 11:16:50,680 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:50,681 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:50,681 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:50,682 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:50,683 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:16:50,684 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:50,684 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:50,685 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:50,685 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:50,688 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:16:50,689 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:50,689 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:50,690 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:50,691 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:50,694 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hinges' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:16:50,695 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:50,696 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:50,698 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:50,698 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:50,700 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:16:50,701 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:50,701 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:50,702 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:50,703 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:50,705 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:16:50,707 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:16:50,710 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:16:50,729 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 11:16:50,730 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 41 
2025-09-05 11:16:50,730 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $95.92 
2025-09-05 11:16:50,730 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 11:16:50,730 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 11:16:50,730 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 from 41 operations 
2025-09-05 11:16:50,730 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 
2025-09-05 11:16:50,916 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:16:50,927 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:50,927 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:50,929 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:50,929 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:50,930 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:50,931 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:50,932 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:50,933 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:50,936 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:50,937 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:50,938 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:50,939 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:50,940 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:50,940 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:50,942 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:50,942 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:50,943 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:50,944 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:50,948 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:16:50,949 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:16:50,950 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:16:53,002 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 11:16:53,005 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 11:16:53,005 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 11:16:53,005 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 11:16:53,005 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 11:16:53,755 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 11:16:53,757 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 11:16:53,757 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 11:16:53,759 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 11:16:53,760 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 11:16:53,760 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 11:16:53,760 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 11:16:53,761 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Key field values - hinge_pickup: 3_per_door_attached, hinge_deliver: 3_per_door_loose_drilled 
2025-09-05 11:16:53,782 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Receive Material Time' from field 'Frame Colour' - Cost: $5.6000000000000005 
2025-09-05 11:16:53,784 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Load / Unload Extrusion & Set Up' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:16:53,793 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Centre Lock Cut Out' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:16:53,815 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Install Centre Lock' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $6.48 
2025-09-05 11:16:53,817 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Pick Centre Lock Site Kit' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $1.36 
2025-09-05 11:16:53,821 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC Picked Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:16:53,824 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Wrap Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:16:53,841 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Delivery Time - Time allocated to CD1 even for pick up orders' from field 'Number of Security Hinges (Pick Up)' - Cost: $5.6000000000000005 
2025-09-05 11:16:53,848 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:16:53,850 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:16:53,851 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:16:53,852 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:16:53,855 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:16:53,856 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:16:53,891 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Cut Extrusions on Upcut Saw' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:16:53,892 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up 2' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:16:53,894 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Extra Time to Cut French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:16:53,895 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Notch Slots for Centre Lock & French Door Mullion Caps in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:16:53,897 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Remove Breakaway Sections for Centre Notch & French Door Mullion Cap Notches in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:16:53,899 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert French Door Mullion Mohair (Schlegel PB48753B) into French Door Mullion or T-Section Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:16:53,900 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert Top & Bottom French Door Mullion Caps' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:16:53,902 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet French Door Mullion onto Lock Door or T-Section Mullion onto Non-Lock Door' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:16:53,911 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hardware' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $1.36 
2025-09-05 11:16:53,914 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:16:53,917 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:16:53,936 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Non-Lock Door Centre Striker Cut Out' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $1.36 
2025-09-05 11:16:53,940 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $2.64 
2025-09-05 11:16:53,945 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:16:53,946 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:16:53,951 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 11:16:53,953 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet Flush Bolts into Non-Lock Door' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $18.720000000000002 
2025-09-05 11:16:53,955 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:16:53,957 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:16:53,959 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:16:53,961 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:16:53,963 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:16:53,968 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:53,968 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:53,970 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:53,971 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:53,973 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $0.96 
2025-09-05 11:16:53,973 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:53,973 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:53,974 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:53,975 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:53,976 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $0.96 
2025-09-05 11:16:53,976 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:53,976 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:53,978 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:53,979 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:53,980 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Install Hinges' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $8.639999999999999 
2025-09-05 11:16:53,980 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:53,981 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:53,982 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:53,983 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:53,984 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hinges' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.4 
2025-09-05 11:16:53,986 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.64 
2025-09-05 11:16:53,988 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.64 
2025-09-05 11:16:53,990 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:53,991 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:53,992 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:53,992 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:53,993 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:16:53,994 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:53,994 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:53,995 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:53,995 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:53,997 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:16:53,997 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:53,998 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:54,000 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:54,000 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:54,001 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hinges' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:16:54,002 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:54,003 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:54,004 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:54,004 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:54,005 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:16:54,006 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:54,006 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:54,007 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:54,007 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:54,008 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:16:54,010 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:16:54,011 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:16:54,031 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 11:16:54,031 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 41 
2025-09-05 11:16:54,031 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $95.92 
2025-09-05 11:16:54,031 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 11:16:54,032 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 11:16:54,032 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 from 41 operations 
2025-09-05 11:16:54,032 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 
2025-09-05 11:16:54,407 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:16:54,414 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:54,414 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:54,415 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:54,416 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:54,416 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:54,417 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:54,418 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:54,418 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:54,422 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:54,422 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:54,423 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:54,423 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:54,424 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:54,424 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:54,425 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:54,426 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:54,426 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:16:54,427 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:16:54,430 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:16:54,431 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:16:54,432 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:06,562 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 11:17:06,562 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 11:17:06,562 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 11:17:06,562 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 11:17:06,562 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 11:17:07,072 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 11:17:07,072 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 11:17:07,073 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 11:17:07,073 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 11:17:07,073 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 11:17:07,074 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 11:17:07,074 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 11:17:07,074 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Key field values - hinge_pickup: 3_per_door_attached, hinge_deliver: 3_per_door_loose_drilled 
2025-09-05 11:17:07,093 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Receive Material Time' from field 'Frame Colour' - Cost: $5.6000000000000005 
2025-09-05 11:17:07,096 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Load / Unload Extrusion & Set Up' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:17:07,098 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC & Colour Consitency CommandeX Door Frame' from field 'Frame Colour' - Cost: $3.8919024624 
2025-09-05 11:17:07,101 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Cut CommandeX Door Frame' from field 'Frame Colour' - Cost: $15.42906316 
2025-09-05 11:17:07,102 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Cut Plugh for CommandeX Door Frame' from field 'Frame Colour' - Cost: $6.0834591888 
2025-09-05 11:17:07,106 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Centre Lock Cut Out' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:17:07,107 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Insert Plugh into CommandeX Door Frame' from field 'Frame Colour' - Cost: $16.058820840000003 
2025-09-05 11:17:07,109 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Assemble CommandeX Door with Crimped Corners' from field 'Frame Colour' - Cost: $33.364561886400004 
2025-09-05 11:17:07,110 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Plugh CommandeX Door with Assembly Press' from field 'Frame Colour' - Cost: $18.5526612528 
2025-09-05 11:17:07,116 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC CommandeX Door' from field 'Frame Colour' - Cost: $17.9229035728 
2025-09-05 11:17:07,117 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Cut Mesh or Sheet' from field 'Frame Colour' - Cost: $12.003181380800001 
2025-09-05 11:17:07,129 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Install Centre Lock' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $6.48 
2025-09-05 11:17:07,132 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Pick Centre Lock Site Kit' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $1.36 
2025-09-05 11:17:07,137 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC Picked Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:17:07,140 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Wrap Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:17:07,155 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Load Door (Delivery) / Customer Collect (Pick Up)' from field 'Number of Security Hinges (Pick Up)' - Cost: $2.6449822560000005 
2025-09-05 11:17:07,160 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Delivery Time - Time allocated to CD1 even for pick up orders' from field 'Number of Security Hinges (Pick Up)' - Cost: $5.6000000000000005 
2025-09-05 11:17:07,167 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:07,169 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:07,172 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:07,174 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:07,176 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:07,177 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:07,216 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Cut Extrusions on Upcut Saw' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:07,217 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up 2' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:07,220 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Extra Time to Cut French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:07,223 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Notch Slots for Centre Lock & French Door Mullion Caps in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:07,226 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Remove Breakaway Sections for Centre Notch & French Door Mullion Cap Notches in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:07,228 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert French Door Mullion Mohair (Schlegel PB48753B) into French Door Mullion or T-Section Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:07,231 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert Top & Bottom French Door Mullion Caps' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:07,232 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet French Door Mullion onto Lock Door or T-Section Mullion onto Non-Lock Door' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:07,244 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hardware' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $1.36 
2025-09-05 11:17:07,250 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:17:07,255 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:17:07,294 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Non-Lock Door Centre Striker Cut Out' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $1.36 
2025-09-05 11:17:07,302 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $2.64 
2025-09-05 11:17:07,398 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:17:07,431 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:17:07,476 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 11:17:07,502 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet Flush Bolts into Non-Lock Door' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $18.720000000000002 
2025-09-05 11:17:07,540 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:17:07,555 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:17:07,565 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:17:07,577 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:17:07,582 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:17:07,595 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:07,596 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:07,599 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:07,601 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:07,605 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $0.96 
2025-09-05 11:17:07,606 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:07,607 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:07,610 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:07,610 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:07,612 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $0.96 
2025-09-05 11:17:07,613 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:07,614 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:07,618 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:07,619 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:07,622 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Install Hinges' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $8.639999999999999 
2025-09-05 11:17:07,660 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:07,666 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:07,700 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:07,702 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:07,762 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hinges' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.4 
2025-09-05 11:17:07,838 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.64 
2025-09-05 11:17:07,880 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.64 
2025-09-05 11:17:07,926 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:07,926 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:07,929 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:07,931 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:07,935 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:17:07,939 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:07,941 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:07,945 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:07,948 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:07,955 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:17:07,957 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:07,957 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:07,964 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:07,964 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:07,966 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hinges' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:17:07,971 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:07,973 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:07,988 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:07,989 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:07,997 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:17:08,000 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:08,001 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:08,002 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:08,003 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:08,005 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:17:08,009 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:17:08,011 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:17:08,048 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 11:17:08,049 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 50 
2025-09-05 11:17:08,049 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $221.871536 
2025-09-05 11:17:08,049 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 11:17:08,049 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 11:17:08,050 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 from 50 operations 
2025-09-05 11:17:08,051 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 
2025-09-05 11:17:08,094 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:17:08,102 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:08,103 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:08,106 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:08,106 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:08,108 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:08,108 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:08,110 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:08,111 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:08,114 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:08,115 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:08,116 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:08,116 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:08,117 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:08,118 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:08,119 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:08,119 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:08,120 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:08,121 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:08,125 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:08,126 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:08,127 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:10,197 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 11:17:10,198 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 11:17:10,198 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 11:17:10,198 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 11:17:10,198 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 11:17:11,162 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 11:17:11,162 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 11:17:11,163 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 11:17:11,163 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 11:17:11,164 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 11:17:11,164 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 11:17:11,165 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 11:17:11,165 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Key field values - hinge_pickup: 3_per_door_attached, hinge_deliver: 3_per_door_loose_drilled 
2025-09-05 11:17:11,197 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Receive Material Time' from field 'Frame Colour' - Cost: $5.6000000000000005 
2025-09-05 11:17:11,204 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Load / Unload Extrusion & Set Up' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:17:11,206 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC & Colour Consitency CommandeX Door Frame' from field 'Frame Colour' - Cost: $3.8919024624 
2025-09-05 11:17:11,209 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Cut CommandeX Door Frame' from field 'Frame Colour' - Cost: $15.42906316 
2025-09-05 11:17:11,210 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Cut Plugh for CommandeX Door Frame' from field 'Frame Colour' - Cost: $6.0834591888 
2025-09-05 11:17:11,213 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Centre Lock Cut Out' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:17:11,214 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Insert Plugh into CommandeX Door Frame' from field 'Frame Colour' - Cost: $16.058820840000003 
2025-09-05 11:17:11,216 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Assemble CommandeX Door with Crimped Corners' from field 'Frame Colour' - Cost: $33.364561886400004 
2025-09-05 11:17:11,217 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Plugh CommandeX Door with Assembly Press' from field 'Frame Colour' - Cost: $18.5526612528 
2025-09-05 11:17:11,224 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC CommandeX Door' from field 'Frame Colour' - Cost: $17.9229035728 
2025-09-05 11:17:11,226 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Cut Mesh or Sheet' from field 'Frame Colour' - Cost: $12.003181380800001 
2025-09-05 11:17:11,237 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Install Centre Lock' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $6.48 
2025-09-05 11:17:11,241 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Pick Centre Lock Site Kit' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $1.36 
2025-09-05 11:17:11,245 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC Picked Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:17:11,248 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Wrap Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:17:11,260 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Load Door (Delivery) / Customer Collect (Pick Up)' from field 'Number of Security Hinges (Pick Up)' - Cost: $2.6449822560000005 
2025-09-05 11:17:11,263 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Delivery Time - Time allocated to CD1 even for pick up orders' from field 'Number of Security Hinges (Pick Up)' - Cost: $5.6000000000000005 
2025-09-05 11:17:11,269 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:11,272 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:11,274 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:11,275 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:11,276 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:11,277 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:11,319 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Cut Extrusions on Upcut Saw' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:11,321 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up 2' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:11,323 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Extra Time to Cut French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:11,326 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Notch Slots for Centre Lock & French Door Mullion Caps in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:11,328 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Remove Breakaway Sections for Centre Notch & French Door Mullion Cap Notches in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:11,330 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert French Door Mullion Mohair (Schlegel PB48753B) into French Door Mullion or T-Section Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:11,332 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert Top & Bottom French Door Mullion Caps' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:11,334 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet French Door Mullion onto Lock Door or T-Section Mullion onto Non-Lock Door' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:11,342 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hardware' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $1.36 
2025-09-05 11:17:11,346 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:17:11,349 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:17:11,371 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Non-Lock Door Centre Striker Cut Out' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $1.36 
2025-09-05 11:17:11,374 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $2.64 
2025-09-05 11:17:11,379 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:17:11,379 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:17:11,386 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 11:17:11,387 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet Flush Bolts into Non-Lock Door' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $18.720000000000002 
2025-09-05 11:17:11,391 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:17:11,394 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:17:11,397 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:17:11,399 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:17:11,405 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:17:11,412 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:11,413 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:11,416 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:11,416 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:11,418 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $0.96 
2025-09-05 11:17:11,419 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:11,421 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:11,423 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:11,424 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:11,427 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $0.96 
2025-09-05 11:17:11,428 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:11,429 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:11,432 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:11,434 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:11,436 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Install Hinges' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $8.639999999999999 
2025-09-05 11:17:11,437 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:11,439 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:11,444 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:11,445 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:11,447 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hinges' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.4 
2025-09-05 11:17:11,451 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.64 
2025-09-05 11:17:11,455 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.64 
2025-09-05 11:17:11,461 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:11,461 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:11,463 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:11,464 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:11,465 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:17:11,466 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:11,467 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:11,468 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:11,468 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:11,469 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:17:11,472 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:11,474 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:11,476 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:11,477 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:11,479 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hinges' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:17:11,484 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:11,487 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:11,489 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:11,490 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:11,493 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:17:11,494 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:11,494 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:11,497 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:11,498 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:11,500 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:17:11,506 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:17:11,537 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:17:11,707 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 11:17:11,718 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 50 
2025-09-05 11:17:11,730 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $221.871536 
2025-09-05 11:17:11,736 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 11:17:11,736 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 11:17:11,737 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 from 50 operations 
2025-09-05 11:17:11,737 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 
2025-09-05 11:17:12,212 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:17:12,218 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:12,218 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:12,220 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:12,220 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:12,222 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:12,223 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:12,225 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:12,225 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:12,230 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:12,230 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:12,231 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:12,231 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:12,232 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:12,232 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:12,233 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:12,234 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:12,235 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:12,235 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:12,239 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:12,241 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:12,242 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:14,253 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 11:17:14,253 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 11:17:14,253 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 11:17:14,253 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 11:17:14,253 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 11:17:15,217 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 11:17:15,218 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 11:17:15,218 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 11:17:15,218 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 11:17:15,218 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 11:17:15,218 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 11:17:15,218 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 11:17:15,218 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Key field values - hinge_pickup: 3_per_door_attached, hinge_deliver: 3_per_door_loose_drilled 
2025-09-05 11:17:15,250 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Receive Material Time' from field 'Frame Colour' - Cost: $5.6000000000000005 
2025-09-05 11:17:15,252 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Load / Unload Extrusion & Set Up' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:17:15,253 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC & Colour Consitency CommandeX Door Frame' from field 'Frame Colour' - Cost: $3.8919024624 
2025-09-05 11:17:15,254 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Cut CommandeX Door Frame' from field 'Frame Colour' - Cost: $15.42906316 
2025-09-05 11:17:15,255 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Cut Plugh for CommandeX Door Frame' from field 'Frame Colour' - Cost: $6.0834591888 
2025-09-05 11:17:15,257 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Centre Lock Cut Out' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:17:15,258 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Insert Plugh into CommandeX Door Frame' from field 'Frame Colour' - Cost: $16.058820840000003 
2025-09-05 11:17:15,259 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Assemble CommandeX Door with Crimped Corners' from field 'Frame Colour' - Cost: $33.364561886400004 
2025-09-05 11:17:15,259 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Plugh CommandeX Door with Assembly Press' from field 'Frame Colour' - Cost: $18.5526612528 
2025-09-05 11:17:15,262 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC CommandeX Door' from field 'Frame Colour' - Cost: $17.9229035728 
2025-09-05 11:17:15,263 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Cut Mesh or Sheet' from field 'Frame Colour' - Cost: $12.003181380800001 
2025-09-05 11:17:15,273 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Install Centre Lock' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $6.48 
2025-09-05 11:17:15,275 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Pick Centre Lock Site Kit' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $1.36 
2025-09-05 11:17:15,278 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC Picked Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:17:15,280 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Wrap Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:17:15,293 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Load Door (Delivery) / Customer Collect (Pick Up)' from field 'Number of Security Hinges (Pick Up)' - Cost: $2.6449822560000005 
2025-09-05 11:17:15,295 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Delivery Time - Time allocated to CD1 even for pick up orders' from field 'Number of Security Hinges (Pick Up)' - Cost: $5.6000000000000005 
2025-09-05 11:17:15,300 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:15,301 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:15,302 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:15,303 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:15,305 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:15,305 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:15,332 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Cut Extrusions on Upcut Saw' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:15,332 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up 2' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:15,333 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Extra Time to Cut French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:15,334 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Notch Slots for Centre Lock & French Door Mullion Caps in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:15,335 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Remove Breakaway Sections for Centre Notch & French Door Mullion Cap Notches in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:15,336 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert French Door Mullion Mohair (Schlegel PB48753B) into French Door Mullion or T-Section Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:15,337 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert Top & Bottom French Door Mullion Caps' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:15,338 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet French Door Mullion onto Lock Door or T-Section Mullion onto Non-Lock Door' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:15,343 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hardware' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $1.36 
2025-09-05 11:17:15,346 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:17:15,352 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:17:15,375 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Non-Lock Door Centre Striker Cut Out' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $1.36 
2025-09-05 11:17:15,378 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $2.64 
2025-09-05 11:17:15,389 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:17:15,390 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:17:15,395 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 11:17:15,397 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet Flush Bolts into Non-Lock Door' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $18.720000000000002 
2025-09-05 11:17:15,399 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:17:15,401 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:17:15,405 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:17:15,407 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:17:15,410 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:17:15,418 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:15,419 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:15,425 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:15,426 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:15,428 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $0.96 
2025-09-05 11:17:15,429 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:15,430 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:15,432 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:15,432 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:15,436 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $0.96 
2025-09-05 11:17:15,438 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:15,439 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:15,443 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:15,443 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:15,446 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Install Hinges' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $8.639999999999999 
2025-09-05 11:17:15,447 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:15,448 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:15,450 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:15,451 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:15,452 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hinges' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.4 
2025-09-05 11:17:15,458 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.64 
2025-09-05 11:17:15,463 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.64 
2025-09-05 11:17:15,465 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:15,466 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:15,468 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:15,468 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:15,473 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:17:15,474 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:15,475 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:15,477 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:15,477 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:15,478 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:17:15,480 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:15,480 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:15,482 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:15,482 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:15,484 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hinges' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:17:15,486 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:15,487 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:15,489 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:15,489 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:15,491 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:17:15,492 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:15,493 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:15,494 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:15,495 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:15,496 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:17:15,499 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:17:15,501 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:17:15,523 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 11:17:15,523 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 50 
2025-09-05 11:17:15,523 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $221.871536 
2025-09-05 11:17:15,524 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 11:17:15,524 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 11:17:15,525 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 from 50 operations 
2025-09-05 11:17:15,526 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 
2025-09-05 11:17:15,793 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:17:15,807 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:15,807 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:15,808 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:15,809 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:15,810 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:15,810 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:15,811 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:15,812 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:15,814 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:15,815 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:15,816 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:15,816 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:15,817 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:15,817 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:15,818 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:15,819 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:15,820 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:17:15,820 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:15,825 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:15,825 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:15,826 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:18,923 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 11:17:18,924 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 11:17:18,924 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 11:17:18,924 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 11:17:18,925 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 11:17:19,234 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 11:17:19,234 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 11:17:19,234 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 11:17:19,234 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 11:17:19,234 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 11:17:19,234 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 11:17:19,234 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 11:17:19,234 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Key field values - hinge_pickup: 0, hinge_deliver: 3_per_door_loose_drilled 
2025-09-05 11:17:19,258 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Receive Material Time' from field 'Frame Colour' - Cost: $5.6000000000000005 
2025-09-05 11:17:19,261 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Load / Unload Extrusion & Set Up' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:17:19,262 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC & Colour Consitency CommandeX Door Frame' from field 'Frame Colour' - Cost: $3.8919024624 
2025-09-05 11:17:19,263 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Cut CommandeX Door Frame' from field 'Frame Colour' - Cost: $15.42906316 
2025-09-05 11:17:19,264 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Cut Plugh for CommandeX Door Frame' from field 'Frame Colour' - Cost: $6.0834591888 
2025-09-05 11:17:19,268 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Centre Lock Cut Out' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:17:19,270 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Insert Plugh into CommandeX Door Frame' from field 'Frame Colour' - Cost: $16.058820840000003 
2025-09-05 11:17:19,271 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Assemble CommandeX Door with Crimped Corners' from field 'Frame Colour' - Cost: $33.364561886400004 
2025-09-05 11:17:19,273 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Plugh CommandeX Door with Assembly Press' from field 'Frame Colour' - Cost: $18.5526612528 
2025-09-05 11:17:19,276 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC CommandeX Door' from field 'Frame Colour' - Cost: $17.9229035728 
2025-09-05 11:17:19,277 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Cut Mesh or Sheet' from field 'Frame Colour' - Cost: $12.003181380800001 
2025-09-05 11:17:19,287 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Install Centre Lock' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $6.48 
2025-09-05 11:17:19,289 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Pick Centre Lock Site Kit' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $1.36 
2025-09-05 11:17:19,291 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC Picked Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:17:19,293 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Wrap Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:17:19,306 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Load Door (Delivery) / Customer Collect (Pick Up)' from field 'Number of Security Hinges (Pick Up)' - Cost: $2.6449822560000005 
2025-09-05 11:17:19,308 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Delivery Time - Time allocated to CD1 even for pick up orders' from field 'Number of Security Hinges (Pick Up)' - Cost: $5.6000000000000005 
2025-09-05 11:17:19,314 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:19,316 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:19,317 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:19,318 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:19,319 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:19,320 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:19,356 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Cut Extrusions on Upcut Saw' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:19,356 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up 2' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:19,357 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Extra Time to Cut French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:19,358 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Notch Slots for Centre Lock & French Door Mullion Caps in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:19,359 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Remove Breakaway Sections for Centre Notch & French Door Mullion Cap Notches in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:19,360 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert French Door Mullion Mohair (Schlegel PB48753B) into French Door Mullion or T-Section Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:19,360 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert Top & Bottom French Door Mullion Caps' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:19,361 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet French Door Mullion onto Lock Door or T-Section Mullion onto Non-Lock Door' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:19,366 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hardware' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $1.36 
2025-09-05 11:17:19,368 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:17:19,369 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:17:19,391 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Non-Lock Door Centre Striker Cut Out' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $1.36 
2025-09-05 11:17:19,394 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $2.64 
2025-09-05 11:17:19,403 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:17:19,404 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:17:19,409 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 11:17:19,411 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet Flush Bolts into Non-Lock Door' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $18.720000000000002 
2025-09-05 11:17:19,414 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:17:19,416 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:17:19,419 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:17:19,422 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:17:19,424 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:17:19,430 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:19,431 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:19,433 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:19,434 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:19,435 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:17:19,436 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:19,436 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:19,437 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:19,438 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:19,439 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:17:19,440 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:19,440 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:19,442 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:19,442 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:19,444 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hinges' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:17:19,444 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:19,445 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:19,446 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:19,447 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:19,448 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:17:19,449 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:19,450 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:19,451 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:19,451 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:19,453 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:17:19,455 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:17:19,457 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:17:19,473 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 11:17:19,473 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 44 
2025-09-05 11:17:19,474 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $203.631536 
2025-09-05 11:17:19,474 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 11:17:19,474 19040 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 11:17:19,476 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $203.631536 from 44 operations 
2025-09-05 11:17:19,476 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $203.631536 
2025-09-05 11:17:20,186 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:17:20,317 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:20,321 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:20,352 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:20,358 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:20,376 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:20,376 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:20,389 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:20,390 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:20,413 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:20,413 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:20,465 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:20,471 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:20,478 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:25,960 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] Received parameters: ['template_id', 'product_id', 'order_line_id', 'config_data', 'configuration_price', 'configuration_price_matrix', 'total_price', 'field_3451', 'field_3452', 'field_3453', 'field_3454', 'field_3455', 'field_3456', 'field_3457', 'field_3458', 'field_3459', 'field_3460', 'field_3461', 'field_3462', 'field_3463', 'field_3464', 'field_3465', 'field_3466', 'field_3467', 'field_3468', 'field_3469', 'field_3470', 'field_3471', 'field_3472', 'field_3473', 'field_3474', 'field_3475', 'field_3476', 'field_3477', 'field_3478', 'field_3479', 'field_3480', 'field_3481', 'field_3482', 'field_3483', 'field_3484', 'field_3485', 'field_3486', 'field_3487', 'field_3488', 'field_3489', 'field_3490', 'field_3491', 'field_3492', 'field_3493', 'field_3494', 'field_3495', 'field_3496', 'field_3497', 'field_3498', 'field_3499', 'field_3500', 'field_3501', 'field_3502', 'field_3503', 'field_3504', 'field_3505', 'field_3506', 'field_3507', 'field_3508', 'field_3509', 'field_3510', 'field_3511', 'field_3512', 'field_3513', 'field_3514', 'field_3515', 'field_3516', 'field_3517', 'field_3518', 'field_3519', 'field_3520', 'field_3521', 'field_3522', 'field_3523', 'field_3524', 'field_3525', 'field_3526', 'field_3527', 'field_3528', 'field_3529', 'field_3530', 'field_3531', 'field_3532', 'field_3533', 'field_3534', 'field_3535', 'field_3536', 'field_3537', 'field_3538', 'field_3539', 'field_3540', 'field_3541', 'field_3542', 'field_3543', 'field_3544', 'field_3545', 'field_3546', 'field_3547', 'field_3548', 'field_3549', 'field_3550', 'field_3551', 'field_3552', 'field_3553', 'field_3554', 'field_3555', 'field_3556', 'field_3557', 'field_3558', 'field_3559', 'field_3560', 'field_3561', 'field_3562', 'field_3563', 'field_3564', 'field_3565', 'field_3566', 'field_3567', 'field_3568', 'field_3569', 'field_3570', 'field_3571', 'field_3572', 'field_3573', 'field_3574', 'field_3575', 'field_3576', 'field_3577', 'field_3578', 'field_3579', 'field_3580', 'field_3581', 'field_3582', 'field_3585', 'field_3586', 'field_3583', 'field_3584', 'field_3587', 'field_3588', 'field_3589', 'field_3590', 'field_3591', 'field_3592', 'field_3593', 'field_3594', 'field_3595', 'field_3596', 'field_3597', 'field_3598', 'field_3599', 'field_3600', 'field_3601', 'field_3602', 'field_3603', 'field_3604', 'field_3605', 'field_3606', 'field_3607', 'field_3608', 'field_3609', 'field_3610', 'field_3611', 'field_3612', 'field_3613', 'field_3614', 'field_3615', 'field_3616', 'field_3617', 'field_3618', 'field_3619', 'field_3620', 'field_3621', 'field_3622', 'field_3623', 'field_3624', 'field_3625', 'field_3626', 'field_3627', 'field_3628', 'field_3629', 'field_3630', 'field_3631', 'field_3632', 'field_3633', 'field_3634', 'field_3635', 'field_3636', 'field_3637', 'field_3638', 'field_3639', 'field_3640', 'field_3641', 'field_3642', 'field_3643', 'field_3644', 'field_3645', 'field_3646', 'field_3647', 'field_3648', 'field_3649', 'field_4140', 'field_3650', 'field_3651', 'field_3652', 'field_3653', 'field_3654', 'field_3655', 'field_3656', 'field_3657', 'field_3658', 'field_3661', 'field_3662', 'field_3663', 'field_3664', 'field_3665', 'field_3666', 'field_3667', 'field_3668', 'field_3669', 'field_3670', 'field_3671', 'field_3672', 'field_3673', 'field_3674', 'field_3675', 'field_3676', 'field_3677', 'field_3678', 'field_3679', 'field_3680'] 
2025-09-05 11:17:25,960 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] configuration_price_matrix in kw: True 
2025-09-05 11:17:25,960 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] configuration_price in kw: True 
2025-09-05 11:17:25,960 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] Extracted price_matrix: 717.74 
2025-09-05 11:17:25,960 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] Extracted price_component: 111.92540000000001 
2025-09-05 11:17:26,297 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:17:26,311 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:26,311 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:26,313 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:26,313 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:26,314 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:26,314 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:26,315 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:26,315 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:26,316 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:26,316 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:26,320 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:26,320 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:26,321 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:26,614 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Found quantity multiplier: 1.0 
2025-09-05 11:17:26,614 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Preview context - price matrix with multiplier: 717.74 * 1.0 = 717.74 
2025-09-05 11:17:26,615 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Component total (BOM lines already include multiplier): 111.92540000000001 
2025-09-05 11:17:26,615 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ===== MODEL OPERATION COST CALCULATION ===== 
2025-09-05 11:17:26,615 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Configuration ID: 74797 
2025-09-05 11:17:26,615 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 11:17:26,615 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Loaded 346 config values 
2025-09-05 11:17:26,615 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 11:17:26,615 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ===== MODEL FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 11:17:26,615 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Key field values - hinge_pickup: 0, hinge_deliver: 3_per_door_loose_drilled 
2025-09-05 11:17:26,621 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Receive Material Time' from field 'Frame Colour' - Cost: $5.6000000000000005 
2025-09-05 11:17:26,624 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'CNC Load / Unload Extrusion & Set Up' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:17:26,625 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'QC & Colour Consitency CommandeX Door Frame' from field 'Frame Colour' - Cost: $3.8919024624 
2025-09-05 11:17:26,627 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Cut CommandeX Door Frame' from field 'Frame Colour' - Cost: $15.42906316 
2025-09-05 11:17:26,628 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Cut Plugh for CommandeX Door Frame' from field 'Frame Colour' - Cost: $6.0834591888 
2025-09-05 11:17:26,630 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'CNC Centre Lock Cut Out' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:17:26,632 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Insert Plugh into CommandeX Door Frame' from field 'Frame Colour' - Cost: $16.058820840000003 
2025-09-05 11:17:26,633 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Assemble CommandeX Door with Crimped Corners' from field 'Frame Colour' - Cost: $33.364561886400004 
2025-09-05 11:17:26,634 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Plugh CommandeX Door with Assembly Press' from field 'Frame Colour' - Cost: $18.5526612528 
2025-09-05 11:17:26,637 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Clamp Product (even when midrail/brace is used) (per Panel)' from field 'Frame Colour' - Cost: $4.48 
2025-09-05 11:17:26,640 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'QC CommandeX Door' from field 'Frame Colour' - Cost: $17.9229035728 
2025-09-05 11:17:26,641 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Cut Mesh or Sheet' from field 'Frame Colour' - Cost: $12.003181380800001 
2025-09-05 11:17:26,649 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Cut Extrusions on Upcut Saw' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:26,650 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up 2' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:26,651 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Extra Time to Cut French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:26,652 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Notch Slots for Centre Lock & French Door Mullion Caps in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:26,652 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Remove Breakaway Sections for Centre Notch & French Door Mullion Cap Notches in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:26,653 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Insert French Door Mullion Mohair (Schlegel PB48753B) into French Door Mullion or T-Section Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:26,654 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Insert Top & Bottom French Door Mullion Caps' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:26,655 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Rivet French Door Mullion onto Lock Door or T-Section Mullion onto Non-Lock Door' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:26,658 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Pick Hardware' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $1.36 
2025-09-05 11:17:26,659 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'QC Picked Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:17:26,661 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Wrap Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:17:26,663 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Install Centre Lock' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $6.48 
2025-09-05 11:17:26,664 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Pick Centre Lock Site Kit' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $1.36 
2025-09-05 11:17:26,665 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'QC Picked Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:17:26,666 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Wrap Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:17:26,675 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Non-Lock Door Centre Striker Cut Out' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $1.36 
2025-09-05 11:17:26,677 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $2.64 
2025-09-05 11:17:26,679 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:17:26,679 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:17:26,684 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 11:17:26,685 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Rivet Flush Bolts into Non-Lock Door' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $18.720000000000002 
2025-09-05 11:17:26,688 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:17:26,690 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:17:26,691 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:17:26,693 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:17:26,694 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:17:26,697 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Extra Time to Cut Extra Mesh Sheet because of Midrail / XB / Mullion' from option 'Yes' in field 'Midrail (Conditional Case 2)' - Cost: $11.36 
2025-09-05 11:17:26,698 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Cut Extrusions on Upcut Saw (Midrail)' from option 'Yes' in field 'Midrail (Conditional Case 2)' - Cost: $4.0 
2025-09-05 11:17:26,699 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Cut Plugh for CommandeX Midrail' from option 'Yes' in field 'Midrail (Conditional Case 2)' - Cost: $2.72 
2025-09-05 11:17:26,701 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Insert Plugh into CommandeX Midrail' from option 'Yes' in field 'Midrail (Conditional Case 2)' - Cost: $3.3600000000000003 
2025-09-05 11:17:26,701 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Extra Time to Assemble Product with CommandeX Midrail' from option 'Yes' in field 'Midrail (Conditional Case 2)' - Cost: $12.64 
2025-09-05 11:17:26,703 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Plugh CommandeX Midrail by Hand' from option 'Yes' in field 'Midrail (Conditional Case 2)' - Cost: $10.56 
2025-09-05 11:17:26,704 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Load Door (Delivery) / Customer Collect (Pick Up)' from field 'Number of Security Hinges (Pick Up)' - Cost: $2.6449822560000005 
2025-09-05 11:17:26,705 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Delivery Time - Time allocated to CD1 even for pick up orders' from field 'Number of Security Hinges (Pick Up)' - Cost: $5.6000000000000005 
2025-09-05 11:17:26,706 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:26,706 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:26,707 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:26,707 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:26,707 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:17:26,708 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:26,708 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:26,709 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:26,709 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:26,710 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:17:26,710 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:26,711 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:26,711 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:26,712 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:26,712 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Pick Hinges' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:17:26,713 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:26,713 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:26,714 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:26,714 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:26,715 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'QC Picked Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:17:26,715 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:26,716 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:26,716 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:26,717 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:26,717 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Wrap Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:17:26,719 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:17:26,720 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:17:26,726 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:26,727 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:26,728 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:26,729 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:26,730 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:26,731 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:26,736 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ===== MODEL FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 11:17:26,736 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Operations found: 51 
2025-09-05 11:17:26,736 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Total cost: $252.75153600000004 
2025-09-05 11:17:26,736 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 11:17:26,736 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ✓ Model operation cost: $252.75153600000004 from 51 operations 
2025-09-05 11:17:26,736 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Operation total (already includes multiplier): 252.75153600000004 
2025-09-05 11:17:26,736 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Configuration 74797 price calculation: 
2025-09-05 11:17:26,736 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Components: $111.92540000000001 
2025-09-05 11:17:26,736 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Operations: $252.75153600000004 
2025-09-05 11:17:26,736 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Sale Price Matrices (with multiplier): $717.74 
2025-09-05 11:17:26,736 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Total: $1082.416936 
2025-09-05 11:17:26,736 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Quantity Multiplier: 1.0 
2025-09-05 11:17:26,736 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - BOM ID: 74767 
2025-09-05 11:17:26,736 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - BOM lines count: 14 
2025-09-05 11:17:27,116 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:17:27,138 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:27,138 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:27,140 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:27,141 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:27,142 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:27,142 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:27,144 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:27,144 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:27,146 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:27,146 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:27,150 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:27,151 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:27,152 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:27,513 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Found quantity multiplier: 1.0 
2025-09-05 11:17:27,513 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Saving configuration - using base price 717.74 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 11:17:27,517 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Component total (BOM lines already include multiplier): 111.92540000000001 
2025-09-05 11:17:27,517 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ===== MODEL OPERATION COST CALCULATION ===== 
2025-09-05 11:17:27,517 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Configuration ID: 74797 
2025-09-05 11:17:27,517 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 11:17:27,517 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Loaded 346 config values 
2025-09-05 11:17:27,517 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 11:17:27,517 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ===== MODEL FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 11:17:27,518 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Key field values - hinge_pickup: 0, hinge_deliver: 3_per_door_loose_drilled 
2025-09-05 11:17:27,522 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Receive Material Time' from field 'Frame Colour' - Cost: $5.6000000000000005 
2025-09-05 11:17:27,525 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'CNC Load / Unload Extrusion & Set Up' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:17:27,529 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'QC & Colour Consitency CommandeX Door Frame' from field 'Frame Colour' - Cost: $3.8919024624 
2025-09-05 11:17:27,530 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Cut CommandeX Door Frame' from field 'Frame Colour' - Cost: $15.42906316 
2025-09-05 11:17:27,531 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Cut Plugh for CommandeX Door Frame' from field 'Frame Colour' - Cost: $6.0834591888 
2025-09-05 11:17:27,533 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'CNC Centre Lock Cut Out' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:17:27,535 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Insert Plugh into CommandeX Door Frame' from field 'Frame Colour' - Cost: $16.058820840000003 
2025-09-05 11:17:27,536 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Assemble CommandeX Door with Crimped Corners' from field 'Frame Colour' - Cost: $33.364561886400004 
2025-09-05 11:17:27,537 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Plugh CommandeX Door with Assembly Press' from field 'Frame Colour' - Cost: $18.5526612528 
2025-09-05 11:17:27,540 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Clamp Product (even when midrail/brace is used) (per Panel)' from field 'Frame Colour' - Cost: $4.48 
2025-09-05 11:17:27,541 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'QC CommandeX Door' from field 'Frame Colour' - Cost: $17.9229035728 
2025-09-05 11:17:27,542 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Cut Mesh or Sheet' from field 'Frame Colour' - Cost: $12.003181380800001 
2025-09-05 11:17:27,548 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Cut Extrusions on Upcut Saw' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:27,549 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up 2' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:27,549 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Extra Time to Cut French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:27,550 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Notch Slots for Centre Lock & French Door Mullion Caps in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:27,550 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Remove Breakaway Sections for Centre Notch & French Door Mullion Cap Notches in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:27,551 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Insert French Door Mullion Mohair (Schlegel PB48753B) into French Door Mullion or T-Section Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:27,551 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Insert Top & Bottom French Door Mullion Caps' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:27,552 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Rivet French Door Mullion onto Lock Door or T-Section Mullion onto Non-Lock Door' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:17:27,555 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Pick Hardware' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $1.36 
2025-09-05 11:17:27,556 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'QC Picked Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:17:27,557 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Wrap Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:17:27,559 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Install Centre Lock' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $6.48 
2025-09-05 11:17:27,560 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Pick Centre Lock Site Kit' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $1.36 
2025-09-05 11:17:27,561 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'QC Picked Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:17:27,562 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Wrap Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:17:27,571 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Non-Lock Door Centre Striker Cut Out' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $1.36 
2025-09-05 11:17:27,572 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $2.64 
2025-09-05 11:17:27,574 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:17:27,574 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:17:27,578 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 11:17:27,579 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Rivet Flush Bolts into Non-Lock Door' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $18.720000000000002 
2025-09-05 11:17:27,580 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:17:27,581 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:17:27,582 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:17:27,583 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:17:27,584 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:17:27,587 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Extra Time to Cut Extra Mesh Sheet because of Midrail / XB / Mullion' from option 'Yes' in field 'Midrail (Conditional Case 2)' - Cost: $11.36 
2025-09-05 11:17:27,588 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Cut Extrusions on Upcut Saw (Midrail)' from option 'Yes' in field 'Midrail (Conditional Case 2)' - Cost: $4.0 
2025-09-05 11:17:27,589 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Cut Plugh for CommandeX Midrail' from option 'Yes' in field 'Midrail (Conditional Case 2)' - Cost: $2.72 
2025-09-05 11:17:27,589 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Insert Plugh into CommandeX Midrail' from option 'Yes' in field 'Midrail (Conditional Case 2)' - Cost: $3.3600000000000003 
2025-09-05 11:17:27,590 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Extra Time to Assemble Product with CommandeX Midrail' from option 'Yes' in field 'Midrail (Conditional Case 2)' - Cost: $12.64 
2025-09-05 11:17:27,591 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Plugh CommandeX Midrail by Hand' from option 'Yes' in field 'Midrail (Conditional Case 2)' - Cost: $10.56 
2025-09-05 11:17:27,593 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Load Door (Delivery) / Customer Collect (Pick Up)' from field 'Number of Security Hinges (Pick Up)' - Cost: $2.6449822560000005 
2025-09-05 11:17:27,594 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Delivery Time - Time allocated to CD1 even for pick up orders' from field 'Number of Security Hinges (Pick Up)' - Cost: $5.6000000000000005 
2025-09-05 11:17:27,594 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:27,595 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:27,595 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:27,596 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:27,596 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:17:27,597 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:27,597 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:27,598 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:27,598 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:27,599 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:17:27,599 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:27,599 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:27,600 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:27,600 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:27,601 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Pick Hinges' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:17:27,601 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:27,602 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:27,602 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:27,603 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:27,604 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'QC Picked Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:17:27,604 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:27,605 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:27,605 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:17:27,606 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:17:27,606 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Wrap Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:17:27,607 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:17:27,609 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:17:27,614 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:27,615 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:27,616 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:27,617 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:27,618 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:27,618 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:17:27,624 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ===== MODEL FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 11:17:27,624 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Operations found: 51 
2025-09-05 11:17:27,624 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Total cost: $252.75153600000004 
2025-09-05 11:17:27,624 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 11:17:27,624 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ✓ Model operation cost: $252.75153600000004 from 51 operations 
2025-09-05 11:17:27,624 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Operation total (already includes multiplier): 252.75153600000004 
2025-09-05 11:17:27,624 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Configuration 74797 price calculation: 
2025-09-05 11:17:27,624 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Components: $111.92540000000001 
2025-09-05 11:17:27,624 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Operations: $252.75153600000004 
2025-09-05 11:17:27,624 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Sale Price Matrices (with multiplier): $717.74 
2025-09-05 11:17:27,624 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Total: $1082.416936 
2025-09-05 11:17:27,624 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Quantity Multiplier: 1.0 
2025-09-05 11:17:27,625 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - BOM ID: 74767 
2025-09-05 11:17:27,625 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - BOM lines count: 14 
2025-09-05 11:17:27,625 19040 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [OPERATION_COSTS_BUG] MAIN.PY: Save config 2 got operation cost $252.75154 from model 
