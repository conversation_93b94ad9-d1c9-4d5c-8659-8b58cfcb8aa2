2025-09-05 09:15:20,533 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 09:15:20,533 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 09:15:20,533 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 09:15:20,533 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 09:15:20,534 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 09:15:21,146 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 09:15:21,147 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 09:15:21,147 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 09:15:21,147 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 09:15:21,148 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 09:15:21,148 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 09:15:21,149 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 09:15:21,287 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:21,292 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:21,296 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:21,299 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:21,302 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:21,305 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:21,459 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:15:21,482 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 09:15:21,531 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:21,533 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:21,538 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:21,539 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:21,546 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:21,549 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:21,551 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:21,552 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:21,556 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:21,561 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:21,565 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:21,566 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:21,570 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:21,571 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:21,577 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:21,578 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:21,593 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:21,595 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:21,600 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:21,603 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:21,607 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:21,608 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:21,611 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:21,612 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:21,620 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:21,621 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:21,624 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:21,625 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:21,632 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:21,633 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:21,638 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:21,640 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:21,644 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:21,645 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:21,647 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:21,649 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:21,698 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 09:15:21,699 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 41 
2025-09-05 09:15:21,700 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $95.92 
2025-09-05 09:15:21,700 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 09:15:21,700 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 09:15:21,700 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 from 41 operations 
2025-09-05 09:15:21,700 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 
2025-09-05 09:15:22,615 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:15:22,625 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:22,625 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:22,627 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:22,627 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:22,628 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:22,628 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:22,631 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:22,631 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:22,636 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:22,637 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:22,638 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:22,638 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:22,639 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:22,640 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:22,641 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:22,641 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:22,642 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:22,643 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:22,649 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:22,650 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:22,651 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:26,500 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 09:15:26,500 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 09:15:26,501 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 09:15:26,501 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 09:15:26,504 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 09:15:27,334 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 09:15:27,335 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 09:15:27,335 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 09:15:27,335 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 09:15:27,335 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 09:15:27,335 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 09:15:27,336 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 09:15:27,455 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:27,457 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:27,459 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:27,460 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:27,463 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:27,466 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:27,570 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:15:27,581 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 09:15:27,611 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:27,612 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:27,614 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:27,616 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:27,620 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:27,621 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:27,622 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:27,623 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:27,626 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:27,627 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:27,629 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:27,630 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:27,634 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:27,635 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:27,638 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:27,638 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:27,649 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:27,650 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:27,651 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:27,652 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:27,656 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:27,656 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:27,658 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:27,659 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:27,661 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:27,661 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:27,662 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:27,663 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:27,666 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:27,667 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:27,669 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:27,670 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:27,672 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:27,673 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:27,674 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:27,675 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:27,706 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 09:15:27,706 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 41 
2025-09-05 09:15:27,706 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $95.92 
2025-09-05 09:15:27,706 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 09:15:27,706 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 09:15:27,706 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 from 41 operations 
2025-09-05 09:15:27,709 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 
2025-09-05 09:15:28,175 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:15:28,182 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:28,182 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:28,184 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:28,185 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:28,186 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:28,186 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:28,189 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:28,189 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:28,194 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:28,195 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:28,196 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:28,196 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:28,198 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:28,198 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:28,200 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:28,201 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:28,202 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:28,202 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:28,206 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:28,207 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:28,208 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:46,016 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 09:15:46,021 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 09:15:46,022 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 09:15:46,022 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 09:15:46,029 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 09:15:47,332 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 09:15:47,333 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 09:15:47,334 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 09:15:47,334 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 09:15:47,334 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 09:15:47,335 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 09:15:47,335 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 09:15:47,498 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:47,503 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:47,507 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:47,509 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:47,515 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:47,520 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:47,702 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:15:47,720 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 09:15:47,763 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:47,764 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:47,769 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:47,771 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:47,775 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:47,776 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:47,777 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:47,778 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:47,781 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:47,781 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:47,784 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:47,787 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:47,791 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:47,792 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:47,794 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:47,796 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:47,811 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:47,811 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:47,813 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:47,814 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:47,817 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:47,817 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:47,819 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:47,821 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:47,824 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:47,824 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:47,826 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:47,826 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:47,831 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:47,831 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:47,833 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:47,833 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:47,838 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:47,838 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:47,840 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:47,841 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:47,911 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 09:15:47,912 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 50 
2025-09-05 09:15:47,912 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $221.871536 
2025-09-05 09:15:47,912 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 09:15:47,912 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 09:15:47,913 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 from 50 operations 
2025-09-05 09:15:47,913 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 
2025-09-05 09:15:48,602 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:15:48,720 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:48,721 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:48,742 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:48,748 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:48,773 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:48,774 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:48,779 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:48,780 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:48,793 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:48,795 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:48,798 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:48,800 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:48,804 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:48,806 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:48,815 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:48,822 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:48,836 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:48,842 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:48,854 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:48,856 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:48,858 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:55,285 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 09:15:55,287 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 09:15:55,287 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 09:15:55,287 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 09:15:55,289 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 09:15:56,002 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 09:15:56,003 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 09:15:56,003 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 09:15:56,004 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 09:15:56,004 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 09:15:56,004 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 09:15:56,005 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 09:15:56,112 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:56,115 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:56,118 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:56,119 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:56,121 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:56,123 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:56,295 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:15:56,306 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 09:15:56,345 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:56,346 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:56,349 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:56,349 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:56,352 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:56,352 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:56,353 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:56,354 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:56,358 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:56,358 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:56,361 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:56,362 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:56,375 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:56,375 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:56,380 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:56,384 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:56,396 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:56,397 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:56,400 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:56,401 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:56,405 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:56,410 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:56,414 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:56,415 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:56,418 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:56,418 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:56,420 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:56,421 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:56,427 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:56,428 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:56,430 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:56,431 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:56,445 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:56,447 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:56,450 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:56,451 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:56,506 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 09:15:56,507 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 50 
2025-09-05 09:15:56,508 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $221.871536 
2025-09-05 09:15:56,508 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 09:15:56,508 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 09:15:56,508 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 from 50 operations 
2025-09-05 09:15:56,508 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 
2025-09-05 09:15:57,719 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:15:57,756 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:57,757 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:57,762 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:57,763 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:57,767 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:57,768 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:57,773 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:57,774 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:57,785 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:57,786 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:57,801 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:57,807 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:57,826 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:57,832 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:57,852 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:57,858 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:57,888 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:15:57,889 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:15:57,940 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:57,962 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:15:57,977 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:16:00,542 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 09:16:00,543 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 09:16:00,543 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 09:16:00,543 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 09:16:00,545 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 09:16:00,970 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 09:16:00,970 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 09:16:00,970 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 09:16:00,970 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 09:16:00,970 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 09:16:00,970 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 09:16:00,970 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 09:16:01,072 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:16:01,074 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:16:01,077 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:16:01,079 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:16:01,082 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:16:01,084 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:16:01,214 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:16:01,222 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 09:16:01,252 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:01,255 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:01,259 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:01,259 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:01,263 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:01,263 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:01,267 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:01,268 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:01,271 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:01,271 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:01,274 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:01,274 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:01,277 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:01,277 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:01,279 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:01,280 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:01,283 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:01,283 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:01,285 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:01,286 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:01,330 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 09:16:01,331 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 44 
2025-09-05 09:16:01,331 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $203.631536 
2025-09-05 09:16:01,331 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 09:16:01,331 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 09:16:01,332 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $203.631536 from 44 operations 
2025-09-05 09:16:01,332 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $203.631536 
2025-09-05 09:16:03,086 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:16:03,586 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:03,587 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:03,593 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:03,595 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:03,598 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:03,599 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:03,620 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:03,622 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:03,625 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:03,627 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:03,639 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:16:03,643 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:16:03,661 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:16:05,743 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 09:16:05,744 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 09:16:05,744 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 09:16:05,744 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 09:16:05,745 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 09:16:06,542 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 09:16:06,542 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 09:16:06,542 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 09:16:06,542 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 09:16:06,543 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 09:16:06,543 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 09:16:06,543 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 09:16:06,700 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:16:06,704 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:16:06,722 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:16:06,725 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:16:06,729 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:16:06,731 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:16:06,960 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:16:06,970 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 09:16:07,011 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:07,011 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:07,015 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:07,015 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:07,020 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:07,022 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:07,024 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:07,025 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:07,028 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:07,029 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:07,032 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:07,033 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:07,037 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:07,038 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:07,041 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:07,042 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:07,048 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:07,050 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:07,055 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:07,056 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:07,125 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 09:16:07,127 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 44 
2025-09-05 09:16:07,127 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $203.631536 
2025-09-05 09:16:07,128 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 09:16:07,128 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 09:16:07,129 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $203.631536 from 44 operations 
2025-09-05 09:16:07,130 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $203.631536 
2025-09-05 09:16:08,048 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:16:08,147 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:08,148 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:08,150 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:08,151 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:08,153 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:08,153 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:08,156 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:08,156 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:08,158 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:08,158 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:08,162 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:16:08,164 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:16:08,165 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:16:34,007 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] Received parameters: ['template_id', 'product_id', 'order_line_id', 'config_data', 'configuration_price', 'configuration_price_matrix', 'total_price', 'field_3451', 'field_3452', 'field_3453', 'field_3454', 'field_3455', 'field_3456', 'field_3457', 'field_3458', 'field_3459', 'field_3460', 'field_3461', 'field_3462', 'field_3463', 'field_3464', 'field_3465', 'field_3466', 'field_3467', 'field_3468', 'field_3469', 'field_3470', 'field_3471', 'field_3472', 'field_3473', 'field_3474', 'field_3475', 'field_3476', 'field_3477', 'field_3478', 'field_3479', 'field_3480', 'field_3481', 'field_3482', 'field_3483', 'field_3484', 'field_3485', 'field_3486', 'field_3487', 'field_3488', 'field_3489', 'field_3490', 'field_3491', 'field_3492', 'field_3493', 'field_3494', 'field_3495', 'field_3496', 'field_3497', 'field_3498', 'field_3499', 'field_3500', 'field_3501', 'field_3502', 'field_3503', 'field_3504', 'field_3505', 'field_3506', 'field_3507', 'field_3508', 'field_3509', 'field_3510', 'field_3511', 'field_3512', 'field_3513', 'field_3514', 'field_3515', 'field_3516', 'field_3517', 'field_3518', 'field_3519', 'field_3520', 'field_3521', 'field_3522', 'field_3523', 'field_3524', 'field_3525', 'field_3526', 'field_3527', 'field_3528', 'field_3529', 'field_3530', 'field_3531', 'field_3532', 'field_3533', 'field_3534', 'field_3535', 'field_3536', 'field_3537', 'field_3538', 'field_3539', 'field_3540', 'field_3541', 'field_3542', 'field_3543', 'field_3544', 'field_3545', 'field_3546', 'field_3547', 'field_3548', 'field_3549', 'field_3550', 'field_3551', 'field_3552', 'field_3553', 'field_3554', 'field_3555', 'field_3556', 'field_3557', 'field_3558', 'field_3559', 'field_3560', 'field_3561', 'field_3562', 'field_3563', 'field_3564', 'field_3565', 'field_3566', 'field_3567', 'field_3568', 'field_3569', 'field_3570', 'field_3571', 'field_3572', 'field_3573', 'field_3574', 'field_3575', 'field_3576', 'field_3577', 'field_3578', 'field_3579', 'field_3580', 'field_3581', 'field_3582', 'field_3585', 'field_3586', 'field_3583', 'field_3584', 'field_3587', 'field_3588', 'field_3589', 'field_3590', 'field_3591', 'field_3592', 'field_3593', 'field_3594', 'field_3595', 'field_3596', 'field_3597', 'field_3598', 'field_3599', 'field_3600', 'field_3601', 'field_3602', 'field_3603', 'field_3604', 'field_3605', 'field_3606', 'field_3607', 'field_3608', 'field_3609', 'field_3610', 'field_3611', 'field_3612', 'field_3613', 'field_3614', 'field_3615', 'field_3616', 'field_3617', 'field_3618', 'field_3619', 'field_3620', 'field_3621', 'field_3622', 'field_3623', 'field_3624', 'field_3625', 'field_3626', 'field_3627', 'field_3628', 'field_3629', 'field_3630', 'field_3631', 'field_3632', 'field_3633', 'field_3634', 'field_3635', 'field_3636', 'field_3637', 'field_3638', 'field_3639', 'field_3640', 'field_3641', 'field_3642', 'field_3643', 'field_3644', 'field_3645', 'field_3646', 'field_3647', 'field_3648', 'field_3649', 'field_4140', 'field_3650', 'field_3651', 'field_3652', 'field_3653', 'field_3654', 'field_3655', 'field_3656', 'field_3657', 'field_3658', 'field_3661', 'field_3662', 'field_3663', 'field_3664', 'field_3665', 'field_3666', 'field_3667', 'field_3668', 'field_3669', 'field_3670', 'field_3671', 'field_3672', 'field_3673', 'field_3674', 'field_3675', 'field_3676', 'field_3677', 'field_3678', 'field_3679', 'field_3680'] 
2025-09-05 09:16:34,008 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] configuration_price_matrix in kw: True 
2025-09-05 09:16:34,008 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] configuration_price in kw: True 
2025-09-05 09:16:34,008 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] Extracted price_matrix: 717.74 
2025-09-05 09:16:34,008 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] Extracted price_component: 111.92540000000001 
2025-09-05 09:16:34,621 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:16:34,637 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:34,637 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:34,639 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:34,640 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:34,640 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:34,641 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:34,643 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:34,643 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:34,645 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:34,645 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:34,649 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:16:34,650 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:16:34,651 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:16:35,350 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Found quantity multiplier: 1.0 
2025-09-05 09:16:35,350 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Preview context - price matrix with multiplier: 717.74 * 1.0 = 717.74 
2025-09-05 09:16:35,351 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Component total (BOM lines already include multiplier): 111.92540000000001 
2025-09-05 09:16:35,351 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ===== MODEL OPERATION COST CALCULATION ===== 
2025-09-05 09:16:35,351 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Configuration ID: 74708 
2025-09-05 09:16:35,351 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 09:16:35,351 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Loaded 346 config values 
2025-09-05 09:16:35,351 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 09:16:35,353 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ===== MODEL FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 09:16:35,353 255860 ERROR canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Error in model field/option mapping calculation: 'config.matrix.option' object has no attribute 'technical_name' 
2025-09-05 09:16:35,407 255860 ERROR canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Traceback: Traceback (most recent call last):
2025-09-05 09:16:35,408 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ✗ Model operation calculation failed: 'config.matrix.option' object has no attribute 'technical_name' 
2025-09-05 09:16:35,408 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Operation total (already includes multiplier): 0.0 
2025-09-05 09:16:35,408 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Configuration 74708 price calculation: 
2025-09-05 09:16:35,408 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Components: $111.92540000000001 
2025-09-05 09:16:35,408 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Operations: $0.0 
2025-09-05 09:16:35,408 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Sale Price Matrices (with multiplier): $717.74 
2025-09-05 09:16:35,408 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Total: $829.6654 
2025-09-05 09:16:35,408 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Quantity Multiplier: 1.0 
2025-09-05 09:16:35,408 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - BOM ID: 74678 
2025-09-05 09:16:35,408 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - BOM lines count: 14 
2025-09-05 09:16:37,897 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:16:37,922 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:37,923 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:37,926 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:37,926 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:37,928 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:37,929 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:37,932 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:37,933 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:37,935 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:16:37,935 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:16:37,942 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:16:37,944 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:16:37,946 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:16:38,376 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Found quantity multiplier: 1.0 
2025-09-05 09:16:38,376 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Saving configuration - using base price 717.74 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 09:16:38,381 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Component total (BOM lines already include multiplier): 111.92540000000001 
2025-09-05 09:16:38,381 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ===== MODEL OPERATION COST CALCULATION ===== 
2025-09-05 09:16:38,381 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Configuration ID: 74708 
2025-09-05 09:16:38,381 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 09:16:38,381 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Loaded 346 config values 
2025-09-05 09:16:38,381 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 09:16:38,381 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ===== MODEL FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 09:16:38,381 255860 ERROR canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Error in model field/option mapping calculation: 'config.matrix.option' object has no attribute 'technical_name' 
2025-09-05 09:16:38,382 255860 ERROR canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Traceback: Traceback (most recent call last):
2025-09-05 09:16:38,382 255860 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ✗ Model operation calculation failed: 'config.matrix.option' object has no attribute 'technical_name' 
2025-09-05 09:16:38,382 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Operation total (already includes multiplier): 0.0 
2025-09-05 09:16:38,382 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Configuration 74708 price calculation: 
2025-09-05 09:16:38,382 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Components: $111.92540000000001 
2025-09-05 09:16:38,382 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Operations: $0.0 
2025-09-05 09:16:38,382 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Sale Price Matrices (with multiplier): $717.74 
2025-09-05 09:16:38,382 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Total: $829.6654 
2025-09-05 09:16:38,382 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Quantity Multiplier: 1.0 
2025-09-05 09:16:38,382 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - BOM ID: 74678 
2025-09-05 09:16:38,382 255860 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - BOM lines count: 14 
2025-09-05 09:19:12,335 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 09:19:12,335 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 09:19:12,336 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 09:19:12,336 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 09:19:12,339 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 09:19:12,832 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 09:19:12,832 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 09:19:12,832 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 09:19:12,832 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 09:19:12,832 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 09:19:12,832 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 09:19:12,832 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 09:19:13,040 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:13,045 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:13,049 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:13,052 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:13,056 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:13,058 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:13,174 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:19:13,201 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:13,202 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:13,208 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:13,210 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:13,230 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:13,232 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:13,238 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:13,240 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:13,257 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:13,259 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:13,263 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:13,264 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:19:13,265 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:13,272 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:13,273 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:13,275 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:13,276 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:13,278 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:13,279 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:13,280 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 09:19:13,299 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:13,302 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:13,304 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:13,327 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:13,328 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:13,332 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:13,332 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:13,336 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:13,337 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:13,343 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:13,345 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:13,348 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:13,349 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:13,351 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:13,353 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:13,359 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:13,359 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:13,362 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:13,363 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:13,384 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:13,385 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:13,387 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:13,388 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:13,395 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:13,398 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:13,401 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:13,403 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:13,407 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:13,408 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:13,410 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:13,410 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:13,418 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:13,418 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:13,422 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:13,423 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:13,427 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:13,428 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:13,431 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:13,435 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:13,490 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 09:19:13,491 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 41 
2025-09-05 09:19:13,492 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $95.92 
2025-09-05 09:19:13,492 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 09:19:13,492 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 09:19:13,493 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 from 41 operations 
2025-09-05 09:19:13,493 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 
2025-09-05 09:19:15,310 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:19:15,320 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:15,320 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:15,323 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:15,323 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:15,325 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:15,325 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:15,327 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:15,327 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:15,331 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:15,331 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:15,333 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:15,333 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:15,334 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:15,335 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:15,336 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:15,336 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:15,338 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:15,338 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:15,344 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:15,346 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:15,348 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:22,914 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 09:19:22,914 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 09:19:22,914 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 09:19:22,914 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 09:19:22,916 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 09:19:23,750 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 09:19:23,751 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 09:19:23,751 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 09:19:23,751 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 09:19:23,751 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 09:19:23,751 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 09:19:23,752 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 09:19:23,914 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:23,920 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:23,926 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:23,929 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:23,934 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:23,937 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:24,111 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:19:24,126 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 09:19:24,169 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:24,172 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:24,175 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:24,177 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:24,184 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:24,185 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:24,188 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:24,188 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:24,192 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:24,193 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:24,195 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:24,196 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:24,201 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:24,201 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:24,204 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:24,205 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:24,222 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:24,223 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:24,225 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:24,226 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:24,229 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:24,230 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:24,232 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:24,232 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:24,236 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:24,237 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:24,238 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:24,239 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:24,244 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:24,246 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:24,248 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:24,249 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:24,251 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:24,252 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:24,255 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:24,256 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:24,300 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 09:19:24,304 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 41 
2025-09-05 09:19:24,304 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $95.92 
2025-09-05 09:19:24,304 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 09:19:24,316 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 09:19:24,316 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 from 41 operations 
2025-09-05 09:19:24,316 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 
2025-09-05 09:19:24,505 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:19:24,522 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:24,523 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:24,528 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:24,529 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:24,532 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:24,532 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:24,535 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:24,536 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:24,543 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:24,543 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:24,545 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:24,546 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:24,548 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:24,549 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:24,551 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:24,552 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:24,555 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:24,556 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:24,562 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:24,564 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:24,565 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:31,501 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 09:19:31,501 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 09:19:31,501 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 09:19:31,501 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 09:19:31,502 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 09:19:32,170 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 09:19:32,170 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 09:19:32,171 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 09:19:32,172 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 09:19:32,180 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 09:19:32,182 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 09:19:32,183 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 09:19:32,317 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:32,322 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:32,325 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:32,326 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:32,329 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:32,331 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:32,470 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:19:32,477 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 09:19:32,509 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:32,511 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:32,515 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:32,517 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:32,520 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:32,521 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:32,524 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:32,526 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:32,529 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:32,530 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:32,534 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:32,536 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:32,538 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:32,540 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:32,544 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:32,544 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:32,560 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:32,561 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:32,563 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:32,565 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:32,570 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:32,571 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:32,573 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:32,576 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:32,581 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:32,582 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:32,585 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:32,586 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:32,591 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:32,592 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:32,593 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:32,595 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:32,599 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:32,600 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:32,602 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:32,603 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:32,656 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 09:19:32,662 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 41 
2025-09-05 09:19:32,663 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $95.92 
2025-09-05 09:19:32,674 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 09:19:32,675 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 09:19:32,675 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 from 41 operations 
2025-09-05 09:19:32,676 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 
2025-09-05 09:19:32,854 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:19:32,866 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:32,867 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:32,869 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:32,869 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:32,871 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:32,871 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:32,873 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:32,874 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:32,878 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:32,879 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:32,880 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:32,881 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:32,882 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:32,882 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:32,884 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:32,884 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:32,886 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:32,886 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:32,892 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:32,894 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:32,895 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:50,457 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 09:19:50,457 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 09:19:50,457 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 09:19:50,457 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 09:19:50,458 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 09:19:51,240 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 09:19:51,240 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 09:19:51,241 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 09:19:51,241 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 09:19:51,241 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 09:19:51,241 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 09:19:51,242 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 09:19:51,364 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:51,366 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:51,369 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:51,372 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:51,375 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:51,378 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:51,511 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:19:51,519 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 09:19:51,549 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:51,550 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:51,554 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:51,555 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:51,559 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:51,560 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:51,562 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:51,562 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:51,565 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:51,566 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:51,569 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:51,569 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:51,573 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:51,573 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:51,576 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:51,577 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:51,589 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:51,590 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:51,591 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:51,592 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:51,595 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:51,595 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:51,596 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:51,597 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:51,600 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:51,601 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:51,603 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:51,604 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:51,607 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:51,608 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:51,609 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:51,610 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:51,613 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:51,613 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:51,615 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:51,617 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:51,680 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 09:19:51,689 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 50 
2025-09-05 09:19:51,689 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $221.871536 
2025-09-05 09:19:51,689 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 09:19:51,689 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 09:19:51,690 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 from 50 operations 
2025-09-05 09:19:51,690 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 
2025-09-05 09:19:51,887 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:19:51,908 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:51,909 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:51,919 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:51,920 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:51,922 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:51,923 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:51,926 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:51,927 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:51,940 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:51,940 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:51,942 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:51,943 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:51,945 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:51,946 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:51,949 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:51,949 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:51,953 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:51,955 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:51,963 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:51,965 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:51,967 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:56,421 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 09:19:56,421 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 09:19:56,422 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 09:19:56,424 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 09:19:56,425 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 09:19:57,749 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 09:19:57,750 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 09:19:57,751 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 09:19:57,752 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 09:19:57,753 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 09:19:57,758 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 09:19:57,770 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 09:19:59,093 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:59,106 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:59,125 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:59,129 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:59,140 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:59,148 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:19:59,786 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:19:59,817 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 09:19:59,873 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:59,878 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:59,883 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:59,884 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:59,895 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:59,897 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:59,901 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:59,918 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:59,924 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:59,926 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:59,939 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:59,941 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:59,950 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:59,951 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:19:59,965 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:19:59,967 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:00,007 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:00,021 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:00,049 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:00,062 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:00,116 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:00,117 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:00,120 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:00,121 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:00,137 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:00,138 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:00,149 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:00,151 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:00,161 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:00,163 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:00,167 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:00,168 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:00,176 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:00,184 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:00,188 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:00,189 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:00,350 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 09:20:00,357 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 50 
2025-09-05 09:20:00,357 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $221.871536 
2025-09-05 09:20:00,361 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 09:20:00,361 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 09:20:00,362 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 from 50 operations 
2025-09-05 09:20:00,362 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 
2025-09-05 09:20:00,901 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:20:00,926 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:00,926 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:00,931 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:00,931 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:00,933 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:00,934 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:00,938 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:00,938 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:00,952 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:00,952 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:00,954 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:00,955 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:00,967 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:00,968 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:00,970 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:00,970 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:00,973 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:00,975 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:00,984 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:00,987 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:00,990 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:02,821 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 09:20:02,824 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 09:20:02,826 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 09:20:02,828 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 09:20:02,835 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 09:20:05,284 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 09:20:05,285 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 09:20:05,288 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 09:20:05,289 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 09:20:05,289 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 09:20:05,289 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 09:20:05,289 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 09:20:05,468 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:05,474 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:05,479 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:05,481 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:05,486 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:05,488 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:05,679 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:20:05,695 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 09:20:05,739 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:05,740 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:05,746 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:05,747 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:05,750 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:05,751 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:05,754 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:05,756 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:05,761 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:05,762 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:05,767 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:05,767 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:05,770 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:05,771 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:05,775 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:05,776 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:05,798 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:05,801 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:05,808 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:05,814 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:05,825 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:05,833 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:05,840 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:05,844 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:05,850 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:05,851 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:05,852 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:05,854 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:05,864 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:05,864 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:05,867 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:05,867 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:05,873 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:05,875 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:05,879 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:05,881 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:05,958 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 09:20:05,960 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 50 
2025-09-05 09:20:05,960 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $221.871536 
2025-09-05 09:20:05,960 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 09:20:05,961 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 09:20:05,962 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 from 50 operations 
2025-09-05 09:20:05,963 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 
2025-09-05 09:20:07,130 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:20:07,423 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:07,425 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:07,430 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:07,432 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:07,436 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:07,437 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:07,442 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:07,443 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:07,459 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:07,461 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:07,469 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:07,486 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:07,502 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:07,503 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:07,507 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:07,509 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:07,515 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 09:20:07,516 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:07,529 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:07,532 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:07,535 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:11,258 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 09:20:11,258 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 09:20:11,258 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 09:20:11,260 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 09:20:11,262 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 09:20:12,638 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 09:20:12,639 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 09:20:12,640 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 09:20:12,642 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 09:20:12,643 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 09:20:12,645 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 09:20:12,647 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 09:20:13,147 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:13,151 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:13,156 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:13,159 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:13,162 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:13,165 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:13,310 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:20:13,319 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 09:20:13,355 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:13,356 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:13,362 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:13,363 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:13,367 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:13,368 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:13,370 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:13,370 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:13,374 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:13,375 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:13,379 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:13,379 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:13,383 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:13,384 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:13,386 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:13,387 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:13,391 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:13,392 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:13,394 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:13,395 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:13,445 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 09:20:13,446 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 44 
2025-09-05 09:20:13,446 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $203.631536 
2025-09-05 09:20:13,446 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 09:20:13,446 260897 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 09:20:13,447 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $203.631536 from 44 operations 
2025-09-05 09:20:13,447 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $203.631536 
2025-09-05 09:20:14,793 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:20:15,058 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:15,060 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:15,066 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:15,066 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:15,069 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:15,070 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:15,079 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:15,080 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:15,083 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:15,084 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:15,100 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:15,107 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:15,120 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:23,031 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] Received parameters: ['template_id', 'product_id', 'order_line_id', 'config_data', 'configuration_price', 'configuration_price_matrix', 'total_price', 'field_3451', 'field_3452', 'field_3453', 'field_3454', 'field_3455', 'field_3456', 'field_3457', 'field_3458', 'field_3459', 'field_3460', 'field_3461', 'field_3462', 'field_3463', 'field_3464', 'field_3465', 'field_3466', 'field_3467', 'field_3468', 'field_3469', 'field_3470', 'field_3471', 'field_3472', 'field_3473', 'field_3474', 'field_3475', 'field_3476', 'field_3477', 'field_3478', 'field_3479', 'field_3480', 'field_3481', 'field_3482', 'field_3483', 'field_3484', 'field_3485', 'field_3486', 'field_3487', 'field_3488', 'field_3489', 'field_3490', 'field_3491', 'field_3492', 'field_3493', 'field_3494', 'field_3495', 'field_3496', 'field_3497', 'field_3498', 'field_3499', 'field_3500', 'field_3501', 'field_3502', 'field_3503', 'field_3504', 'field_3505', 'field_3506', 'field_3507', 'field_3508', 'field_3509', 'field_3510', 'field_3511', 'field_3512', 'field_3513', 'field_3514', 'field_3515', 'field_3516', 'field_3517', 'field_3518', 'field_3519', 'field_3520', 'field_3521', 'field_3522', 'field_3523', 'field_3524', 'field_3525', 'field_3526', 'field_3527', 'field_3528', 'field_3529', 'field_3530', 'field_3531', 'field_3532', 'field_3533', 'field_3534', 'field_3535', 'field_3536', 'field_3537', 'field_3538', 'field_3539', 'field_3540', 'field_3541', 'field_3542', 'field_3543', 'field_3544', 'field_3545', 'field_3546', 'field_3547', 'field_3548', 'field_3549', 'field_3550', 'field_3551', 'field_3552', 'field_3553', 'field_3554', 'field_3555', 'field_3556', 'field_3557', 'field_3558', 'field_3559', 'field_3560', 'field_3561', 'field_3562', 'field_3563', 'field_3564', 'field_3565', 'field_3566', 'field_3567', 'field_3568', 'field_3569', 'field_3570', 'field_3571', 'field_3572', 'field_3573', 'field_3574', 'field_3575', 'field_3576', 'field_3577', 'field_3578', 'field_3579', 'field_3580', 'field_3581', 'field_3582', 'field_3585', 'field_3586', 'field_3583', 'field_3584', 'field_3587', 'field_3588', 'field_3589', 'field_3590', 'field_3591', 'field_3592', 'field_3593', 'field_3594', 'field_3595', 'field_3596', 'field_3597', 'field_3598', 'field_3599', 'field_3600', 'field_3601', 'field_3602', 'field_3603', 'field_3604', 'field_3605', 'field_3606', 'field_3607', 'field_3608', 'field_3609', 'field_3610', 'field_3611', 'field_3612', 'field_3613', 'field_3614', 'field_3615', 'field_3616', 'field_3617', 'field_3618', 'field_3619', 'field_3620', 'field_3621', 'field_3622', 'field_3623', 'field_3624', 'field_3625', 'field_3626', 'field_3627', 'field_3628', 'field_3629', 'field_3630', 'field_3631', 'field_3632', 'field_3633', 'field_3634', 'field_3635', 'field_3636', 'field_3637', 'field_3638', 'field_3639', 'field_3640', 'field_3641', 'field_3642', 'field_3643', 'field_3644', 'field_3645', 'field_3646', 'field_3647', 'field_3648', 'field_3649', 'field_4140', 'field_3650', 'field_3651', 'field_3652', 'field_3653', 'field_3654', 'field_3655', 'field_3656', 'field_3657', 'field_3658', 'field_3661', 'field_3662', 'field_3663', 'field_3664', 'field_3665', 'field_3666', 'field_3667', 'field_3668', 'field_3669', 'field_3670', 'field_3671', 'field_3672', 'field_3673', 'field_3674', 'field_3675', 'field_3676', 'field_3677', 'field_3678', 'field_3679', 'field_3680'] 
2025-09-05 09:20:23,031 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] configuration_price_matrix in kw: True 
2025-09-05 09:20:23,031 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] configuration_price in kw: True 
2025-09-05 09:20:23,031 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] Extracted price_matrix: 717.74 
2025-09-05 09:20:23,031 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] Extracted price_component: 111.92540000000001 
2025-09-05 09:20:23,424 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:20:23,439 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:23,440 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:23,441 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:23,441 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:23,442 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:23,443 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:23,445 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:23,445 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:23,446 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:23,447 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:23,450 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:23,451 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:23,452 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:23,871 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Found quantity multiplier: 1.0 
2025-09-05 09:20:23,871 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Preview context - price matrix with multiplier: 717.74 * 1.0 = 717.74 
2025-09-05 09:20:23,872 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Component total (BOM lines already include multiplier): 111.92540000000001 
2025-09-05 09:20:23,873 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ===== MODEL OPERATION COST CALCULATION ===== 
2025-09-05 09:20:23,873 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Configuration ID: 74717 
2025-09-05 09:20:23,873 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 09:20:23,873 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Loaded 346 config values 
2025-09-05 09:20:23,873 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 09:20:23,874 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ===== MODEL FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 09:20:24,042 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:20:24,046 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 09:20:24,065 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:24,065 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:24,066 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:24,067 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:24,069 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:24,070 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:24,071 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:24,071 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:24,073 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:24,074 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:24,075 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:24,075 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:24,077 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:24,077 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:24,078 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:24,078 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:24,080 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:24,080 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:24,081 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:24,082 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:24,087 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:24,089 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:24,090 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:24,091 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:24,093 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:24,093 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:24,105 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ===== MODEL FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 09:20:24,105 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Operations found: 133 
2025-09-05 09:20:24,105 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Total cost: $421.23153599999995 
2025-09-05 09:20:24,105 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 09:20:24,105 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ✓ Model operation cost: $421.23153599999995 from 133 operations 
2025-09-05 09:20:24,105 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Operation total (already includes multiplier): 421.23153599999995 
2025-09-05 09:20:24,105 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Configuration 74717 price calculation: 
2025-09-05 09:20:24,105 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Components: $111.92540000000001 
2025-09-05 09:20:24,105 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Operations: $421.23153599999995 
2025-09-05 09:20:24,105 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Sale Price Matrices (with multiplier): $717.74 
2025-09-05 09:20:24,105 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Total: $1250.896936 
2025-09-05 09:20:24,105 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Quantity Multiplier: 1.0 
2025-09-05 09:20:24,105 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - BOM ID: 74687 
2025-09-05 09:20:24,105 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - BOM lines count: 14 
2025-09-05 09:20:24,615 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:20:24,631 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:24,632 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:24,633 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:24,634 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:24,635 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:24,635 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:24,637 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:24,637 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:24,638 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:24,639 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:24,643 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:24,644 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:24,645 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:25,156 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Found quantity multiplier: 1.0 
2025-09-05 09:20:25,156 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Saving configuration - using base price 717.74 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 09:20:25,161 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Component total (BOM lines already include multiplier): 111.92540000000001 
2025-09-05 09:20:25,161 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ===== MODEL OPERATION COST CALCULATION ===== 
2025-09-05 09:20:25,161 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Configuration ID: 74717 
2025-09-05 09:20:25,161 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 09:20:25,161 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Loaded 346 config values 
2025-09-05 09:20:25,162 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 09:20:25,162 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ===== MODEL FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 09:20:25,342 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 09:20:25,348 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 09:20:25,371 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:25,372 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:25,373 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:25,374 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:25,376 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:25,376 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:25,377 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:25,378 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:25,380 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:25,380 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:25,381 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:25,382 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:25,384 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:25,384 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:25,386 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:25,386 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:25,388 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:25,388 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:25,390 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 09:20:25,391 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 09:20:25,400 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:25,401 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:25,404 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:25,407 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:25,411 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:25,413 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 09:20:25,429 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ===== MODEL FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 09:20:25,429 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Operations found: 133 
2025-09-05 09:20:25,429 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Total cost: $421.23153599999995 
2025-09-05 09:20:25,429 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 09:20:25,429 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ✓ Model operation cost: $421.23153599999995 from 133 operations 
2025-09-05 09:20:25,429 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Operation total (already includes multiplier): 421.23153599999995 
2025-09-05 09:20:25,429 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Configuration 74717 price calculation: 
2025-09-05 09:20:25,429 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Components: $111.92540000000001 
2025-09-05 09:20:25,429 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Operations: $421.23153599999995 
2025-09-05 09:20:25,429 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Sale Price Matrices (with multiplier): $717.74 
2025-09-05 09:20:25,429 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Total: $1250.896936 
2025-09-05 09:20:25,429 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Quantity Multiplier: 1.0 
2025-09-05 09:20:25,429 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - BOM ID: 74687 
2025-09-05 09:20:25,429 260897 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - BOM lines count: 14 
