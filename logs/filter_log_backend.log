2025-09-05 11:26:45,800 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 11:26:45,802 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 11:26:45,802 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 11:26:45,804 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 11:26:45,807 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 11:26:46,054 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:26:46,081 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:46,084 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:46,090 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:46,092 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:46,094 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:46,095 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:46,097 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:46,099 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:46,111 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:46,111 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:46,113 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:46,116 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:46,119 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:46,120 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:46,121 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:46,122 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:46,132 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:46,133 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:46,149 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:26:46,150 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:26:46,151 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 11:26:46,151 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:26:46,151 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 11:26:46,151 23114 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 11:26:46,152 23114 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 11:26:46,152 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 11:26:46,152 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 11:26:46,152 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 11:26:46,152 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Key field values - hinge_pickup: 3_per_door_attached, hinge_deliver: 3_per_door_loose_drilled 
2025-09-05 11:26:46,170 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Receive Material Time' from field 'Frame Colour' - Cost: $5.6000000000000005 
2025-09-05 11:26:46,173 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Load / Unload Extrusion & Set Up' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:26:46,180 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Centre Lock Cut Out' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:26:46,204 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Install Centre Lock' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $6.48 
2025-09-05 11:26:46,207 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Pick Centre Lock Site Kit' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $1.36 
2025-09-05 11:26:46,211 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC Picked Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:26:46,213 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Wrap Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:26:46,237 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Delivery Time - Time allocated to CD1 even for pick up orders' from field 'Number of Security Hinges (Pick Up)' - Cost: $5.6000000000000005 
2025-09-05 11:26:46,244 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:26:46,248 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:26:46,252 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:26:46,253 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:26:46,255 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:26:46,257 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:26:46,305 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Cut Extrusions on Upcut Saw' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:26:46,307 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up 2' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:26:46,308 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Extra Time to Cut French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:26:46,317 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Notch Slots for Centre Lock & French Door Mullion Caps in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:26:46,328 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Remove Breakaway Sections for Centre Notch & French Door Mullion Cap Notches in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:26:46,334 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert French Door Mullion Mohair (Schlegel PB48753B) into French Door Mullion or T-Section Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:26:46,338 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert Top & Bottom French Door Mullion Caps' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:26:46,342 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet French Door Mullion onto Lock Door or T-Section Mullion onto Non-Lock Door' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:26:46,351 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hardware' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $1.36 
2025-09-05 11:26:46,355 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:26:46,357 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:26:46,378 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Non-Lock Door Centre Striker Cut Out' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $1.36 
2025-09-05 11:26:46,383 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $2.64 
2025-09-05 11:26:46,389 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:26:46,390 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:26:46,397 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 11:26:46,399 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet Flush Bolts into Non-Lock Door' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $18.720000000000002 
2025-09-05 11:26:46,402 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:26:46,405 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:26:46,409 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:26:46,413 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:26:46,416 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:26:46,421 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:46,422 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:46,426 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:46,426 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:46,428 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $0.96 
2025-09-05 11:26:46,430 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:46,430 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:46,431 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:46,432 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:46,434 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $0.96 
2025-09-05 11:26:46,434 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:46,435 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:46,437 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:46,438 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:46,441 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Install Hinges' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $8.639999999999999 
2025-09-05 11:26:46,442 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:46,442 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:46,446 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:46,446 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:46,447 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hinges' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.4 
2025-09-05 11:26:46,451 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.64 
2025-09-05 11:26:46,453 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.64 
2025-09-05 11:26:46,456 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:46,457 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:46,459 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:46,459 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:46,460 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:26:46,461 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:46,462 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:46,464 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:46,465 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:46,466 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:26:46,467 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:46,468 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:46,470 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:46,470 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:46,473 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hinges' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:26:46,474 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:46,474 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:46,477 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:46,479 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:46,481 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:26:46,482 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:46,483 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:46,485 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:46,486 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:46,488 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:26:46,491 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:26:46,495 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:26:46,516 23114 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 11:26:46,516 23114 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 41 
2025-09-05 11:26:46,516 23114 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $95.92 
2025-09-05 11:26:46,516 23114 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 11:26:46,516 23114 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 11:26:46,517 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 from 41 operations 
2025-09-05 11:26:46,517 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 
2025-09-05 11:26:47,430 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:26:47,443 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:47,443 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:47,445 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:47,445 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:47,447 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:47,447 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:47,449 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:47,450 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:47,453 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:47,453 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:47,454 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:47,454 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:47,455 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:47,455 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:47,456 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:47,456 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:47,457 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:26:47,457 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:26:47,461 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:26:47,462 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:26:47,463 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:27:17,383 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 11:27:17,383 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 11:27:17,383 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 11:27:17,385 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 11:27:17,385 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 11:27:17,743 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 11:27:17,743 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 11:27:17,743 23114 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 11:27:17,744 23114 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 11:27:17,744 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 11:27:17,744 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 11:27:17,745 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 11:27:17,745 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Key field values - hinge_pickup: 3_per_door_attached, hinge_deliver: 3_per_door_loose_drilled 
2025-09-05 11:27:17,764 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Receive Material Time' from field 'Frame Colour' - Cost: $5.6000000000000005 
2025-09-05 11:27:17,767 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Load / Unload Extrusion & Set Up' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:27:17,776 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Centre Lock Cut Out' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:27:17,798 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Install Centre Lock' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $6.48 
2025-09-05 11:27:17,803 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Pick Centre Lock Site Kit' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $1.36 
2025-09-05 11:27:17,806 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC Picked Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:27:17,808 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Wrap Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:27:17,821 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Delivery Time - Time allocated to CD1 even for pick up orders' from field 'Number of Security Hinges (Pick Up)' - Cost: $5.6000000000000005 
2025-09-05 11:27:17,827 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:27:17,829 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:27:17,831 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:27:17,832 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:27:17,833 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:27:17,834 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:27:17,864 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Cut Extrusions on Upcut Saw' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:27:17,866 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up 2' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:27:17,868 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Extra Time to Cut French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:27:17,869 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Notch Slots for Centre Lock & French Door Mullion Caps in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:27:17,871 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Remove Breakaway Sections for Centre Notch & French Door Mullion Cap Notches in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:27:17,873 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert French Door Mullion Mohair (Schlegel PB48753B) into French Door Mullion or T-Section Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:27:17,874 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert Top & Bottom French Door Mullion Caps' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:27:17,876 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet French Door Mullion onto Lock Door or T-Section Mullion onto Non-Lock Door' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:27:17,885 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hardware' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $1.36 
2025-09-05 11:27:17,888 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:27:17,891 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:27:17,903 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Non-Lock Door Centre Striker Cut Out' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $1.36 
2025-09-05 11:27:17,905 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $2.64 
2025-09-05 11:27:17,909 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:27:17,910 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:27:17,915 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 11:27:17,916 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet Flush Bolts into Non-Lock Door' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $18.720000000000002 
2025-09-05 11:27:17,917 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:27:17,919 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:27:17,920 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:27:17,922 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:27:17,923 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:27:17,927 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:17,927 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:17,930 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:17,931 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:17,935 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $0.96 
2025-09-05 11:27:17,935 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:17,937 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:17,939 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:17,939 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:17,940 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $0.96 
2025-09-05 11:27:17,941 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:17,941 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:17,943 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:17,944 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:17,946 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Install Hinges' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $8.639999999999999 
2025-09-05 11:27:17,948 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:17,948 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:17,963 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:17,969 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:18,014 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hinges' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.4 
2025-09-05 11:27:18,047 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.64 
2025-09-05 11:27:18,092 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.64 
2025-09-05 11:27:18,156 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:18,158 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:18,164 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:18,178 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:18,183 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:27:18,187 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:18,189 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:18,193 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:18,195 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:18,198 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:27:18,201 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:18,202 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:18,212 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:18,216 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:18,223 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hinges' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:27:18,232 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:18,233 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:18,241 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:18,242 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:18,249 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:27:18,252 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:18,254 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:18,262 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:18,264 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:18,269 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:27:18,275 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:27:18,281 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:27:18,317 23114 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 11:27:18,318 23114 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 41 
2025-09-05 11:27:18,318 23114 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $95.92 
2025-09-05 11:27:18,319 23114 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 11:27:18,320 23114 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 11:27:18,335 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 from 41 operations 
2025-09-05 11:27:18,338 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 
2025-09-05 11:27:18,417 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:27:18,430 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:18,431 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:18,433 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:18,433 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:18,434 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:18,435 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:18,437 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:18,438 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:18,444 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:18,445 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:18,447 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:18,448 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:18,449 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:18,450 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:18,451 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:18,452 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:18,453 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:18,454 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:18,464 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:27:18,465 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:27:18,467 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:27:19,092 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:27:19,118 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:19,118 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:19,124 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:19,124 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:19,128 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:19,130 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:19,137 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:19,138 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:19,156 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:19,159 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:19,165 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:19,165 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:19,168 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:19,172 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:19,179 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:19,181 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:19,184 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:27:19,185 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:27:19,206 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:27:19,208 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:27:19,210 23114 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:01,538 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 11:44:01,539 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 11:44:01,539 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 11:44:01,540 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 11:44:01,541 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 11:44:01,895 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 11:44:01,895 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 11:44:01,895 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 11:44:01,896 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 11:44:01,896 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 11:44:01,897 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 11:44:01,897 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 11:44:01,898 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Key field values - hinge_pickup: 3_per_door_attached, hinge_deliver: 3_per_door_loose_drilled 
2025-09-05 11:44:01,925 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Receive Material Time' from field 'Frame Colour' - Cost: $5.6000000000000005 
2025-09-05 11:44:01,932 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Load / Unload Extrusion & Set Up' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:44:01,969 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Centre Lock Cut Out' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:44:02,138 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Install Centre Lock' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $6.48 
2025-09-05 11:44:02,144 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Pick Centre Lock Site Kit' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $1.36 
2025-09-05 11:44:02,148 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC Picked Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:44:02,153 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Wrap Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:44:02,173 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Delivery Time - Time allocated to CD1 even for pick up orders' from field 'Number of Security Hinges (Pick Up)' - Cost: $5.6000000000000005 
2025-09-05 11:44:02,184 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:02,187 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:02,189 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:02,191 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:02,194 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:02,196 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:02,260 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Cut Extrusions on Upcut Saw' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:02,265 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up 2' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:02,268 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Extra Time to Cut French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:02,273 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Notch Slots for Centre Lock & French Door Mullion Caps in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:02,275 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Remove Breakaway Sections for Centre Notch & French Door Mullion Cap Notches in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:02,279 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert French Door Mullion Mohair (Schlegel PB48753B) into French Door Mullion or T-Section Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:02,282 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert Top & Bottom French Door Mullion Caps' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:02,287 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet French Door Mullion onto Lock Door or T-Section Mullion onto Non-Lock Door' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:02,297 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hardware' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $1.36 
2025-09-05 11:44:02,301 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:44:02,304 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:44:02,325 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Non-Lock Door Centre Striker Cut Out' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $1.36 
2025-09-05 11:44:02,327 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $2.64 
2025-09-05 11:44:02,340 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:44:02,342 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:44:02,352 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 11:44:02,356 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet Flush Bolts into Non-Lock Door' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $18.720000000000002 
2025-09-05 11:44:02,360 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:44:02,365 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:44:02,369 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:44:02,373 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:44:02,380 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:44:02,391 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:02,393 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:02,397 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:02,398 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:02,403 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $0.96 
2025-09-05 11:44:02,405 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:02,406 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:02,408 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:02,409 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:02,412 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $0.96 
2025-09-05 11:44:02,415 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:02,416 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:02,419 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:02,420 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:02,423 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Install Hinges' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $8.639999999999999 
2025-09-05 11:44:02,426 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:02,427 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:02,431 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:02,432 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:02,434 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hinges' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.4 
2025-09-05 11:44:02,447 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.64 
2025-09-05 11:44:02,453 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.64 
2025-09-05 11:44:02,457 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:02,464 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:02,479 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:02,480 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:02,482 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:44:02,483 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:02,484 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:02,485 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:02,486 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:02,488 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:44:02,489 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:02,491 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:02,492 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:02,493 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:02,495 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hinges' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:44:02,502 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:02,503 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:02,506 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:02,507 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:02,510 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:44:02,511 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:02,512 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:02,514 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:02,517 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:02,518 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:44:02,521 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:44:02,526 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:44:02,551 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 11:44:02,552 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 41 
2025-09-05 11:44:02,552 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $95.92 
2025-09-05 11:44:02,552 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 11:44:02,552 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 11:44:02,554 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 from 41 operations 
2025-09-05 11:44:02,555 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 
2025-09-05 11:44:02,636 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:44:02,652 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:02,653 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:02,655 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:02,656 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:02,657 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:02,657 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:02,660 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:02,661 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:02,665 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:02,666 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:02,667 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:02,667 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:02,669 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:02,670 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:02,671 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:02,672 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:02,674 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:02,675 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:02,684 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:02,685 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:02,686 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:03,202 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:44:03,224 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:03,225 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:03,228 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:03,229 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:03,232 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:03,233 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:03,237 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:03,238 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:03,245 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:03,246 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:03,248 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:03,249 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:03,263 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:03,263 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:03,267 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:03,268 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:03,272 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:03,273 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:03,281 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:03,282 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:03,284 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:11,166 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 11:44:11,166 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 11:44:11,166 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 11:44:11,166 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 11:44:11,167 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 11:44:11,732 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 11:44:11,732 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 11:44:11,732 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 11:44:11,732 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 11:44:11,732 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 11:44:11,732 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 11:44:11,732 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 11:44:11,732 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Key field values - hinge_pickup: 3_per_door_attached, hinge_deliver: 3_per_door_loose_drilled 
2025-09-05 11:44:11,759 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Receive Material Time' from field 'Frame Colour' - Cost: $5.6000000000000005 
2025-09-05 11:44:11,762 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Load / Unload Extrusion & Set Up' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:44:11,774 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Centre Lock Cut Out' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:44:11,805 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Install Centre Lock' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $6.48 
2025-09-05 11:44:11,809 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Pick Centre Lock Site Kit' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $1.36 
2025-09-05 11:44:11,812 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC Picked Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:44:11,816 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Wrap Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:44:11,833 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Delivery Time - Time allocated to CD1 even for pick up orders' from field 'Number of Security Hinges (Pick Up)' - Cost: $5.6000000000000005 
2025-09-05 11:44:11,840 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:11,842 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:11,844 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:11,845 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:11,848 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:11,849 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:11,904 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Cut Extrusions on Upcut Saw' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:11,907 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up 2' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:11,909 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Extra Time to Cut French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:11,910 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Notch Slots for Centre Lock & French Door Mullion Caps in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:11,911 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Remove Breakaway Sections for Centre Notch & French Door Mullion Cap Notches in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:11,912 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert French Door Mullion Mohair (Schlegel PB48753B) into French Door Mullion or T-Section Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:11,914 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert Top & Bottom French Door Mullion Caps' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:11,914 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet French Door Mullion onto Lock Door or T-Section Mullion onto Non-Lock Door' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:11,921 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hardware' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $1.36 
2025-09-05 11:44:11,923 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:44:11,925 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:44:11,940 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Non-Lock Door Centre Striker Cut Out' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $1.36 
2025-09-05 11:44:11,942 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $2.64 
2025-09-05 11:44:11,952 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:44:11,953 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:44:11,964 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 11:44:11,965 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet Flush Bolts into Non-Lock Door' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $18.720000000000002 
2025-09-05 11:44:11,968 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:44:11,970 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:44:11,975 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:44:11,976 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:44:11,980 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:44:11,987 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:11,987 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:11,990 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:11,990 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:11,991 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $0.96 
2025-09-05 11:44:11,992 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:11,992 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:11,993 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:11,994 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:11,994 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $0.96 
2025-09-05 11:44:11,995 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:11,995 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:11,997 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:11,998 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:11,999 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Install Hinges' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $8.639999999999999 
2025-09-05 11:44:11,999 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:12,000 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:12,002 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:12,002 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:12,003 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hinges' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.4 
2025-09-05 11:44:12,006 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.64 
2025-09-05 11:44:12,008 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.64 
2025-09-05 11:44:12,010 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:12,011 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:12,014 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:12,017 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:12,019 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:44:12,020 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:12,020 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:12,021 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:12,022 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:12,023 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:44:12,024 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:12,024 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:12,025 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:12,025 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:12,037 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hinges' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:44:12,038 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:12,039 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:12,040 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:12,040 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:12,041 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:44:12,042 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:12,042 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:12,043 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:12,044 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:12,045 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:44:12,048 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:44:12,050 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:44:12,072 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 11:44:12,072 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 41 
2025-09-05 11:44:12,073 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $95.92 
2025-09-05 11:44:12,073 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 11:44:12,073 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 11:44:12,073 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 from 41 operations 
2025-09-05 11:44:12,073 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 
2025-09-05 11:44:12,173 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:44:12,180 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:12,180 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:12,183 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:12,183 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:12,185 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:12,185 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:12,187 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:12,187 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:12,190 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:12,191 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:12,192 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:12,192 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:12,193 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:12,194 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:12,195 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:12,195 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:12,197 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:12,197 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:12,203 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:12,204 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:12,205 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:25,050 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 11:44:25,050 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 11:44:25,050 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 11:44:25,050 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 11:44:25,051 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 11:44:25,541 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 11:44:25,541 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 11:44:25,541 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 11:44:25,541 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 11:44:25,542 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 11:44:25,542 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 11:44:25,542 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 11:44:25,542 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Key field values - hinge_pickup: 3_per_door_attached, hinge_deliver: 3_per_door_loose_drilled 
2025-09-05 11:44:25,559 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Receive Material Time' from field 'Frame Colour' - Cost: $5.6000000000000005 
2025-09-05 11:44:25,561 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Load / Unload Extrusion & Set Up' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:44:25,568 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Centre Lock Cut Out' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:44:25,590 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Install Centre Lock' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $6.48 
2025-09-05 11:44:25,593 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Pick Centre Lock Site Kit' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $1.36 
2025-09-05 11:44:25,597 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC Picked Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:44:25,600 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Wrap Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:44:25,614 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Delivery Time - Time allocated to CD1 even for pick up orders' from field 'Number of Security Hinges (Pick Up)' - Cost: $5.6000000000000005 
2025-09-05 11:44:25,620 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:25,622 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:25,624 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:25,625 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:25,627 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:25,628 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:25,659 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Cut Extrusions on Upcut Saw' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:25,660 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up 2' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:25,661 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Extra Time to Cut French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:25,663 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Notch Slots for Centre Lock & French Door Mullion Caps in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:25,665 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Remove Breakaway Sections for Centre Notch & French Door Mullion Cap Notches in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:25,666 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert French Door Mullion Mohair (Schlegel PB48753B) into French Door Mullion or T-Section Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:25,667 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert Top & Bottom French Door Mullion Caps' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:25,669 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet French Door Mullion onto Lock Door or T-Section Mullion onto Non-Lock Door' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:25,675 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hardware' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $1.36 
2025-09-05 11:44:25,678 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:44:25,681 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:44:25,704 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Non-Lock Door Centre Striker Cut Out' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $1.36 
2025-09-05 11:44:25,706 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $2.64 
2025-09-05 11:44:25,717 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:44:25,718 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:44:25,724 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 11:44:25,726 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet Flush Bolts into Non-Lock Door' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $18.720000000000002 
2025-09-05 11:44:25,730 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:44:25,734 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:44:25,738 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:44:25,740 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:44:25,742 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:44:25,748 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:25,748 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:25,750 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:25,751 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:25,752 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $0.96 
2025-09-05 11:44:25,753 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:25,754 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:25,754 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:25,755 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:25,756 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $0.96 
2025-09-05 11:44:25,756 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:25,756 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:25,757 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:25,758 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:25,759 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Install Hinges' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $8.639999999999999 
2025-09-05 11:44:25,760 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:25,761 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:25,762 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:25,763 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:25,764 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hinges' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.4 
2025-09-05 11:44:25,765 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.64 
2025-09-05 11:44:25,766 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.64 
2025-09-05 11:44:25,768 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:25,769 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:25,770 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:25,770 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:25,771 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:44:25,771 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:25,772 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:25,773 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:25,773 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:25,774 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:44:25,774 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:25,775 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:25,777 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:25,778 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:25,780 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hinges' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:44:25,781 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:25,781 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:25,782 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:25,783 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:25,784 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:44:25,785 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:25,785 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:25,786 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:25,786 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:25,787 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:44:25,789 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:44:25,790 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:44:25,805 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 11:44:25,806 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 41 
2025-09-05 11:44:25,806 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $95.92 
2025-09-05 11:44:25,806 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 11:44:25,806 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 11:44:25,806 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 from 41 operations 
2025-09-05 11:44:25,806 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 
2025-09-05 11:44:25,942 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:44:25,949 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:25,949 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:25,950 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:25,951 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:25,951 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:25,952 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:25,953 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:25,953 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:25,955 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:25,956 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:25,956 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:25,957 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:25,957 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:25,957 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:25,958 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:25,958 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:25,959 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:25,959 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:25,962 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:25,963 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:25,964 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:34,301 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 11:44:34,301 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 11:44:34,301 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 11:44:34,301 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 11:44:34,302 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 11:44:34,870 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 11:44:34,870 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 11:44:34,870 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 11:44:34,870 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 11:44:34,871 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 11:44:34,871 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 11:44:34,871 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 11:44:34,872 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Key field values - hinge_pickup: 3_per_door_attached, hinge_deliver: 3_per_door_loose_drilled 
2025-09-05 11:44:34,892 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Receive Material Time' from field 'Frame Colour' - Cost: $5.6000000000000005 
2025-09-05 11:44:34,895 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Load / Unload Extrusion & Set Up' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:44:34,905 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Centre Lock Cut Out' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:44:34,936 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Install Centre Lock' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $6.48 
2025-09-05 11:44:34,940 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Pick Centre Lock Site Kit' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $1.36 
2025-09-05 11:44:34,943 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC Picked Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:44:34,946 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Wrap Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:44:34,964 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Delivery Time - Time allocated to CD1 even for pick up orders' from field 'Number of Security Hinges (Pick Up)' - Cost: $5.6000000000000005 
2025-09-05 11:44:34,972 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:34,974 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:34,977 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:34,979 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:34,981 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:34,983 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:35,018 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Cut Extrusions on Upcut Saw' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:35,022 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up 2' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:35,025 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Extra Time to Cut French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:35,028 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Notch Slots for Centre Lock & French Door Mullion Caps in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:35,035 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Remove Breakaway Sections for Centre Notch & French Door Mullion Cap Notches in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:35,038 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert French Door Mullion Mohair (Schlegel PB48753B) into French Door Mullion or T-Section Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:35,042 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert Top & Bottom French Door Mullion Caps' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:35,045 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet French Door Mullion onto Lock Door or T-Section Mullion onto Non-Lock Door' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:35,056 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hardware' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $1.36 
2025-09-05 11:44:35,061 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:44:35,063 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:44:35,079 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Non-Lock Door Centre Striker Cut Out' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $1.36 
2025-09-05 11:44:35,081 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $2.64 
2025-09-05 11:44:35,086 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:44:35,087 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:44:35,092 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 11:44:35,094 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet Flush Bolts into Non-Lock Door' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $18.720000000000002 
2025-09-05 11:44:35,097 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:44:35,099 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:44:35,103 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:44:35,105 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:44:35,106 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:44:35,113 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:35,114 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:35,116 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:35,116 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:35,118 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $0.96 
2025-09-05 11:44:35,119 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:35,119 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:35,121 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:35,122 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:35,123 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $0.96 
2025-09-05 11:44:35,124 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:35,124 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:35,126 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:35,126 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:35,128 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Install Hinges' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $8.639999999999999 
2025-09-05 11:44:35,128 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:35,129 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:35,130 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:35,131 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:35,132 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hinges' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.4 
2025-09-05 11:44:35,134 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.64 
2025-09-05 11:44:35,136 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.64 
2025-09-05 11:44:35,139 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:35,141 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:35,142 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:35,142 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:35,143 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:44:35,144 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:35,145 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:35,147 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:35,147 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:35,148 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:44:35,149 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:35,150 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:35,151 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:35,152 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:35,153 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hinges' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:44:35,154 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:35,155 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:35,156 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:35,157 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:35,157 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:44:35,158 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:35,158 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:35,159 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:35,160 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:35,161 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:44:35,163 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:44:35,165 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:44:35,186 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 11:44:35,187 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 41 
2025-09-05 11:44:35,187 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $95.92 
2025-09-05 11:44:35,187 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 11:44:35,187 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 11:44:35,190 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 from 41 operations 
2025-09-05 11:44:35,197 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 
2025-09-05 11:44:35,317 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:44:35,323 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:35,323 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:35,324 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:35,324 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:35,325 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:35,325 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:35,326 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:35,326 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:35,329 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:35,329 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:35,330 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:35,330 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:35,331 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:35,332 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:35,332 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:35,333 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:35,333 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:35,334 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:35,336 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:35,337 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:35,338 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:44,302 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 11:44:44,302 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 11:44:44,302 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 11:44:44,302 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 11:44:44,303 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 11:44:44,847 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 11:44:44,848 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 11:44:44,848 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 11:44:44,849 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 11:44:44,849 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 11:44:44,850 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 11:44:44,850 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 11:44:44,851 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Key field values - hinge_pickup: 3_per_door_attached, hinge_deliver: 3_per_door_loose_drilled 
2025-09-05 11:44:44,869 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Receive Material Time' from field 'Frame Colour' - Cost: $5.6000000000000005 
2025-09-05 11:44:44,873 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Load / Unload Extrusion & Set Up' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:44:44,875 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC & Colour Consitency CommandeX Door Frame' from field 'Frame Colour' - Cost: $3.8919024624 
2025-09-05 11:44:44,877 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Cut CommandeX Door Frame' from field 'Frame Colour' - Cost: $15.42906316 
2025-09-05 11:44:44,880 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Cut Plugh for CommandeX Door Frame' from field 'Frame Colour' - Cost: $6.0834591888 
2025-09-05 11:44:44,883 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Centre Lock Cut Out' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:44:44,885 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Insert Plugh into CommandeX Door Frame' from field 'Frame Colour' - Cost: $16.058820840000003 
2025-09-05 11:44:44,886 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Assemble CommandeX Door with Crimped Corners' from field 'Frame Colour' - Cost: $33.364561886400004 
2025-09-05 11:44:44,887 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Plugh CommandeX Door with Assembly Press' from field 'Frame Colour' - Cost: $18.5526612528 
2025-09-05 11:44:44,894 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC CommandeX Door' from field 'Frame Colour' - Cost: $17.9229035728 
2025-09-05 11:44:44,896 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Cut Mesh or Sheet' from field 'Frame Colour' - Cost: $12.003181380800001 
2025-09-05 11:44:44,910 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Install Centre Lock' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $6.48 
2025-09-05 11:44:44,917 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Pick Centre Lock Site Kit' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $1.36 
2025-09-05 11:44:44,921 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC Picked Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:44:44,924 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Wrap Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:44:44,937 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Load Door (Delivery) / Customer Collect (Pick Up)' from field 'Number of Security Hinges (Pick Up)' - Cost: $2.6449822560000005 
2025-09-05 11:44:44,941 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Delivery Time - Time allocated to CD1 even for pick up orders' from field 'Number of Security Hinges (Pick Up)' - Cost: $5.6000000000000005 
2025-09-05 11:44:44,947 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:44,949 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:44,951 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:44,952 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:44,954 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:44,955 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:44,989 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Cut Extrusions on Upcut Saw' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:44,990 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up 2' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:44,991 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Extra Time to Cut French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:44,993 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Notch Slots for Centre Lock & French Door Mullion Caps in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:44,994 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Remove Breakaway Sections for Centre Notch & French Door Mullion Cap Notches in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:44,997 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert French Door Mullion Mohair (Schlegel PB48753B) into French Door Mullion or T-Section Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:44,999 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert Top & Bottom French Door Mullion Caps' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:45,001 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet French Door Mullion onto Lock Door or T-Section Mullion onto Non-Lock Door' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:45,007 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hardware' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $1.36 
2025-09-05 11:44:45,009 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:44:45,013 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:44:45,032 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Non-Lock Door Centre Striker Cut Out' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $1.36 
2025-09-05 11:44:45,035 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $2.64 
2025-09-05 11:44:45,040 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:44:45,041 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:44:45,049 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 11:44:45,051 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet Flush Bolts into Non-Lock Door' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $18.720000000000002 
2025-09-05 11:44:45,055 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:44:45,059 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:44:45,063 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:44:45,066 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:44:45,067 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:44:45,073 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:45,074 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:45,075 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:45,077 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:45,078 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $0.96 
2025-09-05 11:44:45,079 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:45,079 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:45,081 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:45,081 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:45,083 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $0.96 
2025-09-05 11:44:45,084 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:45,084 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:45,086 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:45,088 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:45,089 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Install Hinges' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $8.639999999999999 
2025-09-05 11:44:45,090 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:45,091 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:45,093 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:45,094 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:45,097 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hinges' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.4 
2025-09-05 11:44:45,105 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.64 
2025-09-05 11:44:45,107 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Attached' in field 'Number of Security Hinges (Pick Up)' - Cost: $2.64 
2025-09-05 11:44:45,110 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:45,111 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:45,113 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:45,114 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:45,116 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:44:45,117 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:45,117 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:45,118 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:45,118 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:45,121 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:44:45,121 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:45,122 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:45,123 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:45,124 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:45,125 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hinges' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:44:45,126 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:45,127 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:45,128 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:45,128 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:45,129 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:44:45,130 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:45,130 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:45,131 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:45,132 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:45,133 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:44:45,134 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:44:45,135 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:44:45,153 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 11:44:45,153 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 50 
2025-09-05 11:44:45,153 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $221.871536 
2025-09-05 11:44:45,153 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 11:44:45,153 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 11:44:45,153 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 from 50 operations 
2025-09-05 11:44:45,153 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 
2025-09-05 11:44:45,784 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:44:45,792 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:45,792 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:45,794 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:45,794 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:45,795 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:45,796 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:45,797 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:45,798 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:45,801 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:45,801 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:45,802 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:45,802 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:45,803 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:45,803 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:45,804 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:45,804 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:45,805 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 11:44:45,806 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:45,809 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:45,810 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:45,811 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:55,615 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 11:44:55,615 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 11:44:55,615 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 11:44:55,615 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 11:44:55,616 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 11:44:55,876 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 11:44:55,876 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 11:44:55,876 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 11:44:55,876 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 11:44:55,876 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 11:44:55,876 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 11:44:55,876 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 11:44:55,876 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Key field values - hinge_pickup: 0, hinge_deliver: 3_per_door_loose_drilled 
2025-09-05 11:44:55,895 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Receive Material Time' from field 'Frame Colour' - Cost: $5.6000000000000005 
2025-09-05 11:44:55,897 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Load / Unload Extrusion & Set Up' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:44:55,899 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC & Colour Consitency CommandeX Door Frame' from field 'Frame Colour' - Cost: $3.8919024624 
2025-09-05 11:44:55,900 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Cut CommandeX Door Frame' from field 'Frame Colour' - Cost: $15.42906316 
2025-09-05 11:44:55,901 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Cut Plugh for CommandeX Door Frame' from field 'Frame Colour' - Cost: $6.0834591888 
2025-09-05 11:44:55,903 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Centre Lock Cut Out' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:44:55,904 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Insert Plugh into CommandeX Door Frame' from field 'Frame Colour' - Cost: $16.058820840000003 
2025-09-05 11:44:55,905 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Assemble CommandeX Door with Crimped Corners' from field 'Frame Colour' - Cost: $33.364561886400004 
2025-09-05 11:44:55,906 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Plugh CommandeX Door with Assembly Press' from field 'Frame Colour' - Cost: $18.5526612528 
2025-09-05 11:44:55,909 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC CommandeX Door' from field 'Frame Colour' - Cost: $17.9229035728 
2025-09-05 11:44:55,910 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Cut Mesh or Sheet' from field 'Frame Colour' - Cost: $12.003181380800001 
2025-09-05 11:44:55,922 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Install Centre Lock' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $6.48 
2025-09-05 11:44:55,924 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Pick Centre Lock Site Kit' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $1.36 
2025-09-05 11:44:55,926 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC Picked Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:44:55,929 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Wrap Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:44:55,940 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Load Door (Delivery) / Customer Collect (Pick Up)' from field 'Number of Security Hinges (Pick Up)' - Cost: $2.6449822560000005 
2025-09-05 11:44:55,943 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Delivery Time - Time allocated to CD1 even for pick up orders' from field 'Number of Security Hinges (Pick Up)' - Cost: $5.6000000000000005 
2025-09-05 11:44:55,947 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:55,949 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:55,951 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:55,951 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:55,953 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:55,953 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:44:55,986 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Cut Extrusions on Upcut Saw' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:55,987 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up 2' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:55,988 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Extra Time to Cut French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:55,989 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Notch Slots for Centre Lock & French Door Mullion Caps in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:55,990 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Remove Breakaway Sections for Centre Notch & French Door Mullion Cap Notches in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:55,991 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert French Door Mullion Mohair (Schlegel PB48753B) into French Door Mullion or T-Section Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:55,992 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert Top & Bottom French Door Mullion Caps' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:55,993 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet French Door Mullion onto Lock Door or T-Section Mullion onto Non-Lock Door' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:44:55,999 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hardware' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $1.36 
2025-09-05 11:44:56,001 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:44:56,002 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:44:56,017 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Non-Lock Door Centre Striker Cut Out' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $1.36 
2025-09-05 11:44:56,018 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $2.64 
2025-09-05 11:44:56,022 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:44:56,022 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:44:56,029 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 11:44:56,031 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet Flush Bolts into Non-Lock Door' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $18.720000000000002 
2025-09-05 11:44:56,034 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:44:56,037 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:44:56,041 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:44:56,047 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:44:56,051 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:44:56,063 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:44:56,064 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:56,069 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:44:56,069 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:56,076 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:44:56,077 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:44:56,078 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:56,082 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:44:56,083 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:56,088 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:44:56,090 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:44:56,091 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:56,100 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:44:56,101 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:56,109 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hinges' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:44:56,111 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:44:56,111 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:56,114 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:44:56,115 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:56,118 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:44:56,119 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:44:56,120 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:56,127 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:44:56,128 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:44:56,130 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:44:56,134 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:44:56,151 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:44:56,188 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 11:44:56,188 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 44 
2025-09-05 11:44:56,189 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $203.631536 
2025-09-05 11:44:56,189 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 11:44:56,189 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 11:44:56,189 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $203.631536 from 44 operations 
2025-09-05 11:44:56,189 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $203.631536 
2025-09-05 11:44:58,947 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 11:44:58,950 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 11:44:58,952 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 11:44:58,953 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 11:44:58,953 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 11:45:00,116 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 11:45:00,117 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 11:45:00,117 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 11:45:00,119 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 11:45:00,121 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 11:45:00,121 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 11:45:00,122 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 11:45:00,123 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Key field values - hinge_pickup: 0, hinge_deliver: 3_per_door_loose_drilled 
2025-09-05 11:45:00,150 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Receive Material Time' from field 'Frame Colour' - Cost: $5.6000000000000005 
2025-09-05 11:45:00,156 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Load / Unload Extrusion & Set Up' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:45:00,158 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC & Colour Consitency CommandeX Door Frame' from field 'Frame Colour' - Cost: $3.8919024624 
2025-09-05 11:45:00,160 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Cut CommandeX Door Frame' from field 'Frame Colour' - Cost: $15.42906316 
2025-09-05 11:45:00,163 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Cut Plugh for CommandeX Door Frame' from field 'Frame Colour' - Cost: $6.0834591888 
2025-09-05 11:45:00,169 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Centre Lock Cut Out' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:45:00,171 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Insert Plugh into CommandeX Door Frame' from field 'Frame Colour' - Cost: $16.058820840000003 
2025-09-05 11:45:00,174 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Assemble CommandeX Door with Crimped Corners' from field 'Frame Colour' - Cost: $33.364561886400004 
2025-09-05 11:45:00,176 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Plugh CommandeX Door with Assembly Press' from field 'Frame Colour' - Cost: $18.5526612528 
2025-09-05 11:45:00,190 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC CommandeX Door' from field 'Frame Colour' - Cost: $17.9229035728 
2025-09-05 11:45:00,192 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Cut Mesh or Sheet' from field 'Frame Colour' - Cost: $12.003181380800001 
2025-09-05 11:45:00,203 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Install Centre Lock' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $6.48 
2025-09-05 11:45:00,207 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Pick Centre Lock Site Kit' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $1.36 
2025-09-05 11:45:00,211 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC Picked Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:45:00,215 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Wrap Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:45:00,231 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Load Door (Delivery) / Customer Collect (Pick Up)' from field 'Number of Security Hinges (Pick Up)' - Cost: $2.6449822560000005 
2025-09-05 11:45:00,235 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Delivery Time - Time allocated to CD1 even for pick up orders' from field 'Number of Security Hinges (Pick Up)' - Cost: $5.6000000000000005 
2025-09-05 11:45:00,242 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:00,245 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:00,248 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:00,250 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:00,252 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:00,254 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:00,300 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Cut Extrusions on Upcut Saw' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:00,303 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up 2' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:00,306 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Extra Time to Cut French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:00,309 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Notch Slots for Centre Lock & French Door Mullion Caps in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:00,312 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Remove Breakaway Sections for Centre Notch & French Door Mullion Cap Notches in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:00,317 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert French Door Mullion Mohair (Schlegel PB48753B) into French Door Mullion or T-Section Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:00,322 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert Top & Bottom French Door Mullion Caps' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:00,326 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet French Door Mullion onto Lock Door or T-Section Mullion onto Non-Lock Door' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:00,342 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hardware' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $1.36 
2025-09-05 11:45:00,350 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:45:00,356 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:45:00,391 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Non-Lock Door Centre Striker Cut Out' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $1.36 
2025-09-05 11:45:00,398 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $2.64 
2025-09-05 11:45:00,408 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:45:00,410 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:45:00,417 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 11:45:00,420 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet Flush Bolts into Non-Lock Door' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $18.720000000000002 
2025-09-05 11:45:00,424 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:45:00,430 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:45:00,437 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:45:00,441 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:45:00,445 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:45:00,456 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:00,457 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:00,460 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:00,460 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:00,463 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:45:00,464 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:00,465 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:00,466 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:00,466 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:00,468 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:45:00,469 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:00,470 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:00,472 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:00,472 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:00,473 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hinges' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:45:00,474 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:00,475 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:00,476 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:00,477 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:00,478 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:45:00,479 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:00,480 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:00,481 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:00,482 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:00,483 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:45:00,485 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:45:00,489 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:45:00,510 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 11:45:00,510 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 44 
2025-09-05 11:45:00,511 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $203.631536 
2025-09-05 11:45:00,511 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 11:45:00,512 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 11:45:00,512 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $203.631536 from 44 operations 
2025-09-05 11:45:00,512 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $203.631536 
2025-09-05 11:45:00,860 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:45:01,111 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:45:01,115 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:01,116 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:01,121 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:01,122 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:01,125 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:01,126 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:01,131 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:01,132 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:01,135 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:01,137 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:01,153 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:01,155 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:01,157 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:01,157 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:01,159 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:01,162 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:01,163 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:01,168 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:01,169 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:01,206 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:01,244 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:01,261 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:01,300 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:01,417 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:01,424 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:01,431 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:05,001 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 11:45:05,001 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 11:45:05,001 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 11:45:05,001 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 11:45:05,002 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 11:45:05,621 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 11:45:05,621 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 11:45:05,622 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 11:45:05,622 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 11:45:05,622 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 11:45:05,622 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 11:45:05,623 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 11:45:05,624 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Key field values - hinge_pickup: 0, hinge_deliver: 3_per_door_loose_drilled 
2025-09-05 11:45:05,648 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Receive Material Time' from field 'Frame Colour' - Cost: $5.6000000000000005 
2025-09-05 11:45:05,651 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Load / Unload Extrusion & Set Up' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:45:05,653 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC & Colour Consitency CommandeX Door Frame' from field 'Frame Colour' - Cost: $3.8919024624 
2025-09-05 11:45:05,656 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Cut CommandeX Door Frame' from field 'Frame Colour' - Cost: $15.42906316 
2025-09-05 11:45:05,658 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Cut Plugh for CommandeX Door Frame' from field 'Frame Colour' - Cost: $6.0834591888 
2025-09-05 11:45:05,662 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'CNC Centre Lock Cut Out' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:45:05,664 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Insert Plugh into CommandeX Door Frame' from field 'Frame Colour' - Cost: $16.058820840000003 
2025-09-05 11:45:05,666 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Assemble CommandeX Door with Crimped Corners' from field 'Frame Colour' - Cost: $33.364561886400004 
2025-09-05 11:45:05,668 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Plugh CommandeX Door with Assembly Press' from field 'Frame Colour' - Cost: $18.5526612528 
2025-09-05 11:45:05,680 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC CommandeX Door' from field 'Frame Colour' - Cost: $17.9229035728 
2025-09-05 11:45:05,682 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Cut Mesh or Sheet' from field 'Frame Colour' - Cost: $12.003181380800001 
2025-09-05 11:45:05,694 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Install Centre Lock' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $6.48 
2025-09-05 11:45:05,698 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Pick Centre Lock Site Kit' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $1.36 
2025-09-05 11:45:05,701 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'QC Picked Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:45:05,704 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Wrap Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:45:05,721 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Load Door (Delivery) / Customer Collect (Pick Up)' from field 'Number of Security Hinges (Pick Up)' - Cost: $2.6449822560000005 
2025-09-05 11:45:05,725 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation 'Delivery Time - Time allocated to CD1 even for pick up orders' from field 'Number of Security Hinges (Pick Up)' - Cost: $5.6000000000000005 
2025-09-05 11:45:05,731 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:05,733 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:05,736 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:05,737 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:05,739 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:05,740 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:05,777 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Cut Extrusions on Upcut Saw' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:05,780 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up 2' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:05,782 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Extra Time to Cut French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:05,783 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Notch Slots for Centre Lock & French Door Mullion Caps in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:05,785 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Remove Breakaway Sections for Centre Notch & French Door Mullion Cap Notches in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:05,787 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert French Door Mullion Mohair (Schlegel PB48753B) into French Door Mullion or T-Section Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:05,789 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Insert Top & Bottom French Door Mullion Caps' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:05,791 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet French Door Mullion onto Lock Door or T-Section Mullion onto Non-Lock Door' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:05,799 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hardware' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $1.36 
2025-09-05 11:45:05,802 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:45:05,805 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:45:05,825 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Non-Lock Door Centre Striker Cut Out' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $1.36 
2025-09-05 11:45:05,828 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $2.64 
2025-09-05 11:45:05,836 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:45:05,837 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:45:05,841 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 11:45:05,843 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Rivet Flush Bolts into Non-Lock Door' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $18.720000000000002 
2025-09-05 11:45:05,844 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:45:05,846 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:45:05,848 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:45:05,850 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:45:05,853 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:45:05,862 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:05,863 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:05,865 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:05,866 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:05,867 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:45:05,867 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:05,867 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:05,868 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:05,869 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:05,870 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:45:05,871 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:05,871 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:05,873 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:05,874 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:05,875 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Pick Hinges' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:45:05,876 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:05,876 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:05,877 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:05,878 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:05,881 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'QC Picked Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:45:05,881 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:05,882 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:05,883 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:05,883 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:05,884 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'Wrap Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:45:05,886 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:45:05,887 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:45:05,903 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 11:45:05,904 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 44 
2025-09-05 11:45:05,904 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $203.631536 
2025-09-05 11:45:05,904 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 11:45:05,904 28709 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 11:45:05,904 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $203.631536 from 44 operations 
2025-09-05 11:45:05,904 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $203.631536 
2025-09-05 11:45:06,139 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:45:06,159 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:06,160 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:06,162 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:06,163 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:06,164 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:06,165 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:06,167 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:06,167 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:06,169 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:06,170 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:06,175 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:06,176 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:06,177 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:11,182 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] Received parameters: ['template_id', 'product_id', 'order_line_id', 'config_data', 'configuration_price', 'configuration_price_matrix', 'total_price', 'field_3451', 'field_3452', 'field_3453', 'field_3454', 'field_3455', 'field_3456', 'field_3457', 'field_3458', 'field_3459', 'field_3460', 'field_3461', 'field_3462', 'field_3463', 'field_3464', 'field_3465', 'field_3466', 'field_3467', 'field_3468', 'field_3469', 'field_3470', 'field_3471', 'field_3472', 'field_3473', 'field_3474', 'field_3475', 'field_3476', 'field_3477', 'field_3478', 'field_3479', 'field_3480', 'field_3481', 'field_3482', 'field_3483', 'field_3484', 'field_3485', 'field_3486', 'field_3487', 'field_3488', 'field_3489', 'field_3490', 'field_3491', 'field_3492', 'field_3493', 'field_3494', 'field_3495', 'field_3496', 'field_3497', 'field_3498', 'field_3499', 'field_3500', 'field_3501', 'field_3502', 'field_3503', 'field_3504', 'field_3505', 'field_3506', 'field_3507', 'field_3508', 'field_3509', 'field_3510', 'field_3511', 'field_3512', 'field_3513', 'field_3514', 'field_3515', 'field_3516', 'field_3517', 'field_3518', 'field_3519', 'field_3520', 'field_3521', 'field_3522', 'field_3523', 'field_3524', 'field_3525', 'field_3526', 'field_3527', 'field_3528', 'field_3529', 'field_3530', 'field_3531', 'field_3532', 'field_3533', 'field_3534', 'field_3535', 'field_3536', 'field_3537', 'field_3538', 'field_3539', 'field_3540', 'field_3541', 'field_3542', 'field_3543', 'field_3544', 'field_3545', 'field_3546', 'field_3547', 'field_3548', 'field_3549', 'field_3550', 'field_3551', 'field_3552', 'field_3553', 'field_3554', 'field_3555', 'field_3556', 'field_3557', 'field_3558', 'field_3559', 'field_3560', 'field_3561', 'field_3562', 'field_3563', 'field_3564', 'field_3565', 'field_3566', 'field_3567', 'field_3568', 'field_3569', 'field_3570', 'field_3571', 'field_3572', 'field_3573', 'field_3574', 'field_3575', 'field_3576', 'field_3577', 'field_3578', 'field_3579', 'field_3580', 'field_3581', 'field_3582', 'field_3585', 'field_3586', 'field_3583', 'field_3584', 'field_3587', 'field_3588', 'field_3589', 'field_3590', 'field_3591', 'field_3592', 'field_3593', 'field_3594', 'field_3595', 'field_3596', 'field_3597', 'field_3598', 'field_3599', 'field_3600', 'field_3601', 'field_3602', 'field_3603', 'field_3604', 'field_3605', 'field_3606', 'field_3607', 'field_3608', 'field_3609', 'field_3610', 'field_3611', 'field_3612', 'field_3613', 'field_3614', 'field_3615', 'field_3616', 'field_3617', 'field_3618', 'field_3619', 'field_3620', 'field_3621', 'field_3622', 'field_3623', 'field_3624', 'field_3625', 'field_3626', 'field_3627', 'field_3628', 'field_3629', 'field_3630', 'field_3631', 'field_3632', 'field_3633', 'field_3634', 'field_3635', 'field_3636', 'field_3637', 'field_3638', 'field_3639', 'field_3640', 'field_3641', 'field_3642', 'field_3643', 'field_3644', 'field_3645', 'field_3646', 'field_3647', 'field_3648', 'field_3649', 'field_4140', 'field_3650', 'field_3651', 'field_3652', 'field_3653', 'field_3654', 'field_3655', 'field_3656', 'field_3657', 'field_3658', 'field_3661', 'field_3662', 'field_3663', 'field_3664', 'field_3665', 'field_3666', 'field_3667', 'field_3668', 'field_3669', 'field_3670', 'field_3671', 'field_3672', 'field_3673', 'field_3674', 'field_3675', 'field_3676', 'field_3677', 'field_3678', 'field_3679', 'field_3680'] 
2025-09-05 11:45:11,182 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] configuration_price_matrix in kw: True 
2025-09-05 11:45:11,182 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] configuration_price in kw: True 
2025-09-05 11:45:11,182 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] Extracted price_matrix: 717.74 
2025-09-05 11:45:11,182 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] Extracted price_component: 111.92540000000001 
2025-09-05 11:45:11,533 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:45:11,546 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:11,546 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:11,547 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:11,548 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:11,549 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:11,549 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:11,551 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:11,551 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:11,552 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:11,553 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:11,556 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:11,557 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:11,558 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:11,848 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Found quantity multiplier: 1.0 
2025-09-05 11:45:11,848 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Preview context - price matrix with multiplier: 717.74 * 1.0 = 717.74 
2025-09-05 11:45:11,849 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Component total (BOM lines already include multiplier): 111.92540000000001 
2025-09-05 11:45:11,849 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ===== MODEL OPERATION COST CALCULATION ===== 
2025-09-05 11:45:11,849 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Configuration ID: 74820 
2025-09-05 11:45:11,849 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 11:45:11,849 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Loaded 346 config values 
2025-09-05 11:45:11,849 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 11:45:11,849 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ===== MODEL FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 11:45:11,849 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Key field values - hinge_pickup: 0, hinge_deliver: 3_per_door_loose_drilled 
2025-09-05 11:45:11,852 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Receive Material Time' from field 'Frame Colour' - Cost: $5.6000000000000005 
2025-09-05 11:45:11,853 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'CNC Load / Unload Extrusion & Set Up' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:45:11,855 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'QC & Colour Consitency CommandeX Door Frame' from field 'Frame Colour' - Cost: $3.8919024624 
2025-09-05 11:45:11,856 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Cut CommandeX Door Frame' from field 'Frame Colour' - Cost: $15.42906316 
2025-09-05 11:45:11,857 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Cut Plugh for CommandeX Door Frame' from field 'Frame Colour' - Cost: $6.0834591888 
2025-09-05 11:45:11,858 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'CNC Centre Lock Cut Out' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:45:11,859 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Insert Plugh into CommandeX Door Frame' from field 'Frame Colour' - Cost: $16.058820840000003 
2025-09-05 11:45:11,860 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Assemble CommandeX Door with Crimped Corners' from field 'Frame Colour' - Cost: $33.364561886400004 
2025-09-05 11:45:11,861 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Plugh CommandeX Door with Assembly Press' from field 'Frame Colour' - Cost: $18.5526612528 
2025-09-05 11:45:11,864 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Clamp Product (even when midrail/brace is used) (per Panel)' from field 'Frame Colour' - Cost: $4.48 
2025-09-05 11:45:11,866 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'QC CommandeX Door' from field 'Frame Colour' - Cost: $17.9229035728 
2025-09-05 11:45:11,867 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Cut Mesh or Sheet' from field 'Frame Colour' - Cost: $12.003181380800001 
2025-09-05 11:45:11,873 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Cut Extrusions on Upcut Saw' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:11,874 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up 2' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:11,875 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Extra Time to Cut French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:11,875 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Notch Slots for Centre Lock & French Door Mullion Caps in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:11,876 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Remove Breakaway Sections for Centre Notch & French Door Mullion Cap Notches in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:11,877 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Insert French Door Mullion Mohair (Schlegel PB48753B) into French Door Mullion or T-Section Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:11,877 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Insert Top & Bottom French Door Mullion Caps' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:11,878 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Rivet French Door Mullion onto Lock Door or T-Section Mullion onto Non-Lock Door' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:11,881 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Pick Hardware' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $1.36 
2025-09-05 11:45:11,883 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'QC Picked Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:45:11,884 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Wrap Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:45:11,886 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Install Centre Lock' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $6.48 
2025-09-05 11:45:11,888 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Pick Centre Lock Site Kit' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $1.36 
2025-09-05 11:45:11,889 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'QC Picked Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:45:11,890 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Wrap Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:45:11,900 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Non-Lock Door Centre Striker Cut Out' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $1.36 
2025-09-05 11:45:11,902 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $2.64 
2025-09-05 11:45:11,904 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:45:11,904 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:45:11,908 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 11:45:11,909 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Rivet Flush Bolts into Non-Lock Door' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $18.720000000000002 
2025-09-05 11:45:11,910 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:45:11,912 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:45:11,913 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:45:11,915 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:45:11,917 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:45:11,921 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Extra Time to Cut Extra Mesh Sheet because of Midrail / XB / Mullion' from option 'Yes' in field 'Midrail (Conditional Case 2)' - Cost: $11.36 
2025-09-05 11:45:11,923 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Cut Extrusions on Upcut Saw (Midrail)' from option 'Yes' in field 'Midrail (Conditional Case 2)' - Cost: $4.0 
2025-09-05 11:45:11,924 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Cut Plugh for CommandeX Midrail' from option 'Yes' in field 'Midrail (Conditional Case 2)' - Cost: $2.72 
2025-09-05 11:45:11,925 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Insert Plugh into CommandeX Midrail' from option 'Yes' in field 'Midrail (Conditional Case 2)' - Cost: $3.3600000000000003 
2025-09-05 11:45:11,927 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Extra Time to Assemble Product with CommandeX Midrail' from option 'Yes' in field 'Midrail (Conditional Case 2)' - Cost: $12.64 
2025-09-05 11:45:11,928 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Plugh CommandeX Midrail by Hand' from option 'Yes' in field 'Midrail (Conditional Case 2)' - Cost: $10.56 
2025-09-05 11:45:11,931 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Load Door (Delivery) / Customer Collect (Pick Up)' from field 'Number of Security Hinges (Pick Up)' - Cost: $2.6449822560000005 
2025-09-05 11:45:11,933 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Delivery Time - Time allocated to CD1 even for pick up orders' from field 'Number of Security Hinges (Pick Up)' - Cost: $5.6000000000000005 
2025-09-05 11:45:11,934 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:11,934 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:11,936 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:11,936 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:11,937 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:45:11,938 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:11,939 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:11,940 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:11,940 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:11,941 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:45:11,942 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:11,942 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:11,943 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:11,943 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:11,944 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Pick Hinges' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:45:11,945 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:11,945 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:11,946 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:11,946 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:11,947 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'QC Picked Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:45:11,948 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:11,948 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:11,949 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:11,949 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:11,950 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Wrap Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:45:11,951 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:45:11,953 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:45:11,957 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:11,958 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:11,959 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:11,960 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:11,961 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:11,962 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:11,967 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ===== MODEL FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 11:45:11,967 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Operations found: 51 
2025-09-05 11:45:11,967 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Total cost: $252.75153600000004 
2025-09-05 11:45:11,967 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 11:45:11,967 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ✓ Model operation cost: $252.75153600000004 from 51 operations 
2025-09-05 11:45:11,967 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Operation total (already includes multiplier): 252.75153600000004 
2025-09-05 11:45:11,967 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Configuration 74820 price calculation: 
2025-09-05 11:45:11,967 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Components: $111.92540000000001 
2025-09-05 11:45:11,967 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Operations: $252.75153600000004 
2025-09-05 11:45:11,967 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Sale Price Matrices (with multiplier): $717.74 
2025-09-05 11:45:11,967 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Total: $1082.416936 
2025-09-05 11:45:11,967 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Quantity Multiplier: 1.0 
2025-09-05 11:45:11,967 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - BOM ID: 74790 
2025-09-05 11:45:11,967 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - BOM lines count: 14 
2025-09-05 11:45:12,383 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:45:12,399 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:12,400 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:12,401 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:12,401 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:12,402 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:12,402 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:12,404 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:12,404 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:12,405 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:12,405 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:12,408 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:12,409 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:12,409 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:12,862 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Found quantity multiplier: 1.0 
2025-09-05 11:45:12,862 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Saving configuration - using base price 717.74 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 11:45:12,869 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Component total (BOM lines already include multiplier): 111.92540000000001 
2025-09-05 11:45:12,869 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ===== MODEL OPERATION COST CALCULATION ===== 
2025-09-05 11:45:12,869 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Configuration ID: 74820 
2025-09-05 11:45:12,869 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 11:45:12,869 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Loaded 346 config values 
2025-09-05 11:45:12,869 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 11:45:12,870 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ===== MODEL FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 11:45:12,870 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Key field values - hinge_pickup: 0, hinge_deliver: 3_per_door_loose_drilled 
2025-09-05 11:45:12,873 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Receive Material Time' from field 'Frame Colour' - Cost: $5.6000000000000005 
2025-09-05 11:45:12,874 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'CNC Load / Unload Extrusion & Set Up' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:45:12,877 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'QC & Colour Consitency CommandeX Door Frame' from field 'Frame Colour' - Cost: $3.8919024624 
2025-09-05 11:45:12,878 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Cut CommandeX Door Frame' from field 'Frame Colour' - Cost: $15.42906316 
2025-09-05 11:45:12,880 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Cut Plugh for CommandeX Door Frame' from field 'Frame Colour' - Cost: $6.0834591888 
2025-09-05 11:45:12,882 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'CNC Centre Lock Cut Out' from field 'Frame Colour' - Cost: $2.64 
2025-09-05 11:45:12,884 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Insert Plugh into CommandeX Door Frame' from field 'Frame Colour' - Cost: $16.058820840000003 
2025-09-05 11:45:12,885 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Assemble CommandeX Door with Crimped Corners' from field 'Frame Colour' - Cost: $33.364561886400004 
2025-09-05 11:45:12,886 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Plugh CommandeX Door with Assembly Press' from field 'Frame Colour' - Cost: $18.5526612528 
2025-09-05 11:45:12,889 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Clamp Product (even when midrail/brace is used) (per Panel)' from field 'Frame Colour' - Cost: $4.48 
2025-09-05 11:45:12,891 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'QC CommandeX Door' from field 'Frame Colour' - Cost: $17.9229035728 
2025-09-05 11:45:12,893 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Cut Mesh or Sheet' from field 'Frame Colour' - Cost: $12.003181380800001 
2025-09-05 11:45:12,901 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Cut Extrusions on Upcut Saw' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:12,902 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up 2' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:12,903 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Extra Time to Cut French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:12,904 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Notch Slots for Centre Lock & French Door Mullion Caps in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:12,904 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Remove Breakaway Sections for Centre Notch & French Door Mullion Cap Notches in French Door Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:12,905 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Insert French Door Mullion Mohair (Schlegel PB48753B) into French Door Mullion or T-Section Mullion' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:12,906 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Insert Top & Bottom French Door Mullion Caps' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:12,906 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Rivet French Door Mullion onto Lock Door or T-Section Mullion onto Non-Lock Door' from option 'Yes' in field 'French Door Mullion w/ 2 Caps & Mohair (attached to outside of Lock Door )' - Cost: $0.0 
2025-09-05 11:45:12,910 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Pick Hardware' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $1.36 
2025-09-05 11:45:12,912 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'QC Picked Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:45:12,914 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Wrap Hardware (Cylinder)' from option 'CommandeX 5 Pin' in field 'CommandeX Hinged - Cylinder' - Cost: $0.4 
2025-09-05 11:45:12,917 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Install Centre Lock' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $6.48 
2025-09-05 11:45:12,919 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Pick Centre Lock Site Kit' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $1.36 
2025-09-05 11:45:12,921 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'QC Picked Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:45:12,922 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Wrap Hardware (Lock Site Kit)' from field 'CommandeX Hinged - Lock Type (Top of Cut Out)' - Cost: $0.4 
2025-09-05 11:45:12,937 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Non-Lock Door Centre Striker Cut Out' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $1.36 
2025-09-05 11:45:12,938 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Yes' in field 'Non-Lock Door Striker Cut Outs' - Cost: $2.64 
2025-09-05 11:45:12,941 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:45:12,941 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 11:45:12,947 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 11:45:12,949 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Rivet Flush Bolts into Non-Lock Door' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $18.720000000000002 
2025-09-05 11:45:12,951 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:45:12,952 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:45:12,953 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:45:12,954 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Flush Bolt Hole in Top or Btm' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $1.36 
2025-09-05 11:45:12,956 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Flush Bolt Cut Out' from option 'Top & Bottom Flush Bolts' in field 'Flush Bolts & Patio Bolts (Non-Lock Door)' - Cost: $2.64 
2025-09-05 11:45:12,959 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Extra Time to Cut Extra Mesh Sheet because of Midrail / XB / Mullion' from option 'Yes' in field 'Midrail (Conditional Case 2)' - Cost: $11.36 
2025-09-05 11:45:12,961 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Cut Extrusions on Upcut Saw (Midrail)' from option 'Yes' in field 'Midrail (Conditional Case 2)' - Cost: $4.0 
2025-09-05 11:45:12,963 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Cut Plugh for CommandeX Midrail' from option 'Yes' in field 'Midrail (Conditional Case 2)' - Cost: $2.72 
2025-09-05 11:45:12,965 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Insert Plugh into CommandeX Midrail' from option 'Yes' in field 'Midrail (Conditional Case 2)' - Cost: $3.3600000000000003 
2025-09-05 11:45:12,966 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Extra Time to Assemble Product with CommandeX Midrail' from option 'Yes' in field 'Midrail (Conditional Case 2)' - Cost: $12.64 
2025-09-05 11:45:12,968 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Plugh CommandeX Midrail by Hand' from option 'Yes' in field 'Midrail (Conditional Case 2)' - Cost: $10.56 
2025-09-05 11:45:12,969 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Load Door (Delivery) / Customer Collect (Pick Up)' from field 'Number of Security Hinges (Pick Up)' - Cost: $2.6449822560000005 
2025-09-05 11:45:12,971 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added field mapping operation 'Delivery Time - Time allocated to CD1 even for pick up orders' from field 'Number of Security Hinges (Pick Up)' - Cost: $5.6000000000000005 
2025-09-05 11:45:12,972 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:12,972 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:12,973 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:12,973 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:12,974 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Hinge Holes (Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:45:12,974 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:12,975 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:12,975 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:12,976 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:12,976 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Hinge Holes (Non-Lock Door)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $0.96 
2025-09-05 11:45:12,977 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:12,977 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:12,978 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:12,978 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:12,979 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Pick Hinges' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:45:12,980 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:12,980 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:12,981 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:12,982 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:12,984 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'QC Picked Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:45:12,985 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:12,985 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:12,987 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 11:45:12,987 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 11:45:12,989 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'Wrap Hardware (x Qty of Hinges)' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.4 
2025-09-05 11:45:12,991 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:45:12,993 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] MODEL: Added option mapping operation 'CNC Load / Unload Extrusion & Set Up' from option '3 per Door - Loose with holes drilled (No rivets supplied)' in field 'Number of Security Hinges (Deliver/Site)' - Cost: $2.64 
2025-09-05 11:45:13,001 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:13,003 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:13,005 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:13,006 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:13,008 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:13,009 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 11:45:13,015 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ===== MODEL FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 11:45:13,015 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Operations found: 51 
2025-09-05 11:45:13,015 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Total cost: $252.75153600000004 
2025-09-05 11:45:13,015 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 11:45:13,015 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ✓ Model operation cost: $252.75153600000004 from 51 operations 
2025-09-05 11:45:13,016 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Operation total (already includes multiplier): 252.75153600000004 
2025-09-05 11:45:13,016 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Configuration 74820 price calculation: 
2025-09-05 11:45:13,016 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Components: $111.92540000000001 
2025-09-05 11:45:13,016 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Operations: $252.75153600000004 
2025-09-05 11:45:13,016 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Sale Price Matrices (with multiplier): $717.74 
2025-09-05 11:45:13,016 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Total: $1082.416936 
2025-09-05 11:45:13,016 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Quantity Multiplier: 1.0 
2025-09-05 11:45:13,016 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - BOM ID: 74790 
2025-09-05 11:45:13,016 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - BOM lines count: 14 
2025-09-05 11:45:13,016 28709 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [OPERATION_COSTS_BUG] MAIN.PY: Save config 2 got operation cost $252.75154 from model 
