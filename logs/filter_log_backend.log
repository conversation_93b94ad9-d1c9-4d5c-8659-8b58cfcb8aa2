2025-09-05 08:58:15,586 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 08:58:15,587 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 08:58:15,587 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 08:58:15,588 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 08:58:15,590 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 08:58:16,028 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 08:58:16,068 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:16,069 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 08:58:16,070 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 08:58:16,070 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 08:58:16,069 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:16,070 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 08:58:16,071 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 08:58:16,071 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 08:58:16,071 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 08:58:16,073 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:16,075 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:16,080 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:16,081 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:16,089 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:16,091 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:16,096 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:16,097 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:16,100 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:16,100 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:16,101 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:16,101 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:16,103 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:16,104 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:16,105 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:16,107 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:16,116 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:16,117 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:16,118 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:16,189 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:16,193 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:16,196 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:16,198 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:16,201 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:16,202 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:16,307 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 08:58:16,313 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 08:58:16,336 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:16,337 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:16,339 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:16,339 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:16,343 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:16,344 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:16,346 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:16,347 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:16,354 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:16,355 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:16,359 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:16,360 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:16,364 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:16,364 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:16,369 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:16,371 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:16,435 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:16,454 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:16,473 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:16,485 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:16,538 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:16,539 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:16,562 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:16,575 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:16,589 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:16,590 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:16,592 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:16,593 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:16,602 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:16,605 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:16,612 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:16,615 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:16,629 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:16,633 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:16,636 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:16,638 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:16,687 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 08:58:16,692 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 41 
2025-09-05 08:58:16,694 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $95.92 
2025-09-05 08:58:16,694 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 08:58:16,694 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 08:58:16,701 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 from 41 operations 
2025-09-05 08:58:16,702 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 
2025-09-05 08:58:17,556 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 08:58:17,562 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:17,562 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:17,563 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:17,564 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:17,564 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:17,565 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:17,566 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:17,566 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:17,570 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:17,570 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:17,572 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:17,573 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:17,574 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:17,574 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:17,575 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:17,575 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:17,577 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:17,577 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:17,581 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:17,582 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:17,583 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:27,217 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 08:58:27,217 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 08:58:27,217 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 08:58:27,217 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 08:58:27,217 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 08:58:27,754 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 08:58:27,754 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 08:58:27,755 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 08:58:27,755 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 08:58:27,755 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 08:58:27,756 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 08:58:27,756 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 08:58:27,840 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:27,842 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:27,845 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:27,846 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:27,848 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:27,849 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:27,940 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 08:58:27,950 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 08:58:27,978 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:27,978 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:27,980 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:27,980 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:27,982 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:27,982 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:27,983 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:27,983 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:27,985 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:27,985 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:27,987 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:27,987 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:27,989 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:27,989 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:27,994 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:27,995 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:28,003 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:28,004 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:28,005 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:28,005 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:28,008 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:28,009 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:28,009 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:28,010 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:28,012 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:28,013 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:28,014 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:28,015 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:28,018 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:28,019 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:28,021 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:28,021 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:28,024 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:28,024 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:28,026 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:28,027 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:28,051 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 08:58:28,052 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 41 
2025-09-05 08:58:28,052 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $95.92 
2025-09-05 08:58:28,052 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 08:58:28,052 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 08:58:28,052 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 from 41 operations 
2025-09-05 08:58:28,052 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 
2025-09-05 08:58:28,269 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 08:58:28,278 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:28,278 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:28,280 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:28,280 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:28,281 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:28,282 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:28,284 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:28,284 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:28,288 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:28,288 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:28,289 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:28,289 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:28,290 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:28,290 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:28,291 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:28,292 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:28,293 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:28,293 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:28,297 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:28,299 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:28,300 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:31,709 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 08:58:31,709 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 08:58:31,710 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 08:58:31,710 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 08:58:31,711 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 08:58:32,368 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 08:58:32,369 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 08:58:32,369 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 08:58:32,369 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 08:58:32,370 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 08:58:32,370 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 08:58:32,370 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 08:58:32,485 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:32,489 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:32,492 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:32,493 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:32,495 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:32,497 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:32,612 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 08:58:32,622 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 08:58:32,651 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:32,652 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:32,655 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:32,655 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:32,659 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:32,660 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:32,663 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:32,663 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:32,666 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:32,668 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:32,671 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:32,671 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:32,674 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:32,675 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:32,676 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:32,677 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:32,687 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:32,687 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:32,688 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:32,689 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:32,692 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:32,693 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:32,694 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:32,695 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:32,698 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:32,699 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:32,700 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:32,701 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:32,703 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:32,703 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:32,706 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:32,707 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:32,710 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:32,710 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:32,712 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:32,713 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:32,744 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 08:58:32,745 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 41 
2025-09-05 08:58:32,745 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $95.92 
2025-09-05 08:58:32,745 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 08:58:32,745 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 08:58:32,759 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 from 41 operations 
2025-09-05 08:58:32,759 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 
2025-09-05 08:58:33,147 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 08:58:33,156 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:33,156 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:33,158 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:33,159 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:33,160 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:33,160 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:33,161 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:33,162 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:33,165 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:33,165 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:33,166 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:33,166 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:33,167 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:33,168 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:33,169 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:33,169 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:33,170 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:33,170 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:33,175 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:33,176 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:33,177 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:53,114 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 08:58:53,114 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 08:58:53,114 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 08:58:53,114 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 08:58:53,115 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 08:58:53,662 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 08:58:53,663 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 08:58:53,663 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 08:58:53,664 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 08:58:53,664 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 08:58:53,665 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 08:58:53,665 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 08:58:53,773 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:53,776 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:53,778 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:53,780 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:53,782 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:53,783 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:53,861 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 08:58:53,867 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 08:58:53,884 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:53,884 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:53,886 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:53,887 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:53,891 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:53,891 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:53,893 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:53,894 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:53,896 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:53,897 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:53,898 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:53,899 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:53,901 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:53,901 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:53,903 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:53,903 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:53,910 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:53,911 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:53,912 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:53,913 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:53,914 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:53,914 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:53,915 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:53,916 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:53,920 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:53,920 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:53,921 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:53,921 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:53,923 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:53,924 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:53,925 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:53,925 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:53,927 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:53,928 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:53,928 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:53,929 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:53,950 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 08:58:53,951 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 50 
2025-09-05 08:58:53,951 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $221.871536 
2025-09-05 08:58:53,951 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 08:58:53,951 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 08:58:53,952 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 from 50 operations 
2025-09-05 08:58:53,952 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 
2025-09-05 08:58:54,154 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 08:58:54,163 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:54,164 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:54,166 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:54,166 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:54,167 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:54,167 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:54,170 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:54,170 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:54,175 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:54,175 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:54,177 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:54,177 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:54,179 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:54,179 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:54,180 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:54,181 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:54,182 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:58:54,182 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:58:54,187 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:54,188 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:54,189 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:58:59,447 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 08:58:59,447 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 08:58:59,447 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 08:58:59,448 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 08:58:59,449 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 08:58:59,721 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 08:58:59,721 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 08:58:59,721 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 08:58:59,721 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 08:58:59,721 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 08:58:59,721 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 08:58:59,721 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 08:59:00,020 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:00,024 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:00,027 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:00,028 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:00,030 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:00,031 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:00,125 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 08:59:00,134 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 08:59:00,153 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:00,154 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:00,156 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:00,156 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:00,158 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:00,159 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:00,160 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:00,160 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:00,161 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:00,162 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:00,163 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:00,164 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:00,166 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:00,166 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:00,168 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:00,168 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:00,176 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:00,177 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:00,178 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:00,178 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:00,181 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:00,181 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:00,184 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:00,185 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:00,188 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:00,189 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:00,190 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:00,190 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:00,193 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:00,194 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:00,195 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:00,196 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:00,198 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:00,199 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:00,200 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:00,201 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:00,232 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 08:59:00,232 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 50 
2025-09-05 08:59:00,232 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $221.871536 
2025-09-05 08:59:00,232 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 08:59:00,232 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 08:59:00,233 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 from 50 operations 
2025-09-05 08:59:00,233 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 
2025-09-05 08:59:01,285 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 08:59:01,294 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:01,295 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:01,297 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:01,298 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:01,299 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:01,299 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:01,301 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:01,301 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:01,307 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:01,307 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:01,309 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:01,309 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:01,310 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:01,310 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:01,312 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:01,313 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:01,314 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:01,314 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:01,320 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:01,321 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:01,322 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:07,039 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 08:59:07,039 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 08:59:07,039 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 08:59:07,039 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 08:59:07,042 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 08:59:07,869 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 08:59:07,869 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 08:59:07,869 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 08:59:07,870 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 08:59:07,870 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 08:59:07,871 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 08:59:07,871 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 08:59:07,980 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:07,983 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:07,985 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:07,987 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:07,989 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:07,991 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:08,106 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 08:59:08,113 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 08:59:08,145 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:08,145 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:08,148 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:08,149 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:08,152 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:08,152 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:08,154 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:08,155 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:08,157 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:08,158 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:08,161 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:08,162 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:08,165 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:08,166 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:08,167 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:08,168 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:08,178 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:08,179 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:08,180 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:08,181 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:08,183 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:08,184 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:08,185 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:08,186 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:08,189 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:08,190 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:08,192 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:08,193 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:08,198 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:08,199 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:08,201 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:08,201 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:08,205 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:08,206 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:08,208 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:08,209 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:08,258 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 08:59:08,259 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 50 
2025-09-05 08:59:08,259 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $221.871536 
2025-09-05 08:59:08,259 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 08:59:08,259 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 08:59:08,261 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 from 50 operations 
2025-09-05 08:59:08,261 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 
2025-09-05 08:59:09,275 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 08:59:09,459 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:09,460 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:09,469 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:09,470 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:09,473 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:09,475 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:09,480 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:09,481 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:09,490 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:09,491 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:09,493 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:09,494 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:09,496 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:09,497 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:09,499 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:09,499 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:09,502 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 08:59:09,502 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:09,511 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:09,513 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:09,515 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:25,605 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 08:59:25,605 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 08:59:25,605 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 08:59:25,605 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 08:59:25,606 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 08:59:26,109 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 08:59:26,109 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 08:59:26,109 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 08:59:26,109 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 08:59:26,109 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 08:59:26,109 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 08:59:26,109 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 08:59:26,248 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:26,252 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:26,255 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:26,256 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:26,259 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:26,261 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:26,400 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 08:59:26,412 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 08:59:26,444 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:26,445 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:26,448 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:26,449 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:26,452 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:26,453 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:26,454 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:26,455 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:26,457 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:26,458 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:26,460 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:26,461 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:26,464 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:26,464 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:26,466 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:26,466 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:26,469 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:26,469 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:26,471 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:26,472 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:26,515 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 08:59:26,515 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 44 
2025-09-05 08:59:26,515 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $203.631536 
2025-09-05 08:59:26,515 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 08:59:26,515 251887 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 08:59:26,515 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $203.631536 from 44 operations 
2025-09-05 08:59:26,516 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $203.631536 
2025-09-05 08:59:29,727 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 08:59:29,756 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:29,756 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:29,759 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:29,760 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:29,762 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:29,762 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:29,765 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:29,765 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:29,767 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:29,767 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:29,772 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:29,774 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:29,776 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:50,708 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] Received parameters: ['template_id', 'product_id', 'order_line_id', 'config_data', 'configuration_price', 'configuration_price_matrix', 'total_price', 'field_3451', 'field_3452', 'field_3453', 'field_3454', 'field_3455', 'field_3456', 'field_3457', 'field_3458', 'field_3459', 'field_3460', 'field_3461', 'field_3462', 'field_3463', 'field_3464', 'field_3465', 'field_3466', 'field_3467', 'field_3468', 'field_3469', 'field_3470', 'field_3471', 'field_3472', 'field_3473', 'field_3474', 'field_3475', 'field_3476', 'field_3477', 'field_3478', 'field_3479', 'field_3480', 'field_3481', 'field_3482', 'field_3483', 'field_3484', 'field_3485', 'field_3486', 'field_3487', 'field_3488', 'field_3489', 'field_3490', 'field_3491', 'field_3492', 'field_3493', 'field_3494', 'field_3495', 'field_3496', 'field_3497', 'field_3498', 'field_3499', 'field_3500', 'field_3501', 'field_3502', 'field_3503', 'field_3504', 'field_3505', 'field_3506', 'field_3507', 'field_3508', 'field_3509', 'field_3510', 'field_3511', 'field_3512', 'field_3513', 'field_3514', 'field_3515', 'field_3516', 'field_3517', 'field_3518', 'field_3519', 'field_3520', 'field_3521', 'field_3522', 'field_3523', 'field_3524', 'field_3525', 'field_3526', 'field_3527', 'field_3528', 'field_3529', 'field_3530', 'field_3531', 'field_3532', 'field_3533', 'field_3534', 'field_3535', 'field_3536', 'field_3537', 'field_3538', 'field_3539', 'field_3540', 'field_3541', 'field_3542', 'field_3543', 'field_3544', 'field_3545', 'field_3546', 'field_3547', 'field_3548', 'field_3549', 'field_3550', 'field_3551', 'field_3552', 'field_3553', 'field_3554', 'field_3555', 'field_3556', 'field_3557', 'field_3558', 'field_3559', 'field_3560', 'field_3561', 'field_3562', 'field_3563', 'field_3564', 'field_3565', 'field_3566', 'field_3567', 'field_3568', 'field_3569', 'field_3570', 'field_3571', 'field_3572', 'field_3573', 'field_3574', 'field_3575', 'field_3576', 'field_3577', 'field_3578', 'field_3579', 'field_3580', 'field_3581', 'field_3582', 'field_3585', 'field_3586', 'field_3583', 'field_3584', 'field_3587', 'field_3588', 'field_3589', 'field_3590', 'field_3591', 'field_3592', 'field_3593', 'field_3594', 'field_3595', 'field_3596', 'field_3597', 'field_3598', 'field_3599', 'field_3600', 'field_3601', 'field_3602', 'field_3603', 'field_3604', 'field_3605', 'field_3606', 'field_3607', 'field_3608', 'field_3609', 'field_3610', 'field_3611', 'field_3612', 'field_3613', 'field_3614', 'field_3615', 'field_3616', 'field_3617', 'field_3618', 'field_3619', 'field_3620', 'field_3621', 'field_3622', 'field_3623', 'field_3624', 'field_3625', 'field_3626', 'field_3627', 'field_3628', 'field_3629', 'field_3630', 'field_3631', 'field_3632', 'field_3633', 'field_3634', 'field_3635', 'field_3636', 'field_3637', 'field_3638', 'field_3639', 'field_3640', 'field_3641', 'field_3642', 'field_3643', 'field_3644', 'field_3645', 'field_3646', 'field_3647', 'field_3648', 'field_3649', 'field_4140', 'field_3650', 'field_3651', 'field_3652', 'field_3653', 'field_3654', 'field_3655', 'field_3656', 'field_3657', 'field_3658', 'field_3661', 'field_3662', 'field_3663', 'field_3664', 'field_3665', 'field_3666', 'field_3667', 'field_3668', 'field_3669', 'field_3670', 'field_3671', 'field_3672', 'field_3673', 'field_3674', 'field_3675', 'field_3676', 'field_3677', 'field_3678', 'field_3679', 'field_3680'] 
2025-09-05 08:59:50,709 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] configuration_price_matrix in kw: True 
2025-09-05 08:59:50,709 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] configuration_price in kw: True 
2025-09-05 08:59:50,709 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] Extracted price_matrix: 717.74 
2025-09-05 08:59:50,710 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] Extracted price_component: 111.92540000000001 
2025-09-05 08:59:51,728 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 08:59:51,772 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:51,773 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:51,778 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:51,778 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:51,781 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:51,781 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:51,786 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:51,787 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:51,789 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:51,789 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:51,797 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:51,800 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:51,803 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:52,824 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Found quantity multiplier: 1.0 
2025-09-05 08:59:52,824 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Preview context - price matrix with multiplier: 717.74 * 1.0 = 717.74 
2025-09-05 08:59:52,825 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Component total (BOM lines already include multiplier): 111.92540000000001 
2025-09-05 08:59:52,826 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Found quantity multiplier: 1.0 
2025-09-05 08:59:52,826 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Found 53 operations in BOM 74660 
2025-09-05 08:59:52,830 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Receive Material Time' base price: $5.6000000000000005 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,833 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Load / Unload Extrusion & Set Up' base price: $2.64 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,834 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'QC & Colour Consitency CommandeX Door Frame' base price: $3.8919024624 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,836 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Cut CommandeX Door Frame' base price: $15.42906316 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,838 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Cut Plugh for CommandeX Door Frame' base price: $6.0834591888 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,840 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Centre Lock Cut Out' base price: $2.64 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,841 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Insert Plugh into CommandeX Door Frame' base price: $16.058820840000003 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,842 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Assemble CommandeX Door with Crimped Corners' base price: $33.364561886400004 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,844 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Plugh CommandeX Door with Assembly Press' base price: $18.5526612528 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,845 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'QC CommandeX Door' base price: $17.9229035728 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,847 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Cut Mesh or Sheet' base price: $12.003181380800001 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,847 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Cut Extrusions on Upcut Saw' base price: $0.0 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,847 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Load / Unload Extrusion & Set Up 2' base price: $0.0 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,847 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Extra Time to Cut French Door Mullion' base price: $0.0 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,848 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Notch Slots for Centre Lock & French Door Mullion Caps in French Door Mullion' base price: $0.0 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,848 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Remove Breakaway Sections for Centre Notch & French Door Mullion Cap Notches in French Door Mullion' base price: $0.0 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,848 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Insert French Door Mullion Mohair (Schlegel PB48753B) into French Door Mullion or T-Section Mullion' base price: $0.0 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,848 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Insert Top & Bottom French Door Mullion Caps' base price: $0.0 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,848 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Rivet French Door Mullion onto Lock Door or T-Section Mullion onto Non-Lock Door' base price: $0.0 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,851 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Pick Hardware' base price: $1.36 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,853 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'QC Picked Hardware (Cylinder)' base price: $0.4 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,856 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Wrap Hardware (Cylinder)' base price: $0.4 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,858 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Install Centre Lock' base price: $6.48 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,860 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Pick Centre Lock Site Kit' base price: $1.36 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,862 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'QC Picked Hardware (Lock Site Kit)' base price: $0.4 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,866 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Wrap Hardware (Lock Site Kit)' base price: $0.4 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,870 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Non-Lock Door Centre Striker Cut Out' base price: $1.36 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,877 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Load / Unload Extrusion & Set Up' base price: $2.64 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,880 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Flush Bolt Cut Out' base price: $2.64 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,889 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 08:59:52,891 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Rivet Flush Bolts into Non-Lock Door' base price: $18.720000000000002 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,894 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Load / Unload Extrusion & Set Up' base price: $2.64 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,897 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Load / Unload Extrusion & Set Up' base price: $2.64 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,901 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Flush Bolt Hole in Top or Btm' base price: $1.36 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,904 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Flush Bolt Hole in Top or Btm' base price: $1.36 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,908 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Flush Bolt Cut Out' base price: $2.64 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,915 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Extra Time to Cut Extra Mesh Sheet because of Midrail / XB / Mullion' base price: $11.36 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,920 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Cut Extrusions on Upcut Saw (Midrail)' base price: $4.0 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,923 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Cut Plugh for CommandeX Midrail' base price: $2.72 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,925 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Insert Plugh into CommandeX Midrail' base price: $3.3600000000000003 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,928 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Extra Time to Assemble Product with CommandeX Midrail' base price: $12.64 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,930 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Plugh CommandeX Midrail by Hand' base price: $10.56 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,931 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Load Door (Delivery) / Customer Collect (Pick Up)' base price: $2.6449822560000005 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,934 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Delivery Time - Time allocated to CD1 even for pick up orders' base price: $5.6000000000000005 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,935 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:52,935 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:52,937 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Hinge Holes (Lock Door)' base price: $0.96 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,938 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:52,938 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:52,940 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Hinge Holes (Non-Lock Door)' base price: $0.96 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,941 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:52,941 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:52,943 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Pick Hinges' base price: $2.4 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,944 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:52,944 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:52,947 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'QC Picked Hardware (x Qty of Hinges)' base price: $2.4 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,948 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:52,949 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:52,951 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Wrap Hardware (x Qty of Hinges)' base price: $2.4 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,953 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Load / Unload Extrusion & Set Up' base price: $2.64 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,955 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Load / Unload Extrusion & Set Up' base price: $2.64 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,956 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:52,958 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Pick Hardware (Security Hinge Packers)' base price: $0.0 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,959 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:52,961 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Wrap Picked Hardware (Security Hinge Packers)' base price: $0.0 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,962 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:52,964 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'QC Picked Hardware (Security Hinge Packers)' base price: $0.0 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,965 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - total operation price (base): $248.27153600000003 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:52,965 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Operation total (already includes multiplier): 248.27153600000003 
2025-09-05 08:59:52,965 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Configuration 74690 price calculation: 
2025-09-05 08:59:52,965 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Components: $111.92540000000001 
2025-09-05 08:59:52,965 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Operations: $248.27153600000003 
2025-09-05 08:59:52,965 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Sale Price Matrices (with multiplier): $717.74 
2025-09-05 08:59:52,965 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Total: $1077.936936 
2025-09-05 08:59:52,965 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Quantity Multiplier: 1.0 
2025-09-05 08:59:52,965 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - BOM ID: 74660 
2025-09-05 08:59:52,965 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - BOM lines count: 14 
2025-09-05 08:59:54,397 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 08:59:54,443 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:54,444 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:54,448 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:54,451 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:54,455 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:54,457 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:54,461 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:54,462 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:54,464 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:54,465 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:54,476 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:54,482 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:54,485 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:55,676 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Found quantity multiplier: 1.0 
2025-09-05 08:59:55,676 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Saving configuration - using base price 717.74 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,689 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Component total (BOM lines already include multiplier): 111.92540000000001 
2025-09-05 08:59:55,689 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Found quantity multiplier: 1.0 
2025-09-05 08:59:55,690 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Found 53 operations in BOM 74660 
2025-09-05 08:59:55,694 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Receive Material Time' base price: $5.6000000000000005 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,696 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Load / Unload Extrusion & Set Up' base price: $2.64 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,702 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'QC & Colour Consitency CommandeX Door Frame' base price: $3.8919024624 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,705 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Cut CommandeX Door Frame' base price: $15.42906316 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,709 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Cut Plugh for CommandeX Door Frame' base price: $6.0834591888 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,712 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Centre Lock Cut Out' base price: $2.64 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,716 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Insert Plugh into CommandeX Door Frame' base price: $16.058820840000003 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,720 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Assemble CommandeX Door with Crimped Corners' base price: $33.364561886400004 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,726 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Plugh CommandeX Door with Assembly Press' base price: $18.5526612528 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,729 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'QC CommandeX Door' base price: $17.9229035728 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,733 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Cut Mesh or Sheet' base price: $12.003181380800001 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,734 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Cut Extrusions on Upcut Saw' base price: $0.0 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,735 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Load / Unload Extrusion & Set Up 2' base price: $0.0 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,735 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Extra Time to Cut French Door Mullion' base price: $0.0 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,736 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Notch Slots for Centre Lock & French Door Mullion Caps in French Door Mullion' base price: $0.0 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,736 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Remove Breakaway Sections for Centre Notch & French Door Mullion Cap Notches in French Door Mullion' base price: $0.0 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,736 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Insert French Door Mullion Mohair (Schlegel PB48753B) into French Door Mullion or T-Section Mullion' base price: $0.0 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,736 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Insert Top & Bottom French Door Mullion Caps' base price: $0.0 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,737 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Rivet French Door Mullion onto Lock Door or T-Section Mullion onto Non-Lock Door' base price: $0.0 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,743 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Pick Hardware' base price: $1.36 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,746 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'QC Picked Hardware (Cylinder)' base price: $0.4 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,749 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Wrap Hardware (Cylinder)' base price: $0.4 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,755 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Install Centre Lock' base price: $6.48 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,759 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Pick Centre Lock Site Kit' base price: $1.36 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,761 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'QC Picked Hardware (Lock Site Kit)' base price: $0.4 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,765 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Wrap Hardware (Lock Site Kit)' base price: $0.4 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,768 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Non-Lock Door Centre Striker Cut Out' base price: $1.36 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,772 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Load / Unload Extrusion & Set Up' base price: $2.64 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,779 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Flush Bolt Cut Out' base price: $2.64 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,791 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 08:59:55,794 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Rivet Flush Bolts into Non-Lock Door' base price: $18.720000000000002 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,798 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Load / Unload Extrusion & Set Up' base price: $2.64 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,804 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Load / Unload Extrusion & Set Up' base price: $2.64 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,806 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Flush Bolt Hole in Top or Btm' base price: $1.36 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,808 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Flush Bolt Hole in Top or Btm' base price: $1.36 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,810 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Flush Bolt Cut Out' base price: $2.64 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,813 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Extra Time to Cut Extra Mesh Sheet because of Midrail / XB / Mullion' base price: $11.36 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,817 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Cut Extrusions on Upcut Saw (Midrail)' base price: $4.0 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,821 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Cut Plugh for CommandeX Midrail' base price: $2.72 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,823 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Insert Plugh into CommandeX Midrail' base price: $3.3600000000000003 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,826 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Extra Time to Assemble Product with CommandeX Midrail' base price: $12.64 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,828 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Plugh CommandeX Midrail by Hand' base price: $10.56 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,832 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Load Door (Delivery) / Customer Collect (Pick Up)' base price: $2.6449822560000005 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,836 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Delivery Time - Time allocated to CD1 even for pick up orders' base price: $5.6000000000000005 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,839 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:55,840 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:55,842 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Hinge Holes (Lock Door)' base price: $0.96 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,843 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:55,844 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:55,846 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Hinge Holes (Non-Lock Door)' base price: $0.96 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,847 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:55,848 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:55,850 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Pick Hinges' base price: $2.4 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,851 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:55,852 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:55,855 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'QC Picked Hardware (x Qty of Hinges)' base price: $2.4 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,856 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 08:59:55,857 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 08:59:55,859 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Wrap Hardware (x Qty of Hinges)' base price: $2.4 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,861 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Load / Unload Extrusion & Set Up' base price: $2.64 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,864 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'CNC Load / Unload Extrusion & Set Up' base price: $2.64 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,865 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:55,868 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Pick Hardware (Security Hinge Packers)' base price: $0.0 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,870 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:55,873 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'Wrap Picked Hardware (Security Hinge Packers)' base price: $0.0 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,874 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 08:59:55,877 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - operation 'QC Picked Hardware (Security Hinge Packers)' base price: $0.0 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,877 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_CALC] Saving configuration - total operation price (base): $248.27153600000003 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 08:59:55,878 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Operation total (already includes multiplier): 248.27153600000003 
2025-09-05 08:59:55,878 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Configuration 74690 price calculation: 
2025-09-05 08:59:55,878 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Components: $111.92540000000001 
2025-09-05 08:59:55,878 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Operations: $248.27153600000003 
2025-09-05 08:59:55,878 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Sale Price Matrices (with multiplier): $717.74 
2025-09-05 08:59:55,878 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Total: $1077.936936 
2025-09-05 08:59:55,878 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Quantity Multiplier: 1.0 
2025-09-05 08:59:55,878 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - BOM ID: 74660 
2025-09-05 08:59:55,878 251887 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - BOM lines count: 14 
