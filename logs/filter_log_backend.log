2025-09-05 10:43:41,628 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 10:43:41,629 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 10:43:41,630 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 10:43:41,630 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 10:43:41,632 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 10:43:41,942 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 10:43:41,942 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 10:43:41,942 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 10:43:41,943 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 10:43:41,943 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 10:43:41,943 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 10:43:41,944 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 10:43:42,027 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:43:42,028 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:43:42,030 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:43:42,031 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:43:42,032 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:43:42,033 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:43:42,291 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 10:43:42,307 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 10:43:42,370 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:42,375 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:42,380 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:42,380 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:42,385 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:42,386 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:42,392 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:42,393 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:42,399 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:42,401 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:42,404 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:42,405 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:42,409 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:42,409 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:42,413 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:42,413 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:42,440 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:42,441 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:42,459 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:42,464 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:42,469 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:42,470 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:42,472 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:42,473 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:42,475 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:42,476 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:42,477 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:42,478 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:42,481 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:42,483 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:42,485 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:42,487 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:42,494 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:42,495 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:42,497 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:42,499 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:42,542 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 10:43:42,544 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 41 
2025-09-05 10:43:42,546 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $95.92 
2025-09-05 10:43:42,547 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 10:43:42,547 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 10:43:42,550 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 from 41 operations 
2025-09-05 10:43:42,552 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 
2025-09-05 10:43:42,654 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 10:43:42,667 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:42,668 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:42,670 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:42,671 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:42,672 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:42,673 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:42,675 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:42,676 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:42,681 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:42,682 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:42,684 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:42,684 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:42,686 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:42,687 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:42,688 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:42,689 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:42,691 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:42,692 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:42,702 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:43:42,703 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:43:42,705 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:43:43,572 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 10:43:43,608 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:43,609 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:43,616 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:43,617 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:43,622 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:43,622 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:43,628 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:43,628 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:43,642 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:43,645 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:43,648 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:43,649 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:43,653 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:43,654 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:43,657 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:43,657 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:43,661 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:43:43,661 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:43:43,684 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:43:43,689 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:43:43,693 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:09,739 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 10:44:09,739 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 10:44:09,739 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 10:44:09,739 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 10:44:09,740 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 10:44:10,244 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 10:44:10,244 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 10:44:10,244 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 10:44:10,244 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 10:44:10,245 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 10:44:10,245 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 10:44:10,245 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 10:44:10,395 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:10,401 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:10,407 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:10,409 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:10,413 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:10,416 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:10,602 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 10:44:10,609 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 10:44:10,634 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:10,634 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:10,638 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:10,640 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:10,645 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:10,647 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:10,651 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:10,653 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:10,656 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:10,656 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:10,658 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:10,659 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:10,661 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:10,661 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:10,664 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:10,664 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:10,674 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:10,676 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:10,677 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:10,678 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:10,680 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:10,680 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:10,683 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:10,684 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:10,686 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:10,687 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:10,689 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:10,690 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:10,694 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:10,695 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:10,696 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:10,697 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:10,700 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:10,701 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:10,702 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:10,702 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:10,754 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 10:44:10,758 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 41 
2025-09-05 10:44:10,758 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $95.92 
2025-09-05 10:44:10,766 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 10:44:10,766 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 10:44:10,775 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 from 41 operations 
2025-09-05 10:44:10,776 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 
2025-09-05 10:44:11,132 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 10:44:11,138 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:11,139 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:11,140 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:11,140 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:11,141 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:11,141 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:11,142 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:11,143 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:11,146 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:11,146 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:11,147 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:11,147 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:11,148 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:11,148 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:11,149 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:11,150 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:11,151 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:11,151 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:11,154 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:11,155 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:11,156 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:13,852 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 10:44:13,853 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 10:44:13,853 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 10:44:13,853 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 10:44:13,853 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 10:44:14,778 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 10:44:14,779 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 10:44:14,781 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 10:44:14,782 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 10:44:14,782 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 10:44:14,783 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 10:44:14,783 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 10:44:14,922 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:14,925 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:14,928 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:14,930 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:14,933 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:14,935 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:15,091 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 10:44:15,104 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 10:44:15,142 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:15,143 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:15,146 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:15,146 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:15,150 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:15,152 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:15,154 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:15,155 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:15,158 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:15,159 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:15,163 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:15,164 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:15,166 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:15,167 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:15,170 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:15,172 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:15,184 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:15,185 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:15,187 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:15,188 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:15,194 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:15,195 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:15,196 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:15,197 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:15,200 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:15,200 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:15,201 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:15,202 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:15,206 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:15,207 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:15,208 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:15,209 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:15,212 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:15,213 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:15,215 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:15,217 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:15,263 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 10:44:15,264 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 41 
2025-09-05 10:44:15,264 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $95.92 
2025-09-05 10:44:15,264 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 10:44:15,264 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 10:44:15,264 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 from 41 operations 
2025-09-05 10:44:15,264 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $95.92 
2025-09-05 10:44:15,472 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 10:44:15,490 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:15,491 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:15,494 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:15,495 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:15,497 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:15,498 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:15,502 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:15,502 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:15,507 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:15,508 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:15,510 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:15,510 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:15,513 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:15,514 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:15,517 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:15,518 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:15,521 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:15,521 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:15,527 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:15,529 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:15,530 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:23,589 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 10:44:23,590 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 10:44:23,590 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 10:44:23,590 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 10:44:23,590 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 10:44:24,637 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 10:44:24,639 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 10:44:24,639 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 10:44:24,639 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 10:44:24,640 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 10:44:24,641 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 10:44:24,641 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 10:44:24,802 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:24,805 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:24,808 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:24,810 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:24,813 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:24,815 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:24,937 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 10:44:24,944 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 10:44:24,977 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:24,978 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:24,983 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:24,983 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:24,986 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:24,987 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:24,989 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:24,989 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:24,993 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:24,994 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:24,998 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:25,000 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:25,002 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:25,002 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:25,005 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:25,007 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:25,021 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:25,022 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:25,023 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:25,024 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:25,026 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:25,027 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:25,028 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:25,029 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:25,032 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:25,035 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:25,040 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:25,041 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:25,045 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:25,046 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:25,051 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:25,052 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:25,055 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:25,056 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:25,058 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:25,059 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:25,108 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 10:44:25,110 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 50 
2025-09-05 10:44:25,110 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $221.871536 
2025-09-05 10:44:25,110 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 10:44:25,110 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 10:44:25,110 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 from 50 operations 
2025-09-05 10:44:25,110 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 
2025-09-05 10:44:25,289 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 10:44:25,296 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:25,296 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:25,298 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:25,299 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:25,300 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:25,301 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:25,303 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:25,303 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:25,306 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:25,307 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:25,307 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:25,308 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:25,309 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:25,309 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:25,311 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:25,311 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:25,312 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:25,313 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:25,318 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:25,319 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:25,320 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:27,621 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 10:44:27,622 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 10:44:27,622 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 10:44:27,622 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 10:44:27,623 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 10:44:30,164 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 10:44:30,165 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 10:44:30,166 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 10:44:30,167 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 10:44:30,168 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 10:44:30,169 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 10:44:30,169 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 10:44:30,330 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:30,334 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:30,337 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:30,341 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:30,346 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:30,349 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:30,460 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 10:44:30,467 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 10:44:30,487 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:30,488 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:30,491 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:30,491 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:30,493 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:30,494 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:30,496 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:30,497 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:30,500 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:30,500 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:30,503 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:30,504 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:30,506 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:30,507 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:30,514 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:30,515 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:30,526 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:30,526 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:30,528 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:30,529 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:30,531 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:30,532 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:30,534 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:30,535 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:30,536 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:30,537 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:30,538 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:30,539 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:30,541 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:30,543 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:30,546 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:30,546 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:30,549 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:30,550 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:30,551 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:30,552 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:30,575 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 10:44:30,575 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 50 
2025-09-05 10:44:30,575 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $221.871536 
2025-09-05 10:44:30,575 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 10:44:30,575 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 10:44:30,576 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 from 50 operations 
2025-09-05 10:44:30,576 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 
2025-09-05 10:44:30,745 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 10:44:30,753 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:30,753 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:30,755 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:30,755 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:30,756 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:30,757 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:30,758 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:30,759 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:30,762 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:30,762 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:30,763 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:30,764 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:30,765 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:30,766 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:30,767 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:30,767 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:30,769 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:30,769 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:30,773 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:30,774 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:30,775 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:32,076 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 10:44:32,076 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 10:44:32,076 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 10:44:32,077 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 10:44:32,077 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 10:44:33,506 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 10:44:33,506 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 10:44:33,507 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 10:44:33,507 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 10:44:33,508 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 10:44:33,508 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 10:44:33,509 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 10:44:33,661 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:33,664 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:33,666 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:33,668 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:33,671 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:33,672 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:33,796 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 10:44:33,805 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 10:44:33,834 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:33,835 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:33,838 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:33,839 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:33,842 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:33,843 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:33,844 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:33,846 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:33,849 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:33,849 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:33,852 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:33,853 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:33,856 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:33,856 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:33,860 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:33,860 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:33,873 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:33,874 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:33,876 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:33,876 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:33,880 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:33,880 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:33,882 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:33,882 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:33,885 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:33,885 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:33,887 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:33,888 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:33,891 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:33,891 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:33,892 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:33,893 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:33,895 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:33,896 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:33,896 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:33,897 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:33,945 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 10:44:33,945 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 50 
2025-09-05 10:44:33,945 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $221.871536 
2025-09-05 10:44:33,946 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 10:44:33,947 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 10:44:33,947 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 from 50 operations 
2025-09-05 10:44:33,947 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 
2025-09-05 10:44:34,538 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 10:44:34,642 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:34,642 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:34,644 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:34,644 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:34,646 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:34,646 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:34,648 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:34,649 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:34,669 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:34,670 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:34,671 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:34,672 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:34,673 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:34,673 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:34,674 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:34,675 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:34,676 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:34,676 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:34,680 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:34,681 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:34,682 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:38,493 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 10:44:38,493 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 10:44:38,493 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 10:44:38,493 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 10:44:38,494 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 10:44:39,117 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 10:44:39,117 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 10:44:39,117 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 10:44:39,117 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 10:44:39,117 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 10:44:39,117 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 10:44:39,117 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 10:44:39,216 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:39,219 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:39,221 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:39,223 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:39,225 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:39,226 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:39,340 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 10:44:39,348 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 10:44:39,367 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:39,367 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:39,369 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:39,370 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:39,372 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:39,372 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:39,374 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:39,374 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:39,377 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:39,377 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:39,380 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:39,381 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:39,385 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:39,386 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:39,390 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:39,392 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:39,403 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:39,404 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:39,414 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:39,415 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:39,418 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:39,419 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:39,421 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:39,421 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:39,424 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:39,424 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:39,425 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:39,426 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:39,430 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:39,430 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:39,432 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:39,432 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:39,434 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:39,434 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:39,435 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:39,436 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:39,476 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 10:44:39,477 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 50 
2025-09-05 10:44:39,477 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $221.871536 
2025-09-05 10:44:39,477 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 10:44:39,477 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 10:44:39,477 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 from 50 operations 
2025-09-05 10:44:39,477 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 
2025-09-05 10:44:42,662 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 10:44:42,669 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:42,670 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:42,671 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:42,672 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:42,673 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:42,674 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:42,677 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:42,677 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:42,682 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:42,682 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:42,684 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:42,684 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:42,686 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:42,686 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:42,688 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:42,689 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:42,691 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:42,692 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:42,698 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:42,699 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:42,701 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:44,992 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 10:44:44,992 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 10:44:44,992 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 10:44:44,992 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 10:44:44,993 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 10:44:45,702 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 10:44:45,703 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 10:44:45,704 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 10:44:45,705 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 10:44:45,706 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 10:44:45,706 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 10:44:45,706 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 10:44:45,835 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:45,838 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:45,841 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:45,843 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:45,849 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:45,851 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:45,993 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 10:44:46,000 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 10:44:46,021 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:46,022 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:46,024 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:46,024 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:46,028 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:46,029 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:46,031 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:46,032 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:46,035 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:46,035 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:46,038 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:46,039 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:46,041 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:46,042 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:46,046 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:46,046 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:46,063 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:46,063 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:46,066 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:46,067 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:46,070 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:46,071 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:46,074 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:46,075 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:46,082 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:46,082 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:46,084 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:46,085 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:46,089 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:46,089 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:46,091 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:46,093 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:46,097 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:46,098 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:46,101 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:46,102 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:46,140 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 10:44:46,141 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 50 
2025-09-05 10:44:46,142 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $221.871536 
2025-09-05 10:44:46,142 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 10:44:46,142 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 10:44:46,143 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 from 50 operations 
2025-09-05 10:44:46,143 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $221.871536 
2025-09-05 10:44:46,327 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 10:44:46,353 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:46,354 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:46,356 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:46,356 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:46,358 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:46,359 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:46,363 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:46,363 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:46,372 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:46,373 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:46,376 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:46,377 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:46,380 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:46,381 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:46,383 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:46,384 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:46,386 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
2025-09-05 10:44:46,387 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:46,396 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:46,398 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:46,399 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:50,468 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED ===== 
2025-09-05 10:44:50,468 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 10:44:50,468 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Config ID: None 
2025-09-05 10:44:50,468 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Has field_values: True 
2025-09-05 10:44:50,469 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Field values count: 300 
2025-09-05 10:44:50,947 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Added 71 calculated fields 
2025-09-05 10:44:50,947 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 10:44:50,947 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id) 
2025-09-05 10:44:50,947 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config 
2025-09-05 10:44:50,947 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Attempting template-based calculation as fallback 
2025-09-05 10:44:50,947 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) ===== 
2025-09-05 10:44:50,947 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 10:44:51,100 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:51,106 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:51,113 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:51,127 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:51,129 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:51,132 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:51,289 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 10:44:51,299 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 10:44:51,332 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:44:51,332 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:51,335 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:44:51,335 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:51,339 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:44:51,340 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:51,342 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:44:51,343 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:51,346 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:44:51,347 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:51,349 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:44:51,350 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:51,353 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:44:51,353 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:51,354 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:44:51,355 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:51,358 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:44:51,359 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:51,362 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:44:51,365 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:51,424 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 10:44:51,424 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Operations found: 44 
2025-09-05 10:44:51,425 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Total cost: $203.631536 
2025-09-05 10:44:51,426 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 10:44:51,426 6396 WARNING canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG ===== 
2025-09-05 10:44:51,427 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $203.631536 from 44 operations 
2025-09-05 10:44:51,428 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS_BUG] ✓ Template-based calculation successful: $203.631536 
2025-09-05 10:44:52,840 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 10:44:52,895 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:44:52,896 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:52,899 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:44:52,900 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:52,902 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:44:52,903 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:52,906 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:44:52,907 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:52,910 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:44:52,910 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:44:52,918 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:52,919 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:44:52,921 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:45:01,426 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] Received parameters: ['template_id', 'product_id', 'order_line_id', 'config_data', 'configuration_price', 'configuration_price_matrix', 'total_price', 'field_3451', 'field_3452', 'field_3453', 'field_3454', 'field_3455', 'field_3456', 'field_3457', 'field_3458', 'field_3459', 'field_3460', 'field_3461', 'field_3462', 'field_3463', 'field_3464', 'field_3465', 'field_3466', 'field_3467', 'field_3468', 'field_3469', 'field_3470', 'field_3471', 'field_3472', 'field_3473', 'field_3474', 'field_3475', 'field_3476', 'field_3477', 'field_3478', 'field_3479', 'field_3480', 'field_3481', 'field_3482', 'field_3483', 'field_3484', 'field_3485', 'field_3486', 'field_3487', 'field_3488', 'field_3489', 'field_3490', 'field_3491', 'field_3492', 'field_3493', 'field_3494', 'field_3495', 'field_3496', 'field_3497', 'field_3498', 'field_3499', 'field_3500', 'field_3501', 'field_3502', 'field_3503', 'field_3504', 'field_3505', 'field_3506', 'field_3507', 'field_3508', 'field_3509', 'field_3510', 'field_3511', 'field_3512', 'field_3513', 'field_3514', 'field_3515', 'field_3516', 'field_3517', 'field_3518', 'field_3519', 'field_3520', 'field_3521', 'field_3522', 'field_3523', 'field_3524', 'field_3525', 'field_3526', 'field_3527', 'field_3528', 'field_3529', 'field_3530', 'field_3531', 'field_3532', 'field_3533', 'field_3534', 'field_3535', 'field_3536', 'field_3537', 'field_3538', 'field_3539', 'field_3540', 'field_3541', 'field_3542', 'field_3543', 'field_3544', 'field_3545', 'field_3546', 'field_3547', 'field_3548', 'field_3549', 'field_3550', 'field_3551', 'field_3552', 'field_3553', 'field_3554', 'field_3555', 'field_3556', 'field_3557', 'field_3558', 'field_3559', 'field_3560', 'field_3561', 'field_3562', 'field_3563', 'field_3564', 'field_3565', 'field_3566', 'field_3567', 'field_3568', 'field_3569', 'field_3570', 'field_3571', 'field_3572', 'field_3573', 'field_3574', 'field_3575', 'field_3576', 'field_3577', 'field_3578', 'field_3579', 'field_3580', 'field_3581', 'field_3582', 'field_3585', 'field_3586', 'field_3583', 'field_3584', 'field_3587', 'field_3588', 'field_3589', 'field_3590', 'field_3591', 'field_3592', 'field_3593', 'field_3594', 'field_3595', 'field_3596', 'field_3597', 'field_3598', 'field_3599', 'field_3600', 'field_3601', 'field_3602', 'field_3603', 'field_3604', 'field_3605', 'field_3606', 'field_3607', 'field_3608', 'field_3609', 'field_3610', 'field_3611', 'field_3612', 'field_3613', 'field_3614', 'field_3615', 'field_3616', 'field_3617', 'field_3618', 'field_3619', 'field_3620', 'field_3621', 'field_3622', 'field_3623', 'field_3624', 'field_3625', 'field_3626', 'field_3627', 'field_3628', 'field_3629', 'field_3630', 'field_3631', 'field_3632', 'field_3633', 'field_3634', 'field_3635', 'field_3636', 'field_3637', 'field_3638', 'field_3639', 'field_3640', 'field_3641', 'field_3642', 'field_3643', 'field_3644', 'field_3645', 'field_3646', 'field_3647', 'field_3648', 'field_3649', 'field_4140', 'field_3650', 'field_3651', 'field_3652', 'field_3653', 'field_3654', 'field_3655', 'field_3656', 'field_3657', 'field_3658', 'field_3661', 'field_3662', 'field_3663', 'field_3664', 'field_3665', 'field_3666', 'field_3667', 'field_3668', 'field_3669', 'field_3670', 'field_3671', 'field_3672', 'field_3673', 'field_3674', 'field_3675', 'field_3676', 'field_3677', 'field_3678', 'field_3679', 'field_3680'] 
2025-09-05 10:45:01,426 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] configuration_price_matrix in kw: True 
2025-09-05 10:45:01,426 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] configuration_price in kw: True 
2025-09-05 10:45:01,426 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] Extracted price_matrix: 717.74 
2025-09-05 10:45:01,426 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] Extracted price_component: 111.92540000000001 
2025-09-05 10:45:01,889 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 10:45:01,904 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:45:01,905 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:45:01,906 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:45:01,906 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:45:01,907 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:45:01,908 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:45:01,909 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:45:01,910 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:45:01,911 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:45:01,911 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:45:01,914 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:45:01,915 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:45:01,916 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:45:02,308 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Found quantity multiplier: 1.0 
2025-09-05 10:45:02,308 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Preview context - price matrix with multiplier: 717.74 * 1.0 = 717.74 
2025-09-05 10:45:02,308 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Component total (BOM lines already include multiplier): 111.92540000000001 
2025-09-05 10:45:02,308 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ===== MODEL OPERATION COST CALCULATION ===== 
2025-09-05 10:45:02,309 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Configuration ID: 74758 
2025-09-05 10:45:02,309 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 10:45:02,309 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Loaded 346 config values 
2025-09-05 10:45:02,309 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 10:45:02,309 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ===== MODEL FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 10:45:02,360 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 10:45:02,365 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 10:45:02,385 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:45:02,385 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:45:02,386 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:45:02,386 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:45:02,387 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:45:02,388 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:45:02,388 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:45:02,389 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:45:02,390 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:45:02,390 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:45:02,391 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:45:02,391 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:45:02,393 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:45:02,393 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:45:02,394 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:45:02,394 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:45:02,395 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:45:02,395 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:45:02,396 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:45:02,397 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:45:02,407 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:45:02,408 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:45:02,410 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:45:02,411 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:45:02,412 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:45:02,413 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:45:02,419 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ===== MODEL FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 10:45:02,419 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Operations found: 51 
2025-09-05 10:45:02,419 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Total cost: $252.75153600000004 
2025-09-05 10:45:02,419 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 10:45:02,419 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ✓ Model operation cost: $252.75153600000004 from 51 operations 
2025-09-05 10:45:02,419 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Operation total (already includes multiplier): 252.75153600000004 
2025-09-05 10:45:02,419 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Configuration 74758 price calculation: 
2025-09-05 10:45:02,419 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Components: $111.92540000000001 
2025-09-05 10:45:02,419 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Operations: $252.75153600000004 
2025-09-05 10:45:02,419 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Sale Price Matrices (with multiplier): $717.74 
2025-09-05 10:45:02,419 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Total: $1082.416936 
2025-09-05 10:45:02,419 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Quantity Multiplier: 1.0 
2025-09-05 10:45:02,419 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - BOM ID: 74728 
2025-09-05 10:45:02,420 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - BOM lines count: 14 
2025-09-05 10:45:02,837 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 10:45:02,848 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:45:02,848 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:45:02,850 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:45:02,850 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:45:02,851 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:45:02,852 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:45:02,853 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:45:02,853 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:45:02,854 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:45:02,854 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:45:02,858 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:45:02,858 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:45:02,859 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:45:03,136 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Found quantity multiplier: 1.0 
2025-09-05 10:45:03,136 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Saving configuration - using base price 717.74 (multiplier 1.0 will be applied to product_uom_qty) 
2025-09-05 10:45:03,139 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Component total (BOM lines already include multiplier): 111.92540000000001 
2025-09-05 10:45:03,140 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ===== MODEL OPERATION COST CALCULATION ===== 
2025-09-05 10:45:03,140 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Configuration ID: 74758 
2025-09-05 10:45:03,140 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Template ID: 24 
2025-09-05 10:45:03,140 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Loaded 346 config values 
2025-09-05 10:45:03,140 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Found quantity multiplier: 1.0 
2025-09-05 10:45:03,140 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ===== MODEL FIELD/OPTION MAPPING CALCULATION ===== 
2025-09-05 10:45:03,193 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
2025-09-05 10:45:03,197 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
2025-09-05 10:45:03,213 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:45:03,213 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:45:03,214 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:45:03,215 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:45:03,216 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:45:03,216 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:45:03,217 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:45:03,217 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:45:03,219 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:45:03,219 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:45:03,220 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:45:03,221 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:45:03,222 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:45:03,222 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:45:03,223 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:45:03,223 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:45:03,224 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:45:03,225 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:45:03,225 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
2025-09-05 10:45:03,226 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
2025-09-05 10:45:03,234 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:45:03,235 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:45:03,237 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:45:03,238 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:45:03,239 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:45:03,239 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
2025-09-05 10:45:03,245 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ===== MODEL FIELD/OPTION MAPPING RESULT ===== 
2025-09-05 10:45:03,245 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Operations found: 51 
2025-09-05 10:45:03,245 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Total cost: $252.75153600000004 
2025-09-05 10:45:03,245 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] Quantity multiplier: 1.0 
2025-09-05 10:45:03,245 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [OPERATION_COSTS_BUG] ✓ Model operation cost: $252.75153600000004 from 51 operations 
2025-09-05 10:45:03,245 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Operation total (already includes multiplier): 252.75153600000004 
2025-09-05 10:45:03,245 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] Configuration 74758 price calculation: 
2025-09-05 10:45:03,245 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Components: $111.92540000000001 
2025-09-05 10:45:03,245 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Operations: $252.75153600000004 
2025-09-05 10:45:03,245 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Sale Price Matrices (with multiplier): $717.74 
2025-09-05 10:45:03,245 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Total: $1082.416936 
2025-09-05 10:45:03,245 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - Quantity Multiplier: 1.0 
2025-09-05 10:45:03,245 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - BOM ID: 74728 
2025-09-05 10:45:03,246 6396 INFO canbrax_sb_2 odoo.addons.canbrax_configmatrix.models.config_matrix_configuration: [CALCULATE_PRICE] - BOM lines count: 14 
