odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Calculating operation costs for template 24 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Received field_values: {'bx_dbl_hinge_location': '', 'bx_dbl_hinge_location_other': '', 'bx_dbl_hinge_jamb_reveal_gt_20mm': '', 'bx_dbl_hinge_screen_clears_handle': '', 'bx_dbl_hinge_new_handle_clears_existing': '', 'bx_dbl_hinge_swing_path_clear': '', 'bx_dbl_hinge_obstruction_description': '', 'bx_dbl_hinge_quantity': '1', 'bx_dbl_hinge_frame_colour': '', 'bx_dbl_hinge_powder_coat_colour': '', 'bx_dbl_hinge_custom_powder_coat_name': '', 'bx_dbl_hinge_custom_powder_coat_finish': '', 'bx_dbl_hinge_custom_powder_coat_code': '', 'bx_dbl_hinge_deduction_assistance': '', 'bx_dbl_hinge_door_split_type': 'even', 'bx_dbl_hinge_opening_height_mm_even': '', 'bx_dbl_hinge_height_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_height_mm_even': '', 'bx_dbl_hinge_opening_top_width_mm_even': '', 'bx_dbl_hinge_top_width_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_top_width_mm_even': '', 'bx_dbl_hinge_opening_middle_width_mm_even': '', 'bx_dbl_hinge_middle_width_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_middle_width_mm_even': '', 'bx_dbl_hinge_opening_bottom_width_mm_even': '', 'bx_dbl_hinge_bottom_width_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_bottom_width_mm_even': '', 'bx_dbl_hinge_left_opening_height_mm_uneven': '', 'bx_dbl_hinge_height_deduction_mm_uneven_left': '', 'bx_dbl_hinge_make_left_door_height_mm_uneven': '', 'bx_dbl_hinge_left_jamb_centre_top_mm_uneven': '', 'bx_dbl_hinge_top_left_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_left_door_top_width_mm_uneven': '', 'bx_dbl_hinge_left_jamb_centre_middle_mm_uneven': '', 'bx_dbl_hinge_middle_left_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_left_door_middle_width_mm_uneven': '', 'bx_dbl_hinge_left_jamb_centre_bottom_mm_uneven': '', 'bx_dbl_hinge_bottom_left_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_left_door_bottom_width_mm_uneven': '', 'bx_dbl_hinge_right_opening_height_mm_uneven': '', 'bx_dbl_hinge_height_deduction_mm_uneven_right': '', 'bx_dbl_hinge_make_right_door_height_mm_uneven': '', 'bx_dbl_hinge_right_jamb_centre_top_mm_uneven': '', 'bx_dbl_hinge_top_right_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_right_door_top_width_mm_uneven': '', 'bx_dbl_hinge_right_jamb_centre_middle_mm_uneven': '', 'bx_dbl_hinge_middle_right_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_right_door_middle_width_mm_uneven': '', 'bx_dbl_hinge_right_jamb_centre_bottom_mm_uneven': '', 'bx_dbl_hinge_bottom_right_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_right_door_bottom_width_mm_uneven': '', 'bx_dbl_hinge_make_left_door_height_mm_manual': '', 'bx_dbl_hinge_make_left_door_top_width_mm_manual': '', 'bx_dbl_hinge_make_left_door_middle_width_mm_manual': '', 'bx_dbl_hinge_make_left_door_bottom_width_mm_manual': '', 'bx_dbl_hinge_make_right_door_height_mm_manual': '', 'bx_dbl_hinge_make_right_door_top_width_mm_manual': '', 'bx_dbl_hinge_make_right_door_middle_width_mm_manual': '', 'bx_dbl_hinge_make_right_door_bottom_width_mm_manual': '', 'bx_dbl_hinge_swing': 'outswing', 'bx_dbl_hinge_t_section_mullion_inswing': 'yes', 'bx_dbl_hinge_french_door_mullion_outswing': 'yes', 'bx_dbl_hinge_lock_brand': 'commandex_hinged', 'bx_dbl_hinge_austral_elegance_xc_lock_colour': 'black', 'bx_dbl_hinge_austral_elegance_xc_cylinder': 'austral_5_pin_dbl', 'bx_dbl_hinge_austral_elegance_xc_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_lt_top_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_lh_bottom_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_handle': '', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_handle': 'single', 'bx_dbl_hinge_commandex_hinged_lock_colour': 'black', 'bx_dbl_hinge_commandex_hinged_cylinder': 'commandex_5_pin', 'bx_dbl_hinge_commandex_hinged_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_top_cut_out': '', 'bx_dbl_hinge_commandex_hinged_lt_top_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_lh_centre_cut_out': '', 'bx_dbl_hinge_commandex_hinged_lt_centre_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_lh_bottom_cut_out': '', 'bx_dbl_hinge_commandex_hinged_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_lh_centre_handle': '', 'bx_dbl_hinge_commandex_hinged_lt_centre_handle': 'single', 'bx_dbl_hinge_lockwood_8654_lock_colour': 'black', 'bx_dbl_hinge_lockwood_8654_cylinder': 'whitco_5_pin', 'bx_dbl_hinge_lockwood_8654_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_top_cut_out': '', 'bx_dbl_hinge_lockwood_8654_lt_top_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_lh_centre_cut_out': '', 'bx_dbl_hinge_lockwood_8654_lt_centre_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_lh_bottom_cut_out': '', 'bx_dbl_hinge_lockwood_8654_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_lh_centre_handle': '', 'bx_dbl_hinge_lockwood_8654_lt_centre_handle': 'single', 'bx_dbl_hinge_whitco_mk2_lock_colour': 'black', 'bx_dbl_hinge_whitco_mk2_cylinder': 'whitco_5_pin', 'bx_dbl_hinge_whitco_mk2_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_top_cut_out': '', 'bx_dbl_hinge_whitco_mk2_lt_top_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_lh_centre_cut_out': '', 'bx_dbl_hinge_whitco_mk2_lt_centre_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_lh_bottom_cut_out': '', 'bx_dbl_hinge_whitco_mk2_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_lh_centre_handle': '', 'bx_dbl_hinge_whitco_mk2_lt_centre_handle': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lock_colour': 'black', 'bx_dbl_hinge_whitco_tasman_escape_cylinder': 'whitco_escape_cylinder_turn_knob', 'bx_dbl_hinge_whitco_tasman_escape_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_top_cut_out': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_top_cut_out': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_cut_out': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_cut_out': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lh_bottom_cut_out': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_handle': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_handle': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_austral_elegance_xc_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_top_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_handle': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_handle': 'single', 'bx_dbl_hinge_commandex_hinged_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_commandex_hinged_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_top_cut_out': '', 'bx_dbl_hinge_commandex_hinged_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_commandex_hinged_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_cut_out': '', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_handle': '', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_handle': 'single', 'bx_dbl_hinge_lockwood_8654_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_lockwood_8654_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_top_cut_out': '', 'bx_dbl_hinge_lockwood_8654_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_cut_out': '', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_lockwood_8654_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_handle': '', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_handle': 'single', 'bx_dbl_hinge_whitco_mk2_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_whitco_mk2_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_top_cut_out': '', 'bx_dbl_hinge_whitco_mk2_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_cut_out': '', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_whitco_mk2_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_handle': '', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_handle': 'single', 'bx_dbl_hinge_lock_cut_out_side_dd': '', 'bx_dbl_hinge_non_lock_door_striker_cutouts': 'yes', 'bx_dbl_hinge_non_lock_door_bolts': 'top_bottom_flush_bolts', 'bx_dbl_hinge_patio_bolt_colour_non_lock': 'black', 'bx_dbl_hinge_midrail_case1': 'yes', 'bx_dbl_hinge_midrail_height_mm_case1': '', 'bx_dbl_hinge_midrail_colour_std_case1': '', 'bx_dbl_hinge_midrail_colour_special_case1': 'black', 'bx_dbl_hinge_midrail_case2': 'no', 'bx_dbl_hinge_midrail_height_mm_case2': '', 'bx_dbl_hinge_midrail_colour_std_case2': 'black', 'bx_dbl_hinge_midrail_colour_special_case2': 'black', 'bx_dbl_hinge_num_sec_hinges_pickup': '3_per_door_attached', 'bx_dbl_hinge_num_sec_hinges_deliver': '3_per_door_loose_drilled', 'bx_dbl_hinge_spec_hinge_loc_2_pickup_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_pickup': '', 'bx_dbl_hinge_spec_hinge_loc_3_pickup_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_pickup': '', 'bx_dbl_hinge_spec_hinge_loc_4_pickup_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_pickup': '', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_pickup': '', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_pickup': '', 'bx_dbl_hinge_spec_hinge_loc_2_deliver_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_deliver': '', 'bx_dbl_hinge_spec_hinge_loc_3_deliver_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_deliver': '', 'bx_dbl_hinge_spec_hinge_loc_4_deliver_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_deliver': '', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_deliver': '', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_deliver': '', 'bx_dbl_hinge_sec_hinge_type': 'security_hinge', 'bx_dbl_hinge_sec_hinge_colour_std': 'black', 'bx_dbl_hinge_sec_hinge_colour_prong': 'black', 'bx_dbl_hinge_sec_hinge_colour_step': 'black', 'bx_dbl_hinge_sec_hinge_packers_qty': '0', 'bx_dbl_hinge_closer': 'no', 'bx_dbl_hinge_closer_colour_austral': 'black', 'bx_dbl_hinge_closer_colour_whitco': 'black', 'bx_dbl_hinge_pet_door': 'no', 'bx_dbl_hinge_pet_door_colour': 'black', 'bx_dbl_hinge_pet_door_location': 'lock_door_lock_side', 'bx_dbl_hinge_pet_door_flexible_flap': 'no', 'bx_dbl_hinge_pet_door_surround_infill': 'no', 'bx_dbl_hinge_bug_seal_size': 'no', 'bx_dbl_hinge_left_bug_seal_length_mm': '', 'bx_dbl_hinge_right_bug_seal_length_mm': '', 'bx_dbl_hinge_stop_bead_colour': 'no', 'bx_dbl_hinge_stop_bead_left_length_mm': '', 'bx_dbl_hinge_stop_bead_top_length_mm': '', 'bx_dbl_hinge_stop_bead_right_length_mm': '', 'bx_dbl_hinge_adhesive_mohair': 'no', 'bx_dbl_hinge_adhesive_mohair_length_mm': '', 'bx_dbl_hinge_jamb_adaptor_type_case1': 'no', 'bx_dbl_hinge_jamb_adaptor_type_case2': '', 'bx_dbl_hinge_jamb_adaptor_type_case3': '', 'bx_dbl_hinge_jamb_opening_left_height_mm': '', 'bx_dbl_hinge_jamb_height_addition_mm': '20', 'bx_dbl_hinge_jamb_adaptor_left_length_mm': '', 'bx_dbl_hinge_jamb_opening_top_width_mm': '', 'bx_dbl_hinge_jamb_width_addition_mm': '40', 'bx_dbl_hinge_jamb_adaptor_top_length_mm': '', 'bx_dbl_hinge_jamb_opening_right_height_mm': '', 'bx_dbl_hinge_jamb_adaptor_right_length_mm': '', '_CALCULATED_deduction_assistance': None, '_CALCULATED_door_split_type': None, '_CALCULATED_lock_height': None, '_CALCULATED_manual_left_height': None, '_CALCULATED_manual_right_height': None, '_CALCULATED_height_calculation_method': None, '_CALCULATED_is_even_split': None, '_CALCULATED_is_manual_mode': None, '_CALCULATED_is_uneven_split': None, '_CALCULATED_largest_door_height': None, '_CALCULATED_smallest_door_height': 20, '_CALCULATED_halfway_point': None, '_CALCULATED_halfway_minus_79': None, '_CALCULATED_halfway_plus_16': None, '_CALCULATED_halfway_plus_32': None, '_CALCULATED_halfway_plus_79': None, '_CALCULATED_height_minus_1000': None, '_CALCULATED_height_minus_1003': None, '_CALCULATED_height_minus_1019': None, '_CALCULATED_height_minus_1090': None, '_CALCULATED_height_minus_1098': None, '_CALCULATED_height_minus_1137': None, '_CALCULATED_height_minus_1153': None, '_CALCULATED_height_minus_1169': None, '_CALCULATED_height_minus_1248': None, '_CALCULATED_height_minus_270': None, '_CALCULATED_height_minus_317': None, '_CALCULATED_height_minus_330': None, '_CALCULATED_height_minus_333': None, '_CALCULATED_height_minus_349': None, '_CALCULATED_height_minus_428': None, '_CALCULATED_height_minus_740': None, '_CALCULATED_height_minus_787': None, '_CALCULATED_height_minus_800': None, '_CALCULATED_height_minus_803': None, '_CALCULATED_height_minus_819': None, '_CALCULATED_height_minus_898': None, '_CALCULATED_height_minus_940': None, '_CALCULATED_height_minus_987': None, '_CALCULATED_Largest_Sum_Door_Width': None, '_CALCULATED_door_width': None, '_CALCULATED_even_bottom_width': None, '_CALCULATED_even_middle_width': None, '_CALCULATED_even_top_width': None, '_CALCULATED_jamb_adaptor_left_length_mm': None, '_CALCULATED_jamb_adaptor_top_length_mm': None, '_CALCULATED_largest_door_width': None, '_CALCULATED_largest_left_door_width': None, '_CALCULATED_largest_right_door_width': None, '_CALCULATED_left_Clamp_Product': None, '_CALCULATED_left_door_middle_width': None, '_CALCULATED_left_height_door': None, '_CALCULATED_manual_formula': None, '_CALCULATED_right_Clamp_Product': None, '_CALCULATED_right_door_middle_width': None, '_CALCULATED_right_height_door': None, '_CALCULATED_smallest_door_width': None, '_CALCULATED_mesh_required': None, '_CALCULATED_mesh_width': None, '_CALCULATED_mesh_height': None, '_CALCULATED_midrail_case1': None, '_CALCULATED_mesh_series': None, '_CALCULATED_midrail_case2': None, '_CALCULATED_midrail_height': 0, '_CALCULATED_mesh_area': None, '_CALCULATED_mesh_operation_required': None, '_CALCULATED_mesh_operation_type': None, '_CALCULATED_mesh_area_m2': None, '_CALCULATED_mesh_perimeter': None, '_CALCULATED_mesh_aspect_ratio': None, '_CALCULATED_mesh_size_category': None} 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Field values keys: ['bx_dbl_hinge_location', 'bx_dbl_hinge_location_other', 'bx_dbl_hinge_jamb_reveal_gt_20mm', 'bx_dbl_hinge_screen_clears_handle', 'bx_dbl_hinge_new_handle_clears_existing', 'bx_dbl_hinge_swing_path_clear', 'bx_dbl_hinge_obstruction_description', 'bx_dbl_hinge_quantity', 'bx_dbl_hinge_frame_colour', 'bx_dbl_hinge_powder_coat_colour', 'bx_dbl_hinge_custom_powder_coat_name', 'bx_dbl_hinge_custom_powder_coat_finish', 'bx_dbl_hinge_custom_powder_coat_code', 'bx_dbl_hinge_deduction_assistance', 'bx_dbl_hinge_door_split_type', 'bx_dbl_hinge_opening_height_mm_even', 'bx_dbl_hinge_height_deduction_mm_even', 'bx_dbl_hinge_make_each_door_height_mm_even', 'bx_dbl_hinge_opening_top_width_mm_even', 'bx_dbl_hinge_top_width_deduction_mm_even', 'bx_dbl_hinge_make_each_door_top_width_mm_even', 'bx_dbl_hinge_opening_middle_width_mm_even', 'bx_dbl_hinge_middle_width_deduction_mm_even', 'bx_dbl_hinge_make_each_door_middle_width_mm_even', 'bx_dbl_hinge_opening_bottom_width_mm_even', 'bx_dbl_hinge_bottom_width_deduction_mm_even', 'bx_dbl_hinge_make_each_door_bottom_width_mm_even', 'bx_dbl_hinge_left_opening_height_mm_uneven', 'bx_dbl_hinge_height_deduction_mm_uneven_left', 'bx_dbl_hinge_make_left_door_height_mm_uneven', 'bx_dbl_hinge_left_jamb_centre_top_mm_uneven', 'bx_dbl_hinge_top_left_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_left_door_top_width_mm_uneven', 'bx_dbl_hinge_left_jamb_centre_middle_mm_uneven', 'bx_dbl_hinge_middle_left_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_left_door_middle_width_mm_uneven', 'bx_dbl_hinge_left_jamb_centre_bottom_mm_uneven', 'bx_dbl_hinge_bottom_left_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_left_door_bottom_width_mm_uneven', 'bx_dbl_hinge_right_opening_height_mm_uneven', 'bx_dbl_hinge_height_deduction_mm_uneven_right', 'bx_dbl_hinge_make_right_door_height_mm_uneven', 'bx_dbl_hinge_right_jamb_centre_top_mm_uneven', 'bx_dbl_hinge_top_right_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_right_door_top_width_mm_uneven', 'bx_dbl_hinge_right_jamb_centre_middle_mm_uneven', 'bx_dbl_hinge_middle_right_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_right_door_middle_width_mm_uneven', 'bx_dbl_hinge_right_jamb_centre_bottom_mm_uneven', 'bx_dbl_hinge_bottom_right_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_right_door_bottom_width_mm_uneven', 'bx_dbl_hinge_make_left_door_height_mm_manual', 'bx_dbl_hinge_make_left_door_top_width_mm_manual', 'bx_dbl_hinge_make_left_door_middle_width_mm_manual', 'bx_dbl_hinge_make_left_door_bottom_width_mm_manual', 'bx_dbl_hinge_make_right_door_height_mm_manual', 'bx_dbl_hinge_make_right_door_top_width_mm_manual', 'bx_dbl_hinge_make_right_door_middle_width_mm_manual', 'bx_dbl_hinge_make_right_door_bottom_width_mm_manual', 'bx_dbl_hinge_swing', 'bx_dbl_hinge_t_section_mullion_inswing', 'bx_dbl_hinge_french_door_mullion_outswing', 'bx_dbl_hinge_lock_brand', 'bx_dbl_hinge_austral_elegance_xc_lock_colour', 'bx_dbl_hinge_austral_elegance_xc_cylinder', 'bx_dbl_hinge_austral_elegance_xc_lock_height_location', 'bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lt_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lt_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_handle', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_handle', 'bx_dbl_hinge_commandex_hinged_lock_colour', 'bx_dbl_hinge_commandex_hinged_cylinder', 'bx_dbl_hinge_commandex_hinged_lock_height_location', 'bx_dbl_hinge_commandex_hinged_lh_top_cut_out', 'bx_dbl_hinge_commandex_hinged_lt_top_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_lt_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_lt_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_centre_handle', 'bx_dbl_hinge_commandex_hinged_lt_centre_handle', 'bx_dbl_hinge_lockwood_8654_lock_colour', 'bx_dbl_hinge_lockwood_8654_cylinder', 'bx_dbl_hinge_lockwood_8654_lock_height_location', 'bx_dbl_hinge_lockwood_8654_lh_top_cut_out', 'bx_dbl_hinge_lockwood_8654_lt_top_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_lt_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_lt_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_centre_handle', 'bx_dbl_hinge_lockwood_8654_lt_centre_handle', 'bx_dbl_hinge_whitco_mk2_lock_colour', 'bx_dbl_hinge_whitco_mk2_cylinder', 'bx_dbl_hinge_whitco_mk2_lock_height_location', 'bx_dbl_hinge_whitco_mk2_lh_top_cut_out', 'bx_dbl_hinge_whitco_mk2_lt_top_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_lt_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_lt_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_centre_handle', 'bx_dbl_hinge_whitco_mk2_lt_centre_handle', 'bx_dbl_hinge_whitco_tasman_escape_lock_colour', 'bx_dbl_hinge_whitco_tasman_escape_cylinder', 'bx_dbl_hinge_whitco_tasman_escape_lock_height_location', 'bx_dbl_hinge_whitco_tasman_escape_lh_top_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lt_top_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_bottom_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lt_bottom_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_handle', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_handle', 'bx_dbl_hinge_austral_elegance_xc_co_cylinder', 'bx_dbl_hinge_austral_elegance_xc_co_lock_height_location', 'bx_dbl_hinge_austral_elegance_xc_co_lh_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lt_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lt_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_handle', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_handle', 'bx_dbl_hinge_commandex_hinged_co_cylinder', 'bx_dbl_hinge_commandex_hinged_co_lock_height_location', 'bx_dbl_hinge_commandex_hinged_co_lh_top_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lt_top_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lt_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_handle', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_handle', 'bx_dbl_hinge_lockwood_8654_co_cylinder', 'bx_dbl_hinge_lockwood_8654_co_lock_height_location', 'bx_dbl_hinge_lockwood_8654_co_lh_top_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lt_top_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lt_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_handle', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_handle', 'bx_dbl_hinge_whitco_mk2_co_cylinder', 'bx_dbl_hinge_whitco_mk2_co_lock_height_location', 'bx_dbl_hinge_whitco_mk2_co_lh_top_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lt_top_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lt_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_handle', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_handle', 'bx_dbl_hinge_lock_cut_out_side_dd', 'bx_dbl_hinge_non_lock_door_striker_cutouts', 'bx_dbl_hinge_non_lock_door_bolts', 'bx_dbl_hinge_patio_bolt_colour_non_lock', 'bx_dbl_hinge_midrail_case1', 'bx_dbl_hinge_midrail_height_mm_case1', 'bx_dbl_hinge_midrail_colour_std_case1', 'bx_dbl_hinge_midrail_colour_special_case1', 'bx_dbl_hinge_midrail_case2', 'bx_dbl_hinge_midrail_height_mm_case2', 'bx_dbl_hinge_midrail_colour_std_case2', 'bx_dbl_hinge_midrail_colour_special_case2', 'bx_dbl_hinge_num_sec_hinges_pickup', 'bx_dbl_hinge_num_sec_hinges_deliver', 'bx_dbl_hinge_spec_hinge_loc_2_pickup_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_pickup', 'bx_dbl_hinge_spec_hinge_loc_3_pickup_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_pickup', 'bx_dbl_hinge_spec_hinge_loc_4_pickup_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_pickup', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_pickup', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_pickup', 'bx_dbl_hinge_spec_hinge_loc_2_deliver_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_deliver', 'bx_dbl_hinge_spec_hinge_loc_3_deliver_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_deliver', 'bx_dbl_hinge_spec_hinge_loc_4_deliver_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_deliver', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_deliver', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_deliver', 'bx_dbl_hinge_sec_hinge_type', 'bx_dbl_hinge_sec_hinge_colour_std', 'bx_dbl_hinge_sec_hinge_colour_prong', 'bx_dbl_hinge_sec_hinge_colour_step', 'bx_dbl_hinge_sec_hinge_packers_qty', 'bx_dbl_hinge_closer', 'bx_dbl_hinge_closer_colour_austral', 'bx_dbl_hinge_closer_colour_whitco', 'bx_dbl_hinge_pet_door', 'bx_dbl_hinge_pet_door_colour', 'bx_dbl_hinge_pet_door_location', 'bx_dbl_hinge_pet_door_flexible_flap', 'bx_dbl_hinge_pet_door_surround_infill', 'bx_dbl_hinge_bug_seal_size', 'bx_dbl_hinge_left_bug_seal_length_mm', 'bx_dbl_hinge_right_bug_seal_length_mm', 'bx_dbl_hinge_stop_bead_colour', 'bx_dbl_hinge_stop_bead_left_length_mm', 'bx_dbl_hinge_stop_bead_top_length_mm', 'bx_dbl_hinge_stop_bead_right_length_mm', 'bx_dbl_hinge_adhesive_mohair', 'bx_dbl_hinge_adhesive_mohair_length_mm', 'bx_dbl_hinge_jamb_adaptor_type_case1', 'bx_dbl_hinge_jamb_adaptor_type_case2', 'bx_dbl_hinge_jamb_adaptor_type_case3', 'bx_dbl_hinge_jamb_opening_left_height_mm', 'bx_dbl_hinge_jamb_height_addition_mm', 'bx_dbl_hinge_jamb_adaptor_left_length_mm', 'bx_dbl_hinge_jamb_opening_top_width_mm', 'bx_dbl_hinge_jamb_width_addition_mm', 'bx_dbl_hinge_jamb_adaptor_top_length_mm', 'bx_dbl_hinge_jamb_opening_right_height_mm', 'bx_dbl_hinge_jamb_adaptor_right_length_mm', '_CALCULATED_deduction_assistance', '_CALCULATED_door_split_type', '_CALCULATED_lock_height', '_CALCULATED_manual_left_height', '_CALCULATED_manual_right_height', '_CALCULATED_height_calculation_method', '_CALCULATED_is_even_split', '_CALCULATED_is_manual_mode', '_CALCULATED_is_uneven_split', '_CALCULATED_largest_door_height', '_CALCULATED_smallest_door_height', '_CALCULATED_halfway_point', '_CALCULATED_halfway_minus_79', '_CALCULATED_halfway_plus_16', '_CALCULATED_halfway_plus_32', '_CALCULATED_halfway_plus_79', '_CALCULATED_height_minus_1000', '_CALCULATED_height_minus_1003', '_CALCULATED_height_minus_1019', '_CALCULATED_height_minus_1090', '_CALCULATED_height_minus_1098', '_CALCULATED_height_minus_1137', '_CALCULATED_height_minus_1153', '_CALCULATED_height_minus_1169', '_CALCULATED_height_minus_1248', '_CALCULATED_height_minus_270', '_CALCULATED_height_minus_317', '_CALCULATED_height_minus_330', '_CALCULATED_height_minus_333', '_CALCULATED_height_minus_349', '_CALCULATED_height_minus_428', '_CALCULATED_height_minus_740', '_CALCULATED_height_minus_787', '_CALCULATED_height_minus_800', '_CALCULATED_height_minus_803', '_CALCULATED_height_minus_819', '_CALCULATED_height_minus_898', '_CALCULATED_height_minus_940', '_CALCULATED_height_minus_987', '_CALCULATED_Largest_Sum_Door_Width', '_CALCULATED_door_width', '_CALCULATED_even_bottom_width', '_CALCULATED_even_middle_width', '_CALCULATED_even_top_width', '_CALCULATED_jamb_adaptor_left_length_mm', '_CALCULATED_jamb_adaptor_top_length_mm', '_CALCULATED_largest_door_width', '_CALCULATED_largest_left_door_width', '_CALCULATED_largest_right_door_width', '_CALCULATED_left_Clamp_Product', '_CALCULATED_left_door_middle_width', '_CALCULATED_left_height_door', '_CALCULATED_manual_formula', '_CALCULATED_right_Clamp_Product', '_CALCULATED_right_door_middle_width', '_CALCULATED_right_height_door', '_CALCULATED_smallest_door_width', '_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_mesh_height', '_CALCULATED_midrail_case1', '_CALCULATED_mesh_series', '_CALCULATED_midrail_case2', '_CALCULATED_midrail_height', '_CALCULATED_mesh_area', '_CALCULATED_mesh_operation_required', '_CALCULATED_mesh_operation_type', '_CALCULATED_mesh_area_m2', '_CALCULATED_mesh_perimeter', '_CALCULATED_mesh_aspect_ratio', '_CALCULATED_mesh_size_category'] 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Found quantity multiplier: 1.0 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Using field/option mapping calculation (no config_id) 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] WARNING: This may result in different operation costs than save config 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Returning 41 operations with total cost 95.92 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COST_METRICS] {'config_id': None, 'operation_count': 41, 'total_cost': 95.92, 'calculation_method': 'field_option_mapping', 'timestamp': 'unknown'} 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COST_METRICS] WARNING: Using field/option mapping - may differ from save config 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Calculating operation costs for template 24 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Received field_values: {'bx_dbl_hinge_location': '', 'bx_dbl_hinge_location_other': '', 'bx_dbl_hinge_jamb_reveal_gt_20mm': '', 'bx_dbl_hinge_screen_clears_handle': '', 'bx_dbl_hinge_new_handle_clears_existing': '', 'bx_dbl_hinge_swing_path_clear': '', 'bx_dbl_hinge_obstruction_description': '', 'bx_dbl_hinge_quantity': '1', 'bx_dbl_hinge_frame_colour': 'black_custom_matt_gn248a', 'bx_dbl_hinge_powder_coat_colour': '', 'bx_dbl_hinge_custom_powder_coat_name': '', 'bx_dbl_hinge_custom_powder_coat_finish': '', 'bx_dbl_hinge_custom_powder_coat_code': '', 'bx_dbl_hinge_deduction_assistance': '', 'bx_dbl_hinge_door_split_type': 'even', 'bx_dbl_hinge_opening_height_mm_even': '', 'bx_dbl_hinge_height_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_height_mm_even': '', 'bx_dbl_hinge_opening_top_width_mm_even': '', 'bx_dbl_hinge_top_width_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_top_width_mm_even': '', 'bx_dbl_hinge_opening_middle_width_mm_even': '', 'bx_dbl_hinge_middle_width_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_middle_width_mm_even': '', 'bx_dbl_hinge_opening_bottom_width_mm_even': '', 'bx_dbl_hinge_bottom_width_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_bottom_width_mm_even': '', 'bx_dbl_hinge_left_opening_height_mm_uneven': '', 'bx_dbl_hinge_height_deduction_mm_uneven_left': '', 'bx_dbl_hinge_make_left_door_height_mm_uneven': '', 'bx_dbl_hinge_left_jamb_centre_top_mm_uneven': '', 'bx_dbl_hinge_top_left_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_left_door_top_width_mm_uneven': '', 'bx_dbl_hinge_left_jamb_centre_middle_mm_uneven': '', 'bx_dbl_hinge_middle_left_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_left_door_middle_width_mm_uneven': '', 'bx_dbl_hinge_left_jamb_centre_bottom_mm_uneven': '', 'bx_dbl_hinge_bottom_left_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_left_door_bottom_width_mm_uneven': '', 'bx_dbl_hinge_right_opening_height_mm_uneven': '', 'bx_dbl_hinge_height_deduction_mm_uneven_right': '', 'bx_dbl_hinge_make_right_door_height_mm_uneven': '', 'bx_dbl_hinge_right_jamb_centre_top_mm_uneven': '', 'bx_dbl_hinge_top_right_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_right_door_top_width_mm_uneven': '', 'bx_dbl_hinge_right_jamb_centre_middle_mm_uneven': '', 'bx_dbl_hinge_middle_right_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_right_door_middle_width_mm_uneven': '', 'bx_dbl_hinge_right_jamb_centre_bottom_mm_uneven': '', 'bx_dbl_hinge_bottom_right_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_right_door_bottom_width_mm_uneven': '', 'bx_dbl_hinge_make_left_door_height_mm_manual': '', 'bx_dbl_hinge_make_left_door_top_width_mm_manual': '', 'bx_dbl_hinge_make_left_door_middle_width_mm_manual': '', 'bx_dbl_hinge_make_left_door_bottom_width_mm_manual': '', 'bx_dbl_hinge_make_right_door_height_mm_manual': '', 'bx_dbl_hinge_make_right_door_top_width_mm_manual': '', 'bx_dbl_hinge_make_right_door_middle_width_mm_manual': '', 'bx_dbl_hinge_make_right_door_bottom_width_mm_manual': '', 'bx_dbl_hinge_swing': 'outswing', 'bx_dbl_hinge_t_section_mullion_inswing': 'yes', 'bx_dbl_hinge_french_door_mullion_outswing': 'yes', 'bx_dbl_hinge_lock_brand': 'commandex_hinged', 'bx_dbl_hinge_austral_elegance_xc_lock_colour': 'black', 'bx_dbl_hinge_austral_elegance_xc_cylinder': 'austral_5_pin_dbl', 'bx_dbl_hinge_austral_elegance_xc_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_lt_top_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_lh_bottom_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_handle': '', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_handle': 'single', 'bx_dbl_hinge_commandex_hinged_lock_colour': 'black', 'bx_dbl_hinge_commandex_hinged_cylinder': 'commandex_5_pin', 'bx_dbl_hinge_commandex_hinged_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_top_cut_out': '', 'bx_dbl_hinge_commandex_hinged_lt_top_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_lh_centre_cut_out': '', 'bx_dbl_hinge_commandex_hinged_lt_centre_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_lh_bottom_cut_out': '', 'bx_dbl_hinge_commandex_hinged_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_lh_centre_handle': '', 'bx_dbl_hinge_commandex_hinged_lt_centre_handle': 'single', 'bx_dbl_hinge_lockwood_8654_lock_colour': 'black', 'bx_dbl_hinge_lockwood_8654_cylinder': 'whitco_5_pin', 'bx_dbl_hinge_lockwood_8654_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_top_cut_out': '', 'bx_dbl_hinge_lockwood_8654_lt_top_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_lh_centre_cut_out': '', 'bx_dbl_hinge_lockwood_8654_lt_centre_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_lh_bottom_cut_out': '', 'bx_dbl_hinge_lockwood_8654_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_lh_centre_handle': '', 'bx_dbl_hinge_lockwood_8654_lt_centre_handle': 'single', 'bx_dbl_hinge_whitco_mk2_lock_colour': 'black', 'bx_dbl_hinge_whitco_mk2_cylinder': 'whitco_5_pin', 'bx_dbl_hinge_whitco_mk2_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_top_cut_out': '', 'bx_dbl_hinge_whitco_mk2_lt_top_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_lh_centre_cut_out': '', 'bx_dbl_hinge_whitco_mk2_lt_centre_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_lh_bottom_cut_out': '', 'bx_dbl_hinge_whitco_mk2_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_lh_centre_handle': '', 'bx_dbl_hinge_whitco_mk2_lt_centre_handle': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lock_colour': 'black', 'bx_dbl_hinge_whitco_tasman_escape_cylinder': 'whitco_escape_cylinder_turn_knob', 'bx_dbl_hinge_whitco_tasman_escape_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_top_cut_out': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_top_cut_out': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_cut_out': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_cut_out': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lh_bottom_cut_out': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_handle': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_handle': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_austral_elegance_xc_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_top_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_handle': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_handle': 'single', 'bx_dbl_hinge_commandex_hinged_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_commandex_hinged_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_top_cut_out': '', 'bx_dbl_hinge_commandex_hinged_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_commandex_hinged_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_cut_out': '', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_handle': '', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_handle': 'single', 'bx_dbl_hinge_lockwood_8654_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_lockwood_8654_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_top_cut_out': '', 'bx_dbl_hinge_lockwood_8654_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_cut_out': '', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_lockwood_8654_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_handle': '', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_handle': 'single', 'bx_dbl_hinge_whitco_mk2_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_whitco_mk2_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_top_cut_out': '', 'bx_dbl_hinge_whitco_mk2_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_cut_out': '', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_whitco_mk2_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_handle': '', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_handle': 'single', 'bx_dbl_hinge_lock_cut_out_side_dd': '', 'bx_dbl_hinge_non_lock_door_striker_cutouts': 'yes', 'bx_dbl_hinge_non_lock_door_bolts': 'top_bottom_flush_bolts', 'bx_dbl_hinge_patio_bolt_colour_non_lock': 'black', 'bx_dbl_hinge_midrail_case1': 'yes', 'bx_dbl_hinge_midrail_height_mm_case1': '', 'bx_dbl_hinge_midrail_colour_std_case1': '', 'bx_dbl_hinge_midrail_colour_special_case1': 'black', 'bx_dbl_hinge_midrail_case2': 'no', 'bx_dbl_hinge_midrail_height_mm_case2': '', 'bx_dbl_hinge_midrail_colour_std_case2': 'black', 'bx_dbl_hinge_midrail_colour_special_case2': 'black', 'bx_dbl_hinge_num_sec_hinges_pickup': '3_per_door_attached', 'bx_dbl_hinge_num_sec_hinges_deliver': '3_per_door_loose_drilled', 'bx_dbl_hinge_spec_hinge_loc_2_pickup_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_pickup': '', 'bx_dbl_hinge_spec_hinge_loc_3_pickup_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_pickup': '', 'bx_dbl_hinge_spec_hinge_loc_4_pickup_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_pickup': '', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_pickup': '', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_pickup': '', 'bx_dbl_hinge_spec_hinge_loc_2_deliver_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_deliver': '', 'bx_dbl_hinge_spec_hinge_loc_3_deliver_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_deliver': '', 'bx_dbl_hinge_spec_hinge_loc_4_deliver_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_deliver': '', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_deliver': '', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_deliver': '', 'bx_dbl_hinge_sec_hinge_type': 'security_hinge', 'bx_dbl_hinge_sec_hinge_colour_std': 'black', 'bx_dbl_hinge_sec_hinge_colour_prong': 'black', 'bx_dbl_hinge_sec_hinge_colour_step': 'black', 'bx_dbl_hinge_sec_hinge_packers_qty': '0', 'bx_dbl_hinge_closer': 'no', 'bx_dbl_hinge_closer_colour_austral': 'black', 'bx_dbl_hinge_closer_colour_whitco': 'black', 'bx_dbl_hinge_pet_door': 'no', 'bx_dbl_hinge_pet_door_colour': 'black', 'bx_dbl_hinge_pet_door_location': 'lock_door_lock_side', 'bx_dbl_hinge_pet_door_flexible_flap': 'no', 'bx_dbl_hinge_pet_door_surround_infill': 'no', 'bx_dbl_hinge_bug_seal_size': 'no', 'bx_dbl_hinge_left_bug_seal_length_mm': '', 'bx_dbl_hinge_right_bug_seal_length_mm': '', 'bx_dbl_hinge_stop_bead_colour': 'no', 'bx_dbl_hinge_stop_bead_left_length_mm': '', 'bx_dbl_hinge_stop_bead_top_length_mm': '', 'bx_dbl_hinge_stop_bead_right_length_mm': '', 'bx_dbl_hinge_adhesive_mohair': 'no', 'bx_dbl_hinge_adhesive_mohair_length_mm': '', 'bx_dbl_hinge_jamb_adaptor_type_case1': 'no', 'bx_dbl_hinge_jamb_adaptor_type_case2': '', 'bx_dbl_hinge_jamb_adaptor_type_case3': '', 'bx_dbl_hinge_jamb_opening_left_height_mm': '', 'bx_dbl_hinge_jamb_height_addition_mm': '20', 'bx_dbl_hinge_jamb_adaptor_left_length_mm': '', 'bx_dbl_hinge_jamb_opening_top_width_mm': '', 'bx_dbl_hinge_jamb_width_addition_mm': '40', 'bx_dbl_hinge_jamb_adaptor_top_length_mm': '', 'bx_dbl_hinge_jamb_opening_right_height_mm': '', 'bx_dbl_hinge_jamb_adaptor_right_length_mm': '', '_CALCULATED_deduction_assistance': '', '_CALCULATED_door_split_type': 'even', '_CALCULATED_lock_height': 0, '_CALCULATED_manual_left_height': 0, '_CALCULATED_manual_right_height': 0, '_CALCULATED_height_calculation_method': 'even', '_CALCULATED_is_even_split': True, '_CALCULATED_is_manual_mode': False, '_CALCULATED_is_uneven_split': False, '_CALCULATED_largest_door_height': 0, '_CALCULATED_smallest_door_height': 20, '_CALCULATED_halfway_point': 0, '_CALCULATED_halfway_minus_79': -79, '_CALCULATED_halfway_plus_16': 16, '_CALCULATED_halfway_plus_32': 32, '_CALCULATED_halfway_plus_79': 79, '_CALCULATED_height_minus_1000': -1000, '_CALCULATED_height_minus_1003': -1003, '_CALCULATED_height_minus_1019': -1019, '_CALCULATED_height_minus_1090': -1090, '_CALCULATED_height_minus_1098': -1098, '_CALCULATED_height_minus_1137': -1137, '_CALCULATED_height_minus_1153': -1153, '_CALCULATED_height_minus_1169': -1169, '_CALCULATED_height_minus_1248': -1248, '_CALCULATED_height_minus_270': -270, '_CALCULATED_height_minus_317': -317, '_CALCULATED_height_minus_330': -330, '_CALCULATED_height_minus_333': -333, '_CALCULATED_height_minus_349': -349, '_CALCULATED_height_minus_428': -428, '_CALCULATED_height_minus_740': -740, '_CALCULATED_height_minus_787': -787, '_CALCULATED_height_minus_800': -800, '_CALCULATED_height_minus_803': -803, '_CALCULATED_height_minus_819': -819, '_CALCULATED_height_minus_898': -898, '_CALCULATED_height_minus_940': -940, '_CALCULATED_height_minus_987': -987, '_CALCULATED_Largest_Sum_Door_Width': 0, '_CALCULATED_door_width': 0, '_CALCULATED_even_bottom_width': 0, '_CALCULATED_even_middle_width': 0, '_CALCULATED_even_top_width': 0, '_CALCULATED_jamb_adaptor_left_length_mm': 20, '_CALCULATED_jamb_adaptor_top_length_mm': 40, '_CALCULATED_largest_door_width': 0, '_CALCULATED_largest_left_door_width': 0, '_CALCULATED_largest_right_door_width': 0, '_CALCULATED_left_Clamp_Product': 'no', '_CALCULATED_left_door_middle_width': 0, '_CALCULATED_left_height_door': 0, '_CALCULATED_manual_formula': 'min(0, 0) = 0', '_CALCULATED_right_Clamp_Product': 'no', '_CALCULATED_right_door_middle_width': 0, '_CALCULATED_right_height_door': 0, '_CALCULATED_smallest_door_width': 0, '_CALCULATED_mesh_required': False, '_CALCULATED_mesh_width': 0, '_CALCULATED_mesh_height': 0, '_CALCULATED_midrail_case1': False, '_CALCULATED_mesh_series': '', '_CALCULATED_midrail_case2': True, '_CALCULATED_midrail_height': 0, '_CALCULATED_mesh_area': 0, '_CALCULATED_mesh_operation_required': False, '_CALCULATED_mesh_operation_type': None, '_CALCULATED_mesh_area_m2': 0, '_CALCULATED_mesh_perimeter': 0, '_CALCULATED_mesh_aspect_ratio': 0, '_CALCULATED_mesh_size_category': None} 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Field values keys: ['bx_dbl_hinge_location', 'bx_dbl_hinge_location_other', 'bx_dbl_hinge_jamb_reveal_gt_20mm', 'bx_dbl_hinge_screen_clears_handle', 'bx_dbl_hinge_new_handle_clears_existing', 'bx_dbl_hinge_swing_path_clear', 'bx_dbl_hinge_obstruction_description', 'bx_dbl_hinge_quantity', 'bx_dbl_hinge_frame_colour', 'bx_dbl_hinge_powder_coat_colour', 'bx_dbl_hinge_custom_powder_coat_name', 'bx_dbl_hinge_custom_powder_coat_finish', 'bx_dbl_hinge_custom_powder_coat_code', 'bx_dbl_hinge_deduction_assistance', 'bx_dbl_hinge_door_split_type', 'bx_dbl_hinge_opening_height_mm_even', 'bx_dbl_hinge_height_deduction_mm_even', 'bx_dbl_hinge_make_each_door_height_mm_even', 'bx_dbl_hinge_opening_top_width_mm_even', 'bx_dbl_hinge_top_width_deduction_mm_even', 'bx_dbl_hinge_make_each_door_top_width_mm_even', 'bx_dbl_hinge_opening_middle_width_mm_even', 'bx_dbl_hinge_middle_width_deduction_mm_even', 'bx_dbl_hinge_make_each_door_middle_width_mm_even', 'bx_dbl_hinge_opening_bottom_width_mm_even', 'bx_dbl_hinge_bottom_width_deduction_mm_even', 'bx_dbl_hinge_make_each_door_bottom_width_mm_even', 'bx_dbl_hinge_left_opening_height_mm_uneven', 'bx_dbl_hinge_height_deduction_mm_uneven_left', 'bx_dbl_hinge_make_left_door_height_mm_uneven', 'bx_dbl_hinge_left_jamb_centre_top_mm_uneven', 'bx_dbl_hinge_top_left_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_left_door_top_width_mm_uneven', 'bx_dbl_hinge_left_jamb_centre_middle_mm_uneven', 'bx_dbl_hinge_middle_left_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_left_door_middle_width_mm_uneven', 'bx_dbl_hinge_left_jamb_centre_bottom_mm_uneven', 'bx_dbl_hinge_bottom_left_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_left_door_bottom_width_mm_uneven', 'bx_dbl_hinge_right_opening_height_mm_uneven', 'bx_dbl_hinge_height_deduction_mm_uneven_right', 'bx_dbl_hinge_make_right_door_height_mm_uneven', 'bx_dbl_hinge_right_jamb_centre_top_mm_uneven', 'bx_dbl_hinge_top_right_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_right_door_top_width_mm_uneven', 'bx_dbl_hinge_right_jamb_centre_middle_mm_uneven', 'bx_dbl_hinge_middle_right_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_right_door_middle_width_mm_uneven', 'bx_dbl_hinge_right_jamb_centre_bottom_mm_uneven', 'bx_dbl_hinge_bottom_right_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_right_door_bottom_width_mm_uneven', 'bx_dbl_hinge_make_left_door_height_mm_manual', 'bx_dbl_hinge_make_left_door_top_width_mm_manual', 'bx_dbl_hinge_make_left_door_middle_width_mm_manual', 'bx_dbl_hinge_make_left_door_bottom_width_mm_manual', 'bx_dbl_hinge_make_right_door_height_mm_manual', 'bx_dbl_hinge_make_right_door_top_width_mm_manual', 'bx_dbl_hinge_make_right_door_middle_width_mm_manual', 'bx_dbl_hinge_make_right_door_bottom_width_mm_manual', 'bx_dbl_hinge_swing', 'bx_dbl_hinge_t_section_mullion_inswing', 'bx_dbl_hinge_french_door_mullion_outswing', 'bx_dbl_hinge_lock_brand', 'bx_dbl_hinge_austral_elegance_xc_lock_colour', 'bx_dbl_hinge_austral_elegance_xc_cylinder', 'bx_dbl_hinge_austral_elegance_xc_lock_height_location', 'bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lt_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lt_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_handle', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_handle', 'bx_dbl_hinge_commandex_hinged_lock_colour', 'bx_dbl_hinge_commandex_hinged_cylinder', 'bx_dbl_hinge_commandex_hinged_lock_height_location', 'bx_dbl_hinge_commandex_hinged_lh_top_cut_out', 'bx_dbl_hinge_commandex_hinged_lt_top_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_lt_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_lt_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_centre_handle', 'bx_dbl_hinge_commandex_hinged_lt_centre_handle', 'bx_dbl_hinge_lockwood_8654_lock_colour', 'bx_dbl_hinge_lockwood_8654_cylinder', 'bx_dbl_hinge_lockwood_8654_lock_height_location', 'bx_dbl_hinge_lockwood_8654_lh_top_cut_out', 'bx_dbl_hinge_lockwood_8654_lt_top_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_lt_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_lt_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_centre_handle', 'bx_dbl_hinge_lockwood_8654_lt_centre_handle', 'bx_dbl_hinge_whitco_mk2_lock_colour', 'bx_dbl_hinge_whitco_mk2_cylinder', 'bx_dbl_hinge_whitco_mk2_lock_height_location', 'bx_dbl_hinge_whitco_mk2_lh_top_cut_out', 'bx_dbl_hinge_whitco_mk2_lt_top_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_lt_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_lt_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_centre_handle', 'bx_dbl_hinge_whitco_mk2_lt_centre_handle', 'bx_dbl_hinge_whitco_tasman_escape_lock_colour', 'bx_dbl_hinge_whitco_tasman_escape_cylinder', 'bx_dbl_hinge_whitco_tasman_escape_lock_height_location', 'bx_dbl_hinge_whitco_tasman_escape_lh_top_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lt_top_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_bottom_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lt_bottom_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_handle', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_handle', 'bx_dbl_hinge_austral_elegance_xc_co_cylinder', 'bx_dbl_hinge_austral_elegance_xc_co_lock_height_location', 'bx_dbl_hinge_austral_elegance_xc_co_lh_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lt_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lt_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_handle', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_handle', 'bx_dbl_hinge_commandex_hinged_co_cylinder', 'bx_dbl_hinge_commandex_hinged_co_lock_height_location', 'bx_dbl_hinge_commandex_hinged_co_lh_top_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lt_top_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lt_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_handle', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_handle', 'bx_dbl_hinge_lockwood_8654_co_cylinder', 'bx_dbl_hinge_lockwood_8654_co_lock_height_location', 'bx_dbl_hinge_lockwood_8654_co_lh_top_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lt_top_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lt_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_handle', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_handle', 'bx_dbl_hinge_whitco_mk2_co_cylinder', 'bx_dbl_hinge_whitco_mk2_co_lock_height_location', 'bx_dbl_hinge_whitco_mk2_co_lh_top_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lt_top_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lt_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_handle', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_handle', 'bx_dbl_hinge_lock_cut_out_side_dd', 'bx_dbl_hinge_non_lock_door_striker_cutouts', 'bx_dbl_hinge_non_lock_door_bolts', 'bx_dbl_hinge_patio_bolt_colour_non_lock', 'bx_dbl_hinge_midrail_case1', 'bx_dbl_hinge_midrail_height_mm_case1', 'bx_dbl_hinge_midrail_colour_std_case1', 'bx_dbl_hinge_midrail_colour_special_case1', 'bx_dbl_hinge_midrail_case2', 'bx_dbl_hinge_midrail_height_mm_case2', 'bx_dbl_hinge_midrail_colour_std_case2', 'bx_dbl_hinge_midrail_colour_special_case2', 'bx_dbl_hinge_num_sec_hinges_pickup', 'bx_dbl_hinge_num_sec_hinges_deliver', 'bx_dbl_hinge_spec_hinge_loc_2_pickup_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_pickup', 'bx_dbl_hinge_spec_hinge_loc_3_pickup_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_pickup', 'bx_dbl_hinge_spec_hinge_loc_4_pickup_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_pickup', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_pickup', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_pickup', 'bx_dbl_hinge_spec_hinge_loc_2_deliver_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_deliver', 'bx_dbl_hinge_spec_hinge_loc_3_deliver_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_deliver', 'bx_dbl_hinge_spec_hinge_loc_4_deliver_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_deliver', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_deliver', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_deliver', 'bx_dbl_hinge_sec_hinge_type', 'bx_dbl_hinge_sec_hinge_colour_std', 'bx_dbl_hinge_sec_hinge_colour_prong', 'bx_dbl_hinge_sec_hinge_colour_step', 'bx_dbl_hinge_sec_hinge_packers_qty', 'bx_dbl_hinge_closer', 'bx_dbl_hinge_closer_colour_austral', 'bx_dbl_hinge_closer_colour_whitco', 'bx_dbl_hinge_pet_door', 'bx_dbl_hinge_pet_door_colour', 'bx_dbl_hinge_pet_door_location', 'bx_dbl_hinge_pet_door_flexible_flap', 'bx_dbl_hinge_pet_door_surround_infill', 'bx_dbl_hinge_bug_seal_size', 'bx_dbl_hinge_left_bug_seal_length_mm', 'bx_dbl_hinge_right_bug_seal_length_mm', 'bx_dbl_hinge_stop_bead_colour', 'bx_dbl_hinge_stop_bead_left_length_mm', 'bx_dbl_hinge_stop_bead_top_length_mm', 'bx_dbl_hinge_stop_bead_right_length_mm', 'bx_dbl_hinge_adhesive_mohair', 'bx_dbl_hinge_adhesive_mohair_length_mm', 'bx_dbl_hinge_jamb_adaptor_type_case1', 'bx_dbl_hinge_jamb_adaptor_type_case2', 'bx_dbl_hinge_jamb_adaptor_type_case3', 'bx_dbl_hinge_jamb_opening_left_height_mm', 'bx_dbl_hinge_jamb_height_addition_mm', 'bx_dbl_hinge_jamb_adaptor_left_length_mm', 'bx_dbl_hinge_jamb_opening_top_width_mm', 'bx_dbl_hinge_jamb_width_addition_mm', 'bx_dbl_hinge_jamb_adaptor_top_length_mm', 'bx_dbl_hinge_jamb_opening_right_height_mm', 'bx_dbl_hinge_jamb_adaptor_right_length_mm', '_CALCULATED_deduction_assistance', '_CALCULATED_door_split_type', '_CALCULATED_lock_height', '_CALCULATED_manual_left_height', '_CALCULATED_manual_right_height', '_CALCULATED_height_calculation_method', '_CALCULATED_is_even_split', '_CALCULATED_is_manual_mode', '_CALCULATED_is_uneven_split', '_CALCULATED_largest_door_height', '_CALCULATED_smallest_door_height', '_CALCULATED_halfway_point', '_CALCULATED_halfway_minus_79', '_CALCULATED_halfway_plus_16', '_CALCULATED_halfway_plus_32', '_CALCULATED_halfway_plus_79', '_CALCULATED_height_minus_1000', '_CALCULATED_height_minus_1003', '_CALCULATED_height_minus_1019', '_CALCULATED_height_minus_1090', '_CALCULATED_height_minus_1098', '_CALCULATED_height_minus_1137', '_CALCULATED_height_minus_1153', '_CALCULATED_height_minus_1169', '_CALCULATED_height_minus_1248', '_CALCULATED_height_minus_270', '_CALCULATED_height_minus_317', '_CALCULATED_height_minus_330', '_CALCULATED_height_minus_333', '_CALCULATED_height_minus_349', '_CALCULATED_height_minus_428', '_CALCULATED_height_minus_740', '_CALCULATED_height_minus_787', '_CALCULATED_height_minus_800', '_CALCULATED_height_minus_803', '_CALCULATED_height_minus_819', '_CALCULATED_height_minus_898', '_CALCULATED_height_minus_940', '_CALCULATED_height_minus_987', '_CALCULATED_Largest_Sum_Door_Width', '_CALCULATED_door_width', '_CALCULATED_even_bottom_width', '_CALCULATED_even_middle_width', '_CALCULATED_even_top_width', '_CALCULATED_jamb_adaptor_left_length_mm', '_CALCULATED_jamb_adaptor_top_length_mm', '_CALCULATED_largest_door_width', '_CALCULATED_largest_left_door_width', '_CALCULATED_largest_right_door_width', '_CALCULATED_left_Clamp_Product', '_CALCULATED_left_door_middle_width', '_CALCULATED_left_height_door', '_CALCULATED_manual_formula', '_CALCULATED_right_Clamp_Product', '_CALCULATED_right_door_middle_width', '_CALCULATED_right_height_door', '_CALCULATED_smallest_door_width', '_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_mesh_height', '_CALCULATED_midrail_case1', '_CALCULATED_mesh_series', '_CALCULATED_midrail_case2', '_CALCULATED_midrail_height', '_CALCULATED_mesh_area', '_CALCULATED_mesh_operation_required', '_CALCULATED_mesh_operation_type', '_CALCULATED_mesh_area_m2', '_CALCULATED_mesh_perimeter', '_CALCULATED_mesh_aspect_ratio', '_CALCULATED_mesh_size_category'] 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Found quantity multiplier: 1.0 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Using field/option mapping calculation (no config_id) 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] WARNING: This may result in different operation costs than save config 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Returning 41 operations with total cost 95.92 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COST_METRICS] {'config_id': None, 'operation_count': 41, 'total_cost': 95.92, 'calculation_method': 'field_option_mapping', 'timestamp': 'unknown'} 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COST_METRICS] WARNING: Using field/option mapping - may differ from save config 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Calculating operation costs for template 24 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Received field_values: {'bx_dbl_hinge_location': '', 'bx_dbl_hinge_location_other': '', 'bx_dbl_hinge_jamb_reveal_gt_20mm': '', 'bx_dbl_hinge_screen_clears_handle': '', 'bx_dbl_hinge_new_handle_clears_existing': '', 'bx_dbl_hinge_swing_path_clear': '', 'bx_dbl_hinge_obstruction_description': '', 'bx_dbl_hinge_quantity': '1', 'bx_dbl_hinge_frame_colour': 'black_custom_matt_gn248a', 'bx_dbl_hinge_powder_coat_colour': '', 'bx_dbl_hinge_custom_powder_coat_name': '', 'bx_dbl_hinge_custom_powder_coat_finish': '', 'bx_dbl_hinge_custom_powder_coat_code': '', 'bx_dbl_hinge_deduction_assistance': 'no', 'bx_dbl_hinge_door_split_type': 'even', 'bx_dbl_hinge_opening_height_mm_even': '', 'bx_dbl_hinge_height_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_height_mm_even': '', 'bx_dbl_hinge_opening_top_width_mm_even': '', 'bx_dbl_hinge_top_width_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_top_width_mm_even': '', 'bx_dbl_hinge_opening_middle_width_mm_even': '', 'bx_dbl_hinge_middle_width_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_middle_width_mm_even': '', 'bx_dbl_hinge_opening_bottom_width_mm_even': '', 'bx_dbl_hinge_bottom_width_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_bottom_width_mm_even': '', 'bx_dbl_hinge_left_opening_height_mm_uneven': '', 'bx_dbl_hinge_height_deduction_mm_uneven_left': '', 'bx_dbl_hinge_make_left_door_height_mm_uneven': '', 'bx_dbl_hinge_left_jamb_centre_top_mm_uneven': '', 'bx_dbl_hinge_top_left_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_left_door_top_width_mm_uneven': '', 'bx_dbl_hinge_left_jamb_centre_middle_mm_uneven': '', 'bx_dbl_hinge_middle_left_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_left_door_middle_width_mm_uneven': '', 'bx_dbl_hinge_left_jamb_centre_bottom_mm_uneven': '', 'bx_dbl_hinge_bottom_left_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_left_door_bottom_width_mm_uneven': '', 'bx_dbl_hinge_right_opening_height_mm_uneven': '', 'bx_dbl_hinge_height_deduction_mm_uneven_right': '', 'bx_dbl_hinge_make_right_door_height_mm_uneven': '', 'bx_dbl_hinge_right_jamb_centre_top_mm_uneven': '', 'bx_dbl_hinge_top_right_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_right_door_top_width_mm_uneven': '', 'bx_dbl_hinge_right_jamb_centre_middle_mm_uneven': '', 'bx_dbl_hinge_middle_right_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_right_door_middle_width_mm_uneven': '', 'bx_dbl_hinge_right_jamb_centre_bottom_mm_uneven': '', 'bx_dbl_hinge_bottom_right_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_right_door_bottom_width_mm_uneven': '', 'bx_dbl_hinge_make_left_door_height_mm_manual': '', 'bx_dbl_hinge_make_left_door_top_width_mm_manual': '', 'bx_dbl_hinge_make_left_door_middle_width_mm_manual': '', 'bx_dbl_hinge_make_left_door_bottom_width_mm_manual': '', 'bx_dbl_hinge_make_right_door_height_mm_manual': '', 'bx_dbl_hinge_make_right_door_top_width_mm_manual': '', 'bx_dbl_hinge_make_right_door_middle_width_mm_manual': '', 'bx_dbl_hinge_make_right_door_bottom_width_mm_manual': '', 'bx_dbl_hinge_swing': 'outswing', 'bx_dbl_hinge_t_section_mullion_inswing': 'yes', 'bx_dbl_hinge_french_door_mullion_outswing': 'yes', 'bx_dbl_hinge_lock_brand': 'commandex_hinged', 'bx_dbl_hinge_austral_elegance_xc_lock_colour': 'black', 'bx_dbl_hinge_austral_elegance_xc_cylinder': 'austral_5_pin_dbl', 'bx_dbl_hinge_austral_elegance_xc_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_lt_top_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_lh_bottom_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_handle': '', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_handle': 'single', 'bx_dbl_hinge_commandex_hinged_lock_colour': 'black', 'bx_dbl_hinge_commandex_hinged_cylinder': 'commandex_5_pin', 'bx_dbl_hinge_commandex_hinged_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_top_cut_out': '', 'bx_dbl_hinge_commandex_hinged_lt_top_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_lh_centre_cut_out': '', 'bx_dbl_hinge_commandex_hinged_lt_centre_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_lh_bottom_cut_out': '', 'bx_dbl_hinge_commandex_hinged_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_lh_centre_handle': '', 'bx_dbl_hinge_commandex_hinged_lt_centre_handle': 'single', 'bx_dbl_hinge_lockwood_8654_lock_colour': 'black', 'bx_dbl_hinge_lockwood_8654_cylinder': 'whitco_5_pin', 'bx_dbl_hinge_lockwood_8654_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_top_cut_out': '', 'bx_dbl_hinge_lockwood_8654_lt_top_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_lh_centre_cut_out': '', 'bx_dbl_hinge_lockwood_8654_lt_centre_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_lh_bottom_cut_out': '', 'bx_dbl_hinge_lockwood_8654_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_lh_centre_handle': '', 'bx_dbl_hinge_lockwood_8654_lt_centre_handle': 'single', 'bx_dbl_hinge_whitco_mk2_lock_colour': 'black', 'bx_dbl_hinge_whitco_mk2_cylinder': 'whitco_5_pin', 'bx_dbl_hinge_whitco_mk2_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_top_cut_out': '', 'bx_dbl_hinge_whitco_mk2_lt_top_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_lh_centre_cut_out': '', 'bx_dbl_hinge_whitco_mk2_lt_centre_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_lh_bottom_cut_out': '', 'bx_dbl_hinge_whitco_mk2_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_lh_centre_handle': '', 'bx_dbl_hinge_whitco_mk2_lt_centre_handle': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lock_colour': 'black', 'bx_dbl_hinge_whitco_tasman_escape_cylinder': 'whitco_escape_cylinder_turn_knob', 'bx_dbl_hinge_whitco_tasman_escape_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_top_cut_out': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_top_cut_out': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_cut_out': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_cut_out': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lh_bottom_cut_out': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_handle': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_handle': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_austral_elegance_xc_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_top_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_handle': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_handle': 'single', 'bx_dbl_hinge_commandex_hinged_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_commandex_hinged_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_top_cut_out': '', 'bx_dbl_hinge_commandex_hinged_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_commandex_hinged_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_cut_out': '', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_handle': '', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_handle': 'single', 'bx_dbl_hinge_lockwood_8654_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_lockwood_8654_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_top_cut_out': '', 'bx_dbl_hinge_lockwood_8654_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_cut_out': '', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_lockwood_8654_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_handle': '', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_handle': 'single', 'bx_dbl_hinge_whitco_mk2_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_whitco_mk2_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_top_cut_out': '', 'bx_dbl_hinge_whitco_mk2_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_cut_out': '', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_whitco_mk2_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_handle': '', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_handle': 'single', 'bx_dbl_hinge_lock_cut_out_side_dd': '', 'bx_dbl_hinge_non_lock_door_striker_cutouts': 'yes', 'bx_dbl_hinge_non_lock_door_bolts': 'top_bottom_flush_bolts', 'bx_dbl_hinge_patio_bolt_colour_non_lock': 'black', 'bx_dbl_hinge_midrail_case1': 'yes', 'bx_dbl_hinge_midrail_height_mm_case1': '', 'bx_dbl_hinge_midrail_colour_std_case1': '', 'bx_dbl_hinge_midrail_colour_special_case1': 'black', 'bx_dbl_hinge_midrail_case2': 'no', 'bx_dbl_hinge_midrail_height_mm_case2': '', 'bx_dbl_hinge_midrail_colour_std_case2': 'black', 'bx_dbl_hinge_midrail_colour_special_case2': 'black', 'bx_dbl_hinge_num_sec_hinges_pickup': '3_per_door_attached', 'bx_dbl_hinge_num_sec_hinges_deliver': '3_per_door_loose_drilled', 'bx_dbl_hinge_spec_hinge_loc_2_pickup_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_pickup': '', 'bx_dbl_hinge_spec_hinge_loc_3_pickup_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_pickup': '', 'bx_dbl_hinge_spec_hinge_loc_4_pickup_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_pickup': '', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_pickup': '', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_pickup': '', 'bx_dbl_hinge_spec_hinge_loc_2_deliver_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_deliver': '', 'bx_dbl_hinge_spec_hinge_loc_3_deliver_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_deliver': '', 'bx_dbl_hinge_spec_hinge_loc_4_deliver_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_deliver': '', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_deliver': '', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_deliver': '', 'bx_dbl_hinge_sec_hinge_type': 'security_hinge', 'bx_dbl_hinge_sec_hinge_colour_std': 'black', 'bx_dbl_hinge_sec_hinge_colour_prong': 'black', 'bx_dbl_hinge_sec_hinge_colour_step': 'black', 'bx_dbl_hinge_sec_hinge_packers_qty': '0', 'bx_dbl_hinge_closer': 'no', 'bx_dbl_hinge_closer_colour_austral': 'black', 'bx_dbl_hinge_closer_colour_whitco': 'black', 'bx_dbl_hinge_pet_door': 'no', 'bx_dbl_hinge_pet_door_colour': 'black', 'bx_dbl_hinge_pet_door_location': 'lock_door_lock_side', 'bx_dbl_hinge_pet_door_flexible_flap': 'no', 'bx_dbl_hinge_pet_door_surround_infill': 'no', 'bx_dbl_hinge_bug_seal_size': 'no', 'bx_dbl_hinge_left_bug_seal_length_mm': '', 'bx_dbl_hinge_right_bug_seal_length_mm': '', 'bx_dbl_hinge_stop_bead_colour': 'no', 'bx_dbl_hinge_stop_bead_left_length_mm': '', 'bx_dbl_hinge_stop_bead_top_length_mm': '', 'bx_dbl_hinge_stop_bead_right_length_mm': '', 'bx_dbl_hinge_adhesive_mohair': 'no', 'bx_dbl_hinge_adhesive_mohair_length_mm': '', 'bx_dbl_hinge_jamb_adaptor_type_case1': 'no', 'bx_dbl_hinge_jamb_adaptor_type_case2': '', 'bx_dbl_hinge_jamb_adaptor_type_case3': '', 'bx_dbl_hinge_jamb_opening_left_height_mm': '', 'bx_dbl_hinge_jamb_height_addition_mm': '20', 'bx_dbl_hinge_jamb_adaptor_left_length_mm': '', 'bx_dbl_hinge_jamb_opening_top_width_mm': '', 'bx_dbl_hinge_jamb_width_addition_mm': '40', 'bx_dbl_hinge_jamb_adaptor_top_length_mm': '', 'bx_dbl_hinge_jamb_opening_right_height_mm': '', 'bx_dbl_hinge_jamb_adaptor_right_length_mm': '', '_CALCULATED_deduction_assistance': '', '_CALCULATED_door_split_type': 'even', '_CALCULATED_lock_height': 0, '_CALCULATED_manual_left_height': 0, '_CALCULATED_manual_right_height': 0, '_CALCULATED_height_calculation_method': 'even', '_CALCULATED_is_even_split': True, '_CALCULATED_is_manual_mode': False, '_CALCULATED_is_uneven_split': False, '_CALCULATED_largest_door_height': 0, '_CALCULATED_smallest_door_height': 20, '_CALCULATED_halfway_point': 0, '_CALCULATED_halfway_minus_79': -79, '_CALCULATED_halfway_plus_16': 16, '_CALCULATED_halfway_plus_32': 32, '_CALCULATED_halfway_plus_79': 79, '_CALCULATED_height_minus_1000': -1000, '_CALCULATED_height_minus_1003': -1003, '_CALCULATED_height_minus_1019': -1019, '_CALCULATED_height_minus_1090': -1090, '_CALCULATED_height_minus_1098': -1098, '_CALCULATED_height_minus_1137': -1137, '_CALCULATED_height_minus_1153': -1153, '_CALCULATED_height_minus_1169': -1169, '_CALCULATED_height_minus_1248': -1248, '_CALCULATED_height_minus_270': -270, '_CALCULATED_height_minus_317': -317, '_CALCULATED_height_minus_330': -330, '_CALCULATED_height_minus_333': -333, '_CALCULATED_height_minus_349': -349, '_CALCULATED_height_minus_428': -428, '_CALCULATED_height_minus_740': -740, '_CALCULATED_height_minus_787': -787, '_CALCULATED_height_minus_800': -800, '_CALCULATED_height_minus_803': -803, '_CALCULATED_height_minus_819': -819, '_CALCULATED_height_minus_898': -898, '_CALCULATED_height_minus_940': -940, '_CALCULATED_height_minus_987': -987, '_CALCULATED_Largest_Sum_Door_Width': 0, '_CALCULATED_door_width': 0, '_CALCULATED_even_bottom_width': 0, '_CALCULATED_even_middle_width': 0, '_CALCULATED_even_top_width': 0, '_CALCULATED_jamb_adaptor_left_length_mm': 20, '_CALCULATED_jamb_adaptor_top_length_mm': 40, '_CALCULATED_largest_door_width': 0, '_CALCULATED_largest_left_door_width': 0, '_CALCULATED_largest_right_door_width': 0, '_CALCULATED_left_Clamp_Product': 'no', '_CALCULATED_left_door_middle_width': 0, '_CALCULATED_left_height_door': 0, '_CALCULATED_manual_formula': 'min(0, 0) = 0', '_CALCULATED_right_Clamp_Product': 'no', '_CALCULATED_right_door_middle_width': 0, '_CALCULATED_right_height_door': 0, '_CALCULATED_smallest_door_width': 0, '_CALCULATED_mesh_required': False, '_CALCULATED_mesh_width': 0, '_CALCULATED_mesh_height': 0, '_CALCULATED_midrail_case1': False, '_CALCULATED_mesh_series': '', '_CALCULATED_midrail_case2': True, '_CALCULATED_midrail_height': 0, '_CALCULATED_mesh_area': 0, '_CALCULATED_mesh_operation_required': False, '_CALCULATED_mesh_operation_type': None, '_CALCULATED_mesh_area_m2': 0, '_CALCULATED_mesh_perimeter': 0, '_CALCULATED_mesh_aspect_ratio': 0, '_CALCULATED_mesh_size_category': None} 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Field values keys: ['bx_dbl_hinge_location', 'bx_dbl_hinge_location_other', 'bx_dbl_hinge_jamb_reveal_gt_20mm', 'bx_dbl_hinge_screen_clears_handle', 'bx_dbl_hinge_new_handle_clears_existing', 'bx_dbl_hinge_swing_path_clear', 'bx_dbl_hinge_obstruction_description', 'bx_dbl_hinge_quantity', 'bx_dbl_hinge_frame_colour', 'bx_dbl_hinge_powder_coat_colour', 'bx_dbl_hinge_custom_powder_coat_name', 'bx_dbl_hinge_custom_powder_coat_finish', 'bx_dbl_hinge_custom_powder_coat_code', 'bx_dbl_hinge_deduction_assistance', 'bx_dbl_hinge_door_split_type', 'bx_dbl_hinge_opening_height_mm_even', 'bx_dbl_hinge_height_deduction_mm_even', 'bx_dbl_hinge_make_each_door_height_mm_even', 'bx_dbl_hinge_opening_top_width_mm_even', 'bx_dbl_hinge_top_width_deduction_mm_even', 'bx_dbl_hinge_make_each_door_top_width_mm_even', 'bx_dbl_hinge_opening_middle_width_mm_even', 'bx_dbl_hinge_middle_width_deduction_mm_even', 'bx_dbl_hinge_make_each_door_middle_width_mm_even', 'bx_dbl_hinge_opening_bottom_width_mm_even', 'bx_dbl_hinge_bottom_width_deduction_mm_even', 'bx_dbl_hinge_make_each_door_bottom_width_mm_even', 'bx_dbl_hinge_left_opening_height_mm_uneven', 'bx_dbl_hinge_height_deduction_mm_uneven_left', 'bx_dbl_hinge_make_left_door_height_mm_uneven', 'bx_dbl_hinge_left_jamb_centre_top_mm_uneven', 'bx_dbl_hinge_top_left_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_left_door_top_width_mm_uneven', 'bx_dbl_hinge_left_jamb_centre_middle_mm_uneven', 'bx_dbl_hinge_middle_left_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_left_door_middle_width_mm_uneven', 'bx_dbl_hinge_left_jamb_centre_bottom_mm_uneven', 'bx_dbl_hinge_bottom_left_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_left_door_bottom_width_mm_uneven', 'bx_dbl_hinge_right_opening_height_mm_uneven', 'bx_dbl_hinge_height_deduction_mm_uneven_right', 'bx_dbl_hinge_make_right_door_height_mm_uneven', 'bx_dbl_hinge_right_jamb_centre_top_mm_uneven', 'bx_dbl_hinge_top_right_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_right_door_top_width_mm_uneven', 'bx_dbl_hinge_right_jamb_centre_middle_mm_uneven', 'bx_dbl_hinge_middle_right_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_right_door_middle_width_mm_uneven', 'bx_dbl_hinge_right_jamb_centre_bottom_mm_uneven', 'bx_dbl_hinge_bottom_right_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_right_door_bottom_width_mm_uneven', 'bx_dbl_hinge_make_left_door_height_mm_manual', 'bx_dbl_hinge_make_left_door_top_width_mm_manual', 'bx_dbl_hinge_make_left_door_middle_width_mm_manual', 'bx_dbl_hinge_make_left_door_bottom_width_mm_manual', 'bx_dbl_hinge_make_right_door_height_mm_manual', 'bx_dbl_hinge_make_right_door_top_width_mm_manual', 'bx_dbl_hinge_make_right_door_middle_width_mm_manual', 'bx_dbl_hinge_make_right_door_bottom_width_mm_manual', 'bx_dbl_hinge_swing', 'bx_dbl_hinge_t_section_mullion_inswing', 'bx_dbl_hinge_french_door_mullion_outswing', 'bx_dbl_hinge_lock_brand', 'bx_dbl_hinge_austral_elegance_xc_lock_colour', 'bx_dbl_hinge_austral_elegance_xc_cylinder', 'bx_dbl_hinge_austral_elegance_xc_lock_height_location', 'bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lt_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lt_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_handle', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_handle', 'bx_dbl_hinge_commandex_hinged_lock_colour', 'bx_dbl_hinge_commandex_hinged_cylinder', 'bx_dbl_hinge_commandex_hinged_lock_height_location', 'bx_dbl_hinge_commandex_hinged_lh_top_cut_out', 'bx_dbl_hinge_commandex_hinged_lt_top_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_lt_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_lt_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_centre_handle', 'bx_dbl_hinge_commandex_hinged_lt_centre_handle', 'bx_dbl_hinge_lockwood_8654_lock_colour', 'bx_dbl_hinge_lockwood_8654_cylinder', 'bx_dbl_hinge_lockwood_8654_lock_height_location', 'bx_dbl_hinge_lockwood_8654_lh_top_cut_out', 'bx_dbl_hinge_lockwood_8654_lt_top_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_lt_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_lt_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_centre_handle', 'bx_dbl_hinge_lockwood_8654_lt_centre_handle', 'bx_dbl_hinge_whitco_mk2_lock_colour', 'bx_dbl_hinge_whitco_mk2_cylinder', 'bx_dbl_hinge_whitco_mk2_lock_height_location', 'bx_dbl_hinge_whitco_mk2_lh_top_cut_out', 'bx_dbl_hinge_whitco_mk2_lt_top_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_lt_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_lt_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_centre_handle', 'bx_dbl_hinge_whitco_mk2_lt_centre_handle', 'bx_dbl_hinge_whitco_tasman_escape_lock_colour', 'bx_dbl_hinge_whitco_tasman_escape_cylinder', 'bx_dbl_hinge_whitco_tasman_escape_lock_height_location', 'bx_dbl_hinge_whitco_tasman_escape_lh_top_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lt_top_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_bottom_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lt_bottom_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_handle', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_handle', 'bx_dbl_hinge_austral_elegance_xc_co_cylinder', 'bx_dbl_hinge_austral_elegance_xc_co_lock_height_location', 'bx_dbl_hinge_austral_elegance_xc_co_lh_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lt_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lt_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_handle', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_handle', 'bx_dbl_hinge_commandex_hinged_co_cylinder', 'bx_dbl_hinge_commandex_hinged_co_lock_height_location', 'bx_dbl_hinge_commandex_hinged_co_lh_top_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lt_top_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lt_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_handle', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_handle', 'bx_dbl_hinge_lockwood_8654_co_cylinder', 'bx_dbl_hinge_lockwood_8654_co_lock_height_location', 'bx_dbl_hinge_lockwood_8654_co_lh_top_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lt_top_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lt_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_handle', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_handle', 'bx_dbl_hinge_whitco_mk2_co_cylinder', 'bx_dbl_hinge_whitco_mk2_co_lock_height_location', 'bx_dbl_hinge_whitco_mk2_co_lh_top_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lt_top_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lt_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_handle', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_handle', 'bx_dbl_hinge_lock_cut_out_side_dd', 'bx_dbl_hinge_non_lock_door_striker_cutouts', 'bx_dbl_hinge_non_lock_door_bolts', 'bx_dbl_hinge_patio_bolt_colour_non_lock', 'bx_dbl_hinge_midrail_case1', 'bx_dbl_hinge_midrail_height_mm_case1', 'bx_dbl_hinge_midrail_colour_std_case1', 'bx_dbl_hinge_midrail_colour_special_case1', 'bx_dbl_hinge_midrail_case2', 'bx_dbl_hinge_midrail_height_mm_case2', 'bx_dbl_hinge_midrail_colour_std_case2', 'bx_dbl_hinge_midrail_colour_special_case2', 'bx_dbl_hinge_num_sec_hinges_pickup', 'bx_dbl_hinge_num_sec_hinges_deliver', 'bx_dbl_hinge_spec_hinge_loc_2_pickup_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_pickup', 'bx_dbl_hinge_spec_hinge_loc_3_pickup_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_pickup', 'bx_dbl_hinge_spec_hinge_loc_4_pickup_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_pickup', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_pickup', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_pickup', 'bx_dbl_hinge_spec_hinge_loc_2_deliver_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_deliver', 'bx_dbl_hinge_spec_hinge_loc_3_deliver_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_deliver', 'bx_dbl_hinge_spec_hinge_loc_4_deliver_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_deliver', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_deliver', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_deliver', 'bx_dbl_hinge_sec_hinge_type', 'bx_dbl_hinge_sec_hinge_colour_std', 'bx_dbl_hinge_sec_hinge_colour_prong', 'bx_dbl_hinge_sec_hinge_colour_step', 'bx_dbl_hinge_sec_hinge_packers_qty', 'bx_dbl_hinge_closer', 'bx_dbl_hinge_closer_colour_austral', 'bx_dbl_hinge_closer_colour_whitco', 'bx_dbl_hinge_pet_door', 'bx_dbl_hinge_pet_door_colour', 'bx_dbl_hinge_pet_door_location', 'bx_dbl_hinge_pet_door_flexible_flap', 'bx_dbl_hinge_pet_door_surround_infill', 'bx_dbl_hinge_bug_seal_size', 'bx_dbl_hinge_left_bug_seal_length_mm', 'bx_dbl_hinge_right_bug_seal_length_mm', 'bx_dbl_hinge_stop_bead_colour', 'bx_dbl_hinge_stop_bead_left_length_mm', 'bx_dbl_hinge_stop_bead_top_length_mm', 'bx_dbl_hinge_stop_bead_right_length_mm', 'bx_dbl_hinge_adhesive_mohair', 'bx_dbl_hinge_adhesive_mohair_length_mm', 'bx_dbl_hinge_jamb_adaptor_type_case1', 'bx_dbl_hinge_jamb_adaptor_type_case2', 'bx_dbl_hinge_jamb_adaptor_type_case3', 'bx_dbl_hinge_jamb_opening_left_height_mm', 'bx_dbl_hinge_jamb_height_addition_mm', 'bx_dbl_hinge_jamb_adaptor_left_length_mm', 'bx_dbl_hinge_jamb_opening_top_width_mm', 'bx_dbl_hinge_jamb_width_addition_mm', 'bx_dbl_hinge_jamb_adaptor_top_length_mm', 'bx_dbl_hinge_jamb_opening_right_height_mm', 'bx_dbl_hinge_jamb_adaptor_right_length_mm', '_CALCULATED_deduction_assistance', '_CALCULATED_door_split_type', '_CALCULATED_lock_height', '_CALCULATED_manual_left_height', '_CALCULATED_manual_right_height', '_CALCULATED_height_calculation_method', '_CALCULATED_is_even_split', '_CALCULATED_is_manual_mode', '_CALCULATED_is_uneven_split', '_CALCULATED_largest_door_height', '_CALCULATED_smallest_door_height', '_CALCULATED_halfway_point', '_CALCULATED_halfway_minus_79', '_CALCULATED_halfway_plus_16', '_CALCULATED_halfway_plus_32', '_CALCULATED_halfway_plus_79', '_CALCULATED_height_minus_1000', '_CALCULATED_height_minus_1003', '_CALCULATED_height_minus_1019', '_CALCULATED_height_minus_1090', '_CALCULATED_height_minus_1098', '_CALCULATED_height_minus_1137', '_CALCULATED_height_minus_1153', '_CALCULATED_height_minus_1169', '_CALCULATED_height_minus_1248', '_CALCULATED_height_minus_270', '_CALCULATED_height_minus_317', '_CALCULATED_height_minus_330', '_CALCULATED_height_minus_333', '_CALCULATED_height_minus_349', '_CALCULATED_height_minus_428', '_CALCULATED_height_minus_740', '_CALCULATED_height_minus_787', '_CALCULATED_height_minus_800', '_CALCULATED_height_minus_803', '_CALCULATED_height_minus_819', '_CALCULATED_height_minus_898', '_CALCULATED_height_minus_940', '_CALCULATED_height_minus_987', '_CALCULATED_Largest_Sum_Door_Width', '_CALCULATED_door_width', '_CALCULATED_even_bottom_width', '_CALCULATED_even_middle_width', '_CALCULATED_even_top_width', '_CALCULATED_jamb_adaptor_left_length_mm', '_CALCULATED_jamb_adaptor_top_length_mm', '_CALCULATED_largest_door_width', '_CALCULATED_largest_left_door_width', '_CALCULATED_largest_right_door_width', '_CALCULATED_left_Clamp_Product', '_CALCULATED_left_door_middle_width', '_CALCULATED_left_height_door', '_CALCULATED_manual_formula', '_CALCULATED_right_Clamp_Product', '_CALCULATED_right_door_middle_width', '_CALCULATED_right_height_door', '_CALCULATED_smallest_door_width', '_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_mesh_height', '_CALCULATED_midrail_case1', '_CALCULATED_mesh_series', '_CALCULATED_midrail_case2', '_CALCULATED_midrail_height', '_CALCULATED_mesh_area', '_CALCULATED_mesh_operation_required', '_CALCULATED_mesh_operation_type', '_CALCULATED_mesh_area_m2', '_CALCULATED_mesh_perimeter', '_CALCULATED_mesh_aspect_ratio', '_CALCULATED_mesh_size_category'] 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Found quantity multiplier: 1.0 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Using field/option mapping calculation (no config_id) 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] WARNING: This may result in different operation costs than save config 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Returning 41 operations with total cost 95.92 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COST_METRICS] {'config_id': None, 'operation_count': 41, 'total_cost': 95.92, 'calculation_method': 'field_option_mapping', 'timestamp': 'unknown'} 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COST_METRICS] WARNING: Using field/option mapping - may differ from save config 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Calculating operation costs for template 24 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Received field_values: {'bx_dbl_hinge_location': '', 'bx_dbl_hinge_location_other': '', 'bx_dbl_hinge_jamb_reveal_gt_20mm': '', 'bx_dbl_hinge_screen_clears_handle': '', 'bx_dbl_hinge_new_handle_clears_existing': '', 'bx_dbl_hinge_swing_path_clear': '', 'bx_dbl_hinge_obstruction_description': '', 'bx_dbl_hinge_quantity': '1', 'bx_dbl_hinge_frame_colour': 'black_custom_matt_gn248a', 'bx_dbl_hinge_powder_coat_colour': '', 'bx_dbl_hinge_custom_powder_coat_name': '', 'bx_dbl_hinge_custom_powder_coat_finish': '', 'bx_dbl_hinge_custom_powder_coat_code': '', 'bx_dbl_hinge_deduction_assistance': 'no', 'bx_dbl_hinge_door_split_type': 'even', 'bx_dbl_hinge_opening_height_mm_even': '', 'bx_dbl_hinge_height_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_height_mm_even': '', 'bx_dbl_hinge_opening_top_width_mm_even': '', 'bx_dbl_hinge_top_width_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_top_width_mm_even': '', 'bx_dbl_hinge_opening_middle_width_mm_even': '', 'bx_dbl_hinge_middle_width_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_middle_width_mm_even': '', 'bx_dbl_hinge_opening_bottom_width_mm_even': '', 'bx_dbl_hinge_bottom_width_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_bottom_width_mm_even': '', 'bx_dbl_hinge_left_opening_height_mm_uneven': '', 'bx_dbl_hinge_height_deduction_mm_uneven_left': '', 'bx_dbl_hinge_make_left_door_height_mm_uneven': '', 'bx_dbl_hinge_left_jamb_centre_top_mm_uneven': '', 'bx_dbl_hinge_top_left_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_left_door_top_width_mm_uneven': '', 'bx_dbl_hinge_left_jamb_centre_middle_mm_uneven': '', 'bx_dbl_hinge_middle_left_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_left_door_middle_width_mm_uneven': '', 'bx_dbl_hinge_left_jamb_centre_bottom_mm_uneven': '', 'bx_dbl_hinge_bottom_left_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_left_door_bottom_width_mm_uneven': '', 'bx_dbl_hinge_right_opening_height_mm_uneven': '', 'bx_dbl_hinge_height_deduction_mm_uneven_right': '', 'bx_dbl_hinge_make_right_door_height_mm_uneven': '', 'bx_dbl_hinge_right_jamb_centre_top_mm_uneven': '', 'bx_dbl_hinge_top_right_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_right_door_top_width_mm_uneven': '', 'bx_dbl_hinge_right_jamb_centre_middle_mm_uneven': '', 'bx_dbl_hinge_middle_right_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_right_door_middle_width_mm_uneven': '', 'bx_dbl_hinge_right_jamb_centre_bottom_mm_uneven': '', 'bx_dbl_hinge_bottom_right_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_right_door_bottom_width_mm_uneven': '', 'bx_dbl_hinge_make_left_door_height_mm_manual': '2110', 'bx_dbl_hinge_make_left_door_top_width_mm_manual': '860', 'bx_dbl_hinge_make_left_door_middle_width_mm_manual': '', 'bx_dbl_hinge_make_left_door_bottom_width_mm_manual': '', 'bx_dbl_hinge_make_right_door_height_mm_manual': '2110', 'bx_dbl_hinge_make_right_door_top_width_mm_manual': '', 'bx_dbl_hinge_make_right_door_middle_width_mm_manual': '', 'bx_dbl_hinge_make_right_door_bottom_width_mm_manual': '', 'bx_dbl_hinge_swing': 'outswing', 'bx_dbl_hinge_t_section_mullion_inswing': 'yes', 'bx_dbl_hinge_french_door_mullion_outswing': 'yes', 'bx_dbl_hinge_lock_brand': 'commandex_hinged', 'bx_dbl_hinge_austral_elegance_xc_lock_colour': 'black', 'bx_dbl_hinge_austral_elegance_xc_cylinder': 'austral_5_pin_dbl', 'bx_dbl_hinge_austral_elegance_xc_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_lt_top_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_lh_bottom_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_handle': '', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_handle': 'single', 'bx_dbl_hinge_commandex_hinged_lock_colour': 'black', 'bx_dbl_hinge_commandex_hinged_cylinder': 'commandex_5_pin', 'bx_dbl_hinge_commandex_hinged_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_top_cut_out': '', 'bx_dbl_hinge_commandex_hinged_lt_top_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_lh_centre_cut_out': '', 'bx_dbl_hinge_commandex_hinged_lt_centre_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_lh_bottom_cut_out': '', 'bx_dbl_hinge_commandex_hinged_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_lh_centre_handle': '', 'bx_dbl_hinge_commandex_hinged_lt_centre_handle': 'single', 'bx_dbl_hinge_lockwood_8654_lock_colour': 'black', 'bx_dbl_hinge_lockwood_8654_cylinder': 'whitco_5_pin', 'bx_dbl_hinge_lockwood_8654_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_top_cut_out': '', 'bx_dbl_hinge_lockwood_8654_lt_top_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_lh_centre_cut_out': '', 'bx_dbl_hinge_lockwood_8654_lt_centre_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_lh_bottom_cut_out': '', 'bx_dbl_hinge_lockwood_8654_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_lh_centre_handle': '', 'bx_dbl_hinge_lockwood_8654_lt_centre_handle': 'single', 'bx_dbl_hinge_whitco_mk2_lock_colour': 'black', 'bx_dbl_hinge_whitco_mk2_cylinder': 'whitco_5_pin', 'bx_dbl_hinge_whitco_mk2_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_top_cut_out': '', 'bx_dbl_hinge_whitco_mk2_lt_top_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_lh_centre_cut_out': '', 'bx_dbl_hinge_whitco_mk2_lt_centre_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_lh_bottom_cut_out': '', 'bx_dbl_hinge_whitco_mk2_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_lh_centre_handle': '', 'bx_dbl_hinge_whitco_mk2_lt_centre_handle': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lock_colour': 'black', 'bx_dbl_hinge_whitco_tasman_escape_cylinder': 'whitco_escape_cylinder_turn_knob', 'bx_dbl_hinge_whitco_tasman_escape_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_top_cut_out': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_top_cut_out': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_cut_out': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_cut_out': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lh_bottom_cut_out': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_handle': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_handle': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_austral_elegance_xc_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_top_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_handle': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_handle': 'single', 'bx_dbl_hinge_commandex_hinged_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_commandex_hinged_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_top_cut_out': '', 'bx_dbl_hinge_commandex_hinged_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_commandex_hinged_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_cut_out': '', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_handle': '', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_handle': 'single', 'bx_dbl_hinge_lockwood_8654_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_lockwood_8654_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_top_cut_out': '', 'bx_dbl_hinge_lockwood_8654_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_cut_out': '', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_lockwood_8654_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_handle': '', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_handle': 'single', 'bx_dbl_hinge_whitco_mk2_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_whitco_mk2_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_top_cut_out': '', 'bx_dbl_hinge_whitco_mk2_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_cut_out': '', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_whitco_mk2_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_handle': '', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_handle': 'single', 'bx_dbl_hinge_lock_cut_out_side_dd': '', 'bx_dbl_hinge_non_lock_door_striker_cutouts': 'yes', 'bx_dbl_hinge_non_lock_door_bolts': 'top_bottom_flush_bolts', 'bx_dbl_hinge_patio_bolt_colour_non_lock': 'black', 'bx_dbl_hinge_midrail_case1': 'yes', 'bx_dbl_hinge_midrail_height_mm_case1': '', 'bx_dbl_hinge_midrail_colour_std_case1': '', 'bx_dbl_hinge_midrail_colour_special_case1': 'black', 'bx_dbl_hinge_midrail_case2': 'no', 'bx_dbl_hinge_midrail_height_mm_case2': '', 'bx_dbl_hinge_midrail_colour_std_case2': 'black', 'bx_dbl_hinge_midrail_colour_special_case2': 'black', 'bx_dbl_hinge_num_sec_hinges_pickup': '3_per_door_attached', 'bx_dbl_hinge_num_sec_hinges_deliver': '3_per_door_loose_drilled', 'bx_dbl_hinge_spec_hinge_loc_2_pickup_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_pickup': '', 'bx_dbl_hinge_spec_hinge_loc_3_pickup_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_pickup': '', 'bx_dbl_hinge_spec_hinge_loc_4_pickup_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_pickup': '', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_pickup': '', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_pickup': '', 'bx_dbl_hinge_spec_hinge_loc_2_deliver_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_deliver': '', 'bx_dbl_hinge_spec_hinge_loc_3_deliver_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_deliver': '', 'bx_dbl_hinge_spec_hinge_loc_4_deliver_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_deliver': '', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_deliver': '', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_deliver': '', 'bx_dbl_hinge_sec_hinge_type': 'security_hinge', 'bx_dbl_hinge_sec_hinge_colour_std': 'black', 'bx_dbl_hinge_sec_hinge_colour_prong': 'black', 'bx_dbl_hinge_sec_hinge_colour_step': 'black', 'bx_dbl_hinge_sec_hinge_packers_qty': '0', 'bx_dbl_hinge_closer': 'no', 'bx_dbl_hinge_closer_colour_austral': 'black', 'bx_dbl_hinge_closer_colour_whitco': 'black', 'bx_dbl_hinge_pet_door': 'no', 'bx_dbl_hinge_pet_door_colour': 'black', 'bx_dbl_hinge_pet_door_location': 'lock_door_lock_side', 'bx_dbl_hinge_pet_door_flexible_flap': 'no', 'bx_dbl_hinge_pet_door_surround_infill': 'no', 'bx_dbl_hinge_bug_seal_size': 'no', 'bx_dbl_hinge_left_bug_seal_length_mm': '', 'bx_dbl_hinge_right_bug_seal_length_mm': '', 'bx_dbl_hinge_stop_bead_colour': 'no', 'bx_dbl_hinge_stop_bead_left_length_mm': '', 'bx_dbl_hinge_stop_bead_top_length_mm': '', 'bx_dbl_hinge_stop_bead_right_length_mm': '', 'bx_dbl_hinge_adhesive_mohair': 'no', 'bx_dbl_hinge_adhesive_mohair_length_mm': '', 'bx_dbl_hinge_jamb_adaptor_type_case1': 'no', 'bx_dbl_hinge_jamb_adaptor_type_case2': '', 'bx_dbl_hinge_jamb_adaptor_type_case3': '', 'bx_dbl_hinge_jamb_opening_left_height_mm': '', 'bx_dbl_hinge_jamb_height_addition_mm': '20', 'bx_dbl_hinge_jamb_adaptor_left_length_mm': '', 'bx_dbl_hinge_jamb_opening_top_width_mm': '', 'bx_dbl_hinge_jamb_width_addition_mm': '40', 'bx_dbl_hinge_jamb_adaptor_top_length_mm': '', 'bx_dbl_hinge_jamb_opening_right_height_mm': '', 'bx_dbl_hinge_jamb_adaptor_right_length_mm': '', '_CALCULATED_deduction_assistance': 'no', '_CALCULATED_door_split_type': 'even', '_CALCULATED_lock_height': 0, '_CALCULATED_manual_left_height': 0, '_CALCULATED_manual_right_height': 0, '_CALCULATED_height_calculation_method': 'manual', '_CALCULATED_is_even_split': True, '_CALCULATED_is_manual_mode': True, '_CALCULATED_is_uneven_split': False, '_CALCULATED_largest_door_height': 0, '_CALCULATED_smallest_door_height': 20, '_CALCULATED_halfway_point': 0, '_CALCULATED_halfway_minus_79': -79, '_CALCULATED_halfway_plus_16': 16, '_CALCULATED_halfway_plus_32': 32, '_CALCULATED_halfway_plus_79': 79, '_CALCULATED_height_minus_1000': -1000, '_CALCULATED_height_minus_1003': -1003, '_CALCULATED_height_minus_1019': -1019, '_CALCULATED_height_minus_1090': -1090, '_CALCULATED_height_minus_1098': -1098, '_CALCULATED_height_minus_1137': -1137, '_CALCULATED_height_minus_1153': -1153, '_CALCULATED_height_minus_1169': -1169, '_CALCULATED_height_minus_1248': -1248, '_CALCULATED_height_minus_270': -270, '_CALCULATED_height_minus_317': -317, '_CALCULATED_height_minus_330': -330, '_CALCULATED_height_minus_333': -333, '_CALCULATED_height_minus_349': -349, '_CALCULATED_height_minus_428': -428, '_CALCULATED_height_minus_740': -740, '_CALCULATED_height_minus_787': -787, '_CALCULATED_height_minus_800': -800, '_CALCULATED_height_minus_803': -803, '_CALCULATED_height_minus_819': -819, '_CALCULATED_height_minus_898': -898, '_CALCULATED_height_minus_940': -940, '_CALCULATED_height_minus_987': -987, '_CALCULATED_Largest_Sum_Door_Width': 0, '_CALCULATED_door_width': 0, '_CALCULATED_even_bottom_width': 0, '_CALCULATED_even_middle_width': 0, '_CALCULATED_even_top_width': 0, '_CALCULATED_jamb_adaptor_left_length_mm': 20, '_CALCULATED_jamb_adaptor_top_length_mm': 40, '_CALCULATED_largest_door_width': 0, '_CALCULATED_largest_left_door_width': 0, '_CALCULATED_largest_right_door_width': 0, '_CALCULATED_left_Clamp_Product': 'no', '_CALCULATED_left_door_middle_width': 0, '_CALCULATED_left_height_door': 0, '_CALCULATED_manual_formula': 'min(0, 0) = 0', '_CALCULATED_right_Clamp_Product': 'no', '_CALCULATED_right_door_middle_width': 0, '_CALCULATED_right_height_door': 0, '_CALCULATED_smallest_door_width': 0, '_CALCULATED_mesh_required': False, '_CALCULATED_mesh_width': 0, '_CALCULATED_mesh_height': 0, '_CALCULATED_midrail_case1': False, '_CALCULATED_mesh_series': '', '_CALCULATED_midrail_case2': True, '_CALCULATED_midrail_height': 0, '_CALCULATED_mesh_area': 0, '_CALCULATED_mesh_operation_required': False, '_CALCULATED_mesh_operation_type': None, '_CALCULATED_mesh_area_m2': 0, '_CALCULATED_mesh_perimeter': 0, '_CALCULATED_mesh_aspect_ratio': 0, '_CALCULATED_mesh_size_category': None} 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Field values keys: ['bx_dbl_hinge_location', 'bx_dbl_hinge_location_other', 'bx_dbl_hinge_jamb_reveal_gt_20mm', 'bx_dbl_hinge_screen_clears_handle', 'bx_dbl_hinge_new_handle_clears_existing', 'bx_dbl_hinge_swing_path_clear', 'bx_dbl_hinge_obstruction_description', 'bx_dbl_hinge_quantity', 'bx_dbl_hinge_frame_colour', 'bx_dbl_hinge_powder_coat_colour', 'bx_dbl_hinge_custom_powder_coat_name', 'bx_dbl_hinge_custom_powder_coat_finish', 'bx_dbl_hinge_custom_powder_coat_code', 'bx_dbl_hinge_deduction_assistance', 'bx_dbl_hinge_door_split_type', 'bx_dbl_hinge_opening_height_mm_even', 'bx_dbl_hinge_height_deduction_mm_even', 'bx_dbl_hinge_make_each_door_height_mm_even', 'bx_dbl_hinge_opening_top_width_mm_even', 'bx_dbl_hinge_top_width_deduction_mm_even', 'bx_dbl_hinge_make_each_door_top_width_mm_even', 'bx_dbl_hinge_opening_middle_width_mm_even', 'bx_dbl_hinge_middle_width_deduction_mm_even', 'bx_dbl_hinge_make_each_door_middle_width_mm_even', 'bx_dbl_hinge_opening_bottom_width_mm_even', 'bx_dbl_hinge_bottom_width_deduction_mm_even', 'bx_dbl_hinge_make_each_door_bottom_width_mm_even', 'bx_dbl_hinge_left_opening_height_mm_uneven', 'bx_dbl_hinge_height_deduction_mm_uneven_left', 'bx_dbl_hinge_make_left_door_height_mm_uneven', 'bx_dbl_hinge_left_jamb_centre_top_mm_uneven', 'bx_dbl_hinge_top_left_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_left_door_top_width_mm_uneven', 'bx_dbl_hinge_left_jamb_centre_middle_mm_uneven', 'bx_dbl_hinge_middle_left_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_left_door_middle_width_mm_uneven', 'bx_dbl_hinge_left_jamb_centre_bottom_mm_uneven', 'bx_dbl_hinge_bottom_left_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_left_door_bottom_width_mm_uneven', 'bx_dbl_hinge_right_opening_height_mm_uneven', 'bx_dbl_hinge_height_deduction_mm_uneven_right', 'bx_dbl_hinge_make_right_door_height_mm_uneven', 'bx_dbl_hinge_right_jamb_centre_top_mm_uneven', 'bx_dbl_hinge_top_right_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_right_door_top_width_mm_uneven', 'bx_dbl_hinge_right_jamb_centre_middle_mm_uneven', 'bx_dbl_hinge_middle_right_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_right_door_middle_width_mm_uneven', 'bx_dbl_hinge_right_jamb_centre_bottom_mm_uneven', 'bx_dbl_hinge_bottom_right_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_right_door_bottom_width_mm_uneven', 'bx_dbl_hinge_make_left_door_height_mm_manual', 'bx_dbl_hinge_make_left_door_top_width_mm_manual', 'bx_dbl_hinge_make_left_door_middle_width_mm_manual', 'bx_dbl_hinge_make_left_door_bottom_width_mm_manual', 'bx_dbl_hinge_make_right_door_height_mm_manual', 'bx_dbl_hinge_make_right_door_top_width_mm_manual', 'bx_dbl_hinge_make_right_door_middle_width_mm_manual', 'bx_dbl_hinge_make_right_door_bottom_width_mm_manual', 'bx_dbl_hinge_swing', 'bx_dbl_hinge_t_section_mullion_inswing', 'bx_dbl_hinge_french_door_mullion_outswing', 'bx_dbl_hinge_lock_brand', 'bx_dbl_hinge_austral_elegance_xc_lock_colour', 'bx_dbl_hinge_austral_elegance_xc_cylinder', 'bx_dbl_hinge_austral_elegance_xc_lock_height_location', 'bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lt_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lt_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_handle', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_handle', 'bx_dbl_hinge_commandex_hinged_lock_colour', 'bx_dbl_hinge_commandex_hinged_cylinder', 'bx_dbl_hinge_commandex_hinged_lock_height_location', 'bx_dbl_hinge_commandex_hinged_lh_top_cut_out', 'bx_dbl_hinge_commandex_hinged_lt_top_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_lt_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_lt_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_centre_handle', 'bx_dbl_hinge_commandex_hinged_lt_centre_handle', 'bx_dbl_hinge_lockwood_8654_lock_colour', 'bx_dbl_hinge_lockwood_8654_cylinder', 'bx_dbl_hinge_lockwood_8654_lock_height_location', 'bx_dbl_hinge_lockwood_8654_lh_top_cut_out', 'bx_dbl_hinge_lockwood_8654_lt_top_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_lt_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_lt_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_centre_handle', 'bx_dbl_hinge_lockwood_8654_lt_centre_handle', 'bx_dbl_hinge_whitco_mk2_lock_colour', 'bx_dbl_hinge_whitco_mk2_cylinder', 'bx_dbl_hinge_whitco_mk2_lock_height_location', 'bx_dbl_hinge_whitco_mk2_lh_top_cut_out', 'bx_dbl_hinge_whitco_mk2_lt_top_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_lt_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_lt_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_centre_handle', 'bx_dbl_hinge_whitco_mk2_lt_centre_handle', 'bx_dbl_hinge_whitco_tasman_escape_lock_colour', 'bx_dbl_hinge_whitco_tasman_escape_cylinder', 'bx_dbl_hinge_whitco_tasman_escape_lock_height_location', 'bx_dbl_hinge_whitco_tasman_escape_lh_top_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lt_top_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_bottom_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lt_bottom_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_handle', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_handle', 'bx_dbl_hinge_austral_elegance_xc_co_cylinder', 'bx_dbl_hinge_austral_elegance_xc_co_lock_height_location', 'bx_dbl_hinge_austral_elegance_xc_co_lh_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lt_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lt_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_handle', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_handle', 'bx_dbl_hinge_commandex_hinged_co_cylinder', 'bx_dbl_hinge_commandex_hinged_co_lock_height_location', 'bx_dbl_hinge_commandex_hinged_co_lh_top_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lt_top_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lt_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_handle', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_handle', 'bx_dbl_hinge_lockwood_8654_co_cylinder', 'bx_dbl_hinge_lockwood_8654_co_lock_height_location', 'bx_dbl_hinge_lockwood_8654_co_lh_top_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lt_top_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lt_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_handle', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_handle', 'bx_dbl_hinge_whitco_mk2_co_cylinder', 'bx_dbl_hinge_whitco_mk2_co_lock_height_location', 'bx_dbl_hinge_whitco_mk2_co_lh_top_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lt_top_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lt_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_handle', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_handle', 'bx_dbl_hinge_lock_cut_out_side_dd', 'bx_dbl_hinge_non_lock_door_striker_cutouts', 'bx_dbl_hinge_non_lock_door_bolts', 'bx_dbl_hinge_patio_bolt_colour_non_lock', 'bx_dbl_hinge_midrail_case1', 'bx_dbl_hinge_midrail_height_mm_case1', 'bx_dbl_hinge_midrail_colour_std_case1', 'bx_dbl_hinge_midrail_colour_special_case1', 'bx_dbl_hinge_midrail_case2', 'bx_dbl_hinge_midrail_height_mm_case2', 'bx_dbl_hinge_midrail_colour_std_case2', 'bx_dbl_hinge_midrail_colour_special_case2', 'bx_dbl_hinge_num_sec_hinges_pickup', 'bx_dbl_hinge_num_sec_hinges_deliver', 'bx_dbl_hinge_spec_hinge_loc_2_pickup_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_pickup', 'bx_dbl_hinge_spec_hinge_loc_3_pickup_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_pickup', 'bx_dbl_hinge_spec_hinge_loc_4_pickup_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_pickup', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_pickup', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_pickup', 'bx_dbl_hinge_spec_hinge_loc_2_deliver_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_deliver', 'bx_dbl_hinge_spec_hinge_loc_3_deliver_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_deliver', 'bx_dbl_hinge_spec_hinge_loc_4_deliver_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_deliver', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_deliver', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_deliver', 'bx_dbl_hinge_sec_hinge_type', 'bx_dbl_hinge_sec_hinge_colour_std', 'bx_dbl_hinge_sec_hinge_colour_prong', 'bx_dbl_hinge_sec_hinge_colour_step', 'bx_dbl_hinge_sec_hinge_packers_qty', 'bx_dbl_hinge_closer', 'bx_dbl_hinge_closer_colour_austral', 'bx_dbl_hinge_closer_colour_whitco', 'bx_dbl_hinge_pet_door', 'bx_dbl_hinge_pet_door_colour', 'bx_dbl_hinge_pet_door_location', 'bx_dbl_hinge_pet_door_flexible_flap', 'bx_dbl_hinge_pet_door_surround_infill', 'bx_dbl_hinge_bug_seal_size', 'bx_dbl_hinge_left_bug_seal_length_mm', 'bx_dbl_hinge_right_bug_seal_length_mm', 'bx_dbl_hinge_stop_bead_colour', 'bx_dbl_hinge_stop_bead_left_length_mm', 'bx_dbl_hinge_stop_bead_top_length_mm', 'bx_dbl_hinge_stop_bead_right_length_mm', 'bx_dbl_hinge_adhesive_mohair', 'bx_dbl_hinge_adhesive_mohair_length_mm', 'bx_dbl_hinge_jamb_adaptor_type_case1', 'bx_dbl_hinge_jamb_adaptor_type_case2', 'bx_dbl_hinge_jamb_adaptor_type_case3', 'bx_dbl_hinge_jamb_opening_left_height_mm', 'bx_dbl_hinge_jamb_height_addition_mm', 'bx_dbl_hinge_jamb_adaptor_left_length_mm', 'bx_dbl_hinge_jamb_opening_top_width_mm', 'bx_dbl_hinge_jamb_width_addition_mm', 'bx_dbl_hinge_jamb_adaptor_top_length_mm', 'bx_dbl_hinge_jamb_opening_right_height_mm', 'bx_dbl_hinge_jamb_adaptor_right_length_mm', '_CALCULATED_deduction_assistance', '_CALCULATED_door_split_type', '_CALCULATED_lock_height', '_CALCULATED_manual_left_height', '_CALCULATED_manual_right_height', '_CALCULATED_height_calculation_method', '_CALCULATED_is_even_split', '_CALCULATED_is_manual_mode', '_CALCULATED_is_uneven_split', '_CALCULATED_largest_door_height', '_CALCULATED_smallest_door_height', '_CALCULATED_halfway_point', '_CALCULATED_halfway_minus_79', '_CALCULATED_halfway_plus_16', '_CALCULATED_halfway_plus_32', '_CALCULATED_halfway_plus_79', '_CALCULATED_height_minus_1000', '_CALCULATED_height_minus_1003', '_CALCULATED_height_minus_1019', '_CALCULATED_height_minus_1090', '_CALCULATED_height_minus_1098', '_CALCULATED_height_minus_1137', '_CALCULATED_height_minus_1153', '_CALCULATED_height_minus_1169', '_CALCULATED_height_minus_1248', '_CALCULATED_height_minus_270', '_CALCULATED_height_minus_317', '_CALCULATED_height_minus_330', '_CALCULATED_height_minus_333', '_CALCULATED_height_minus_349', '_CALCULATED_height_minus_428', '_CALCULATED_height_minus_740', '_CALCULATED_height_minus_787', '_CALCULATED_height_minus_800', '_CALCULATED_height_minus_803', '_CALCULATED_height_minus_819', '_CALCULATED_height_minus_898', '_CALCULATED_height_minus_940', '_CALCULATED_height_minus_987', '_CALCULATED_Largest_Sum_Door_Width', '_CALCULATED_door_width', '_CALCULATED_even_bottom_width', '_CALCULATED_even_middle_width', '_CALCULATED_even_top_width', '_CALCULATED_jamb_adaptor_left_length_mm', '_CALCULATED_jamb_adaptor_top_length_mm', '_CALCULATED_largest_door_width', '_CALCULATED_largest_left_door_width', '_CALCULATED_largest_right_door_width', '_CALCULATED_left_Clamp_Product', '_CALCULATED_left_door_middle_width', '_CALCULATED_left_height_door', '_CALCULATED_manual_formula', '_CALCULATED_right_Clamp_Product', '_CALCULATED_right_door_middle_width', '_CALCULATED_right_height_door', '_CALCULATED_smallest_door_width', '_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_mesh_height', '_CALCULATED_midrail_case1', '_CALCULATED_mesh_series', '_CALCULATED_midrail_case2', '_CALCULATED_midrail_height', '_CALCULATED_mesh_area', '_CALCULATED_mesh_operation_required', '_CALCULATED_mesh_operation_type', '_CALCULATED_mesh_area_m2', '_CALCULATED_mesh_perimeter', '_CALCULATED_mesh_aspect_ratio', '_CALCULATED_mesh_size_category'] 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Found quantity multiplier: 1.0 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Using field/option mapping calculation (no config_id) 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] WARNING: This may result in different operation costs than save config 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Returning 50 operations with total cost 221.871536 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COST_METRICS] {'config_id': None, 'operation_count': 50, 'total_cost': 221.87, 'calculation_method': 'field_option_mapping', 'timestamp': 'unknown'} 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COST_METRICS] WARNING: Using field/option mapping - may differ from save config 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Calculating operation costs for template 24 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Received field_values: {'bx_dbl_hinge_location': '', 'bx_dbl_hinge_location_other': '', 'bx_dbl_hinge_jamb_reveal_gt_20mm': '', 'bx_dbl_hinge_screen_clears_handle': '', 'bx_dbl_hinge_new_handle_clears_existing': '', 'bx_dbl_hinge_swing_path_clear': '', 'bx_dbl_hinge_obstruction_description': '', 'bx_dbl_hinge_quantity': '1', 'bx_dbl_hinge_frame_colour': 'black_custom_matt_gn248a', 'bx_dbl_hinge_powder_coat_colour': '', 'bx_dbl_hinge_custom_powder_coat_name': '', 'bx_dbl_hinge_custom_powder_coat_finish': '', 'bx_dbl_hinge_custom_powder_coat_code': '', 'bx_dbl_hinge_deduction_assistance': 'no', 'bx_dbl_hinge_door_split_type': 'even', 'bx_dbl_hinge_opening_height_mm_even': '', 'bx_dbl_hinge_height_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_height_mm_even': '', 'bx_dbl_hinge_opening_top_width_mm_even': '', 'bx_dbl_hinge_top_width_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_top_width_mm_even': '', 'bx_dbl_hinge_opening_middle_width_mm_even': '', 'bx_dbl_hinge_middle_width_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_middle_width_mm_even': '', 'bx_dbl_hinge_opening_bottom_width_mm_even': '', 'bx_dbl_hinge_bottom_width_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_bottom_width_mm_even': '', 'bx_dbl_hinge_left_opening_height_mm_uneven': '', 'bx_dbl_hinge_height_deduction_mm_uneven_left': '', 'bx_dbl_hinge_make_left_door_height_mm_uneven': '', 'bx_dbl_hinge_left_jamb_centre_top_mm_uneven': '', 'bx_dbl_hinge_top_left_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_left_door_top_width_mm_uneven': '', 'bx_dbl_hinge_left_jamb_centre_middle_mm_uneven': '', 'bx_dbl_hinge_middle_left_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_left_door_middle_width_mm_uneven': '', 'bx_dbl_hinge_left_jamb_centre_bottom_mm_uneven': '', 'bx_dbl_hinge_bottom_left_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_left_door_bottom_width_mm_uneven': '', 'bx_dbl_hinge_right_opening_height_mm_uneven': '', 'bx_dbl_hinge_height_deduction_mm_uneven_right': '', 'bx_dbl_hinge_make_right_door_height_mm_uneven': '', 'bx_dbl_hinge_right_jamb_centre_top_mm_uneven': '', 'bx_dbl_hinge_top_right_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_right_door_top_width_mm_uneven': '', 'bx_dbl_hinge_right_jamb_centre_middle_mm_uneven': '', 'bx_dbl_hinge_middle_right_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_right_door_middle_width_mm_uneven': '', 'bx_dbl_hinge_right_jamb_centre_bottom_mm_uneven': '', 'bx_dbl_hinge_bottom_right_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_right_door_bottom_width_mm_uneven': '', 'bx_dbl_hinge_make_left_door_height_mm_manual': '2110', 'bx_dbl_hinge_make_left_door_top_width_mm_manual': '860', 'bx_dbl_hinge_make_left_door_middle_width_mm_manual': '860', 'bx_dbl_hinge_make_left_door_bottom_width_mm_manual': '', 'bx_dbl_hinge_make_right_door_height_mm_manual': '2110', 'bx_dbl_hinge_make_right_door_top_width_mm_manual': '860', 'bx_dbl_hinge_make_right_door_middle_width_mm_manual': '', 'bx_dbl_hinge_make_right_door_bottom_width_mm_manual': '', 'bx_dbl_hinge_swing': 'outswing', 'bx_dbl_hinge_t_section_mullion_inswing': 'yes', 'bx_dbl_hinge_french_door_mullion_outswing': 'yes', 'bx_dbl_hinge_lock_brand': 'commandex_hinged', 'bx_dbl_hinge_austral_elegance_xc_lock_colour': 'black', 'bx_dbl_hinge_austral_elegance_xc_cylinder': 'austral_5_pin_dbl', 'bx_dbl_hinge_austral_elegance_xc_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_lt_top_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_lh_bottom_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_handle': '', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_handle': 'single', 'bx_dbl_hinge_commandex_hinged_lock_colour': 'black', 'bx_dbl_hinge_commandex_hinged_cylinder': 'commandex_5_pin', 'bx_dbl_hinge_commandex_hinged_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_top_cut_out': '', 'bx_dbl_hinge_commandex_hinged_lt_top_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_lh_centre_cut_out': '', 'bx_dbl_hinge_commandex_hinged_lt_centre_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_lh_bottom_cut_out': '', 'bx_dbl_hinge_commandex_hinged_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_lh_centre_handle': '', 'bx_dbl_hinge_commandex_hinged_lt_centre_handle': 'single', 'bx_dbl_hinge_lockwood_8654_lock_colour': 'black', 'bx_dbl_hinge_lockwood_8654_cylinder': 'whitco_5_pin', 'bx_dbl_hinge_lockwood_8654_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_top_cut_out': '', 'bx_dbl_hinge_lockwood_8654_lt_top_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_lh_centre_cut_out': '', 'bx_dbl_hinge_lockwood_8654_lt_centre_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_lh_bottom_cut_out': '', 'bx_dbl_hinge_lockwood_8654_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_lh_centre_handle': '', 'bx_dbl_hinge_lockwood_8654_lt_centre_handle': 'single', 'bx_dbl_hinge_whitco_mk2_lock_colour': 'black', 'bx_dbl_hinge_whitco_mk2_cylinder': 'whitco_5_pin', 'bx_dbl_hinge_whitco_mk2_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_top_cut_out': '', 'bx_dbl_hinge_whitco_mk2_lt_top_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_lh_centre_cut_out': '', 'bx_dbl_hinge_whitco_mk2_lt_centre_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_lh_bottom_cut_out': '', 'bx_dbl_hinge_whitco_mk2_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_lh_centre_handle': '', 'bx_dbl_hinge_whitco_mk2_lt_centre_handle': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lock_colour': 'black', 'bx_dbl_hinge_whitco_tasman_escape_cylinder': 'whitco_escape_cylinder_turn_knob', 'bx_dbl_hinge_whitco_tasman_escape_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_top_cut_out': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_top_cut_out': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_cut_out': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_cut_out': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lh_bottom_cut_out': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_handle': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_handle': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_austral_elegance_xc_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_top_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_handle': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_handle': 'single', 'bx_dbl_hinge_commandex_hinged_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_commandex_hinged_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_top_cut_out': '', 'bx_dbl_hinge_commandex_hinged_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_commandex_hinged_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_cut_out': '', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_handle': '', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_handle': 'single', 'bx_dbl_hinge_lockwood_8654_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_lockwood_8654_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_top_cut_out': '', 'bx_dbl_hinge_lockwood_8654_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_cut_out': '', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_lockwood_8654_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_handle': '', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_handle': 'single', 'bx_dbl_hinge_whitco_mk2_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_whitco_mk2_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_top_cut_out': '', 'bx_dbl_hinge_whitco_mk2_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_cut_out': '', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_whitco_mk2_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_handle': '', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_handle': 'single', 'bx_dbl_hinge_lock_cut_out_side_dd': '', 'bx_dbl_hinge_non_lock_door_striker_cutouts': 'yes', 'bx_dbl_hinge_non_lock_door_bolts': 'top_bottom_flush_bolts', 'bx_dbl_hinge_patio_bolt_colour_non_lock': 'black', 'bx_dbl_hinge_midrail_case1': 'yes', 'bx_dbl_hinge_midrail_height_mm_case1': '', 'bx_dbl_hinge_midrail_colour_std_case1': '', 'bx_dbl_hinge_midrail_colour_special_case1': 'black', 'bx_dbl_hinge_midrail_case2': 'no', 'bx_dbl_hinge_midrail_height_mm_case2': '', 'bx_dbl_hinge_midrail_colour_std_case2': 'black', 'bx_dbl_hinge_midrail_colour_special_case2': 'black', 'bx_dbl_hinge_num_sec_hinges_pickup': '3_per_door_attached', 'bx_dbl_hinge_num_sec_hinges_deliver': '3_per_door_loose_drilled', 'bx_dbl_hinge_spec_hinge_loc_2_pickup_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_pickup': '', 'bx_dbl_hinge_spec_hinge_loc_3_pickup_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_pickup': '', 'bx_dbl_hinge_spec_hinge_loc_4_pickup_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_pickup': '', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_pickup': '', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_pickup': '', 'bx_dbl_hinge_spec_hinge_loc_2_deliver_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_deliver': '', 'bx_dbl_hinge_spec_hinge_loc_3_deliver_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_deliver': '', 'bx_dbl_hinge_spec_hinge_loc_4_deliver_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_deliver': '', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_deliver': '', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_deliver': '', 'bx_dbl_hinge_sec_hinge_type': 'security_hinge', 'bx_dbl_hinge_sec_hinge_colour_std': 'black', 'bx_dbl_hinge_sec_hinge_colour_prong': 'black', 'bx_dbl_hinge_sec_hinge_colour_step': 'black', 'bx_dbl_hinge_sec_hinge_packers_qty': '0', 'bx_dbl_hinge_closer': 'no', 'bx_dbl_hinge_closer_colour_austral': 'black', 'bx_dbl_hinge_closer_colour_whitco': 'black', 'bx_dbl_hinge_pet_door': 'no', 'bx_dbl_hinge_pet_door_colour': 'black', 'bx_dbl_hinge_pet_door_location': 'lock_door_lock_side', 'bx_dbl_hinge_pet_door_flexible_flap': 'no', 'bx_dbl_hinge_pet_door_surround_infill': 'no', 'bx_dbl_hinge_bug_seal_size': 'no', 'bx_dbl_hinge_left_bug_seal_length_mm': '', 'bx_dbl_hinge_right_bug_seal_length_mm': '', 'bx_dbl_hinge_stop_bead_colour': 'no', 'bx_dbl_hinge_stop_bead_left_length_mm': '', 'bx_dbl_hinge_stop_bead_top_length_mm': '', 'bx_dbl_hinge_stop_bead_right_length_mm': '', 'bx_dbl_hinge_adhesive_mohair': 'no', 'bx_dbl_hinge_adhesive_mohair_length_mm': '', 'bx_dbl_hinge_jamb_adaptor_type_case1': 'no', 'bx_dbl_hinge_jamb_adaptor_type_case2': '', 'bx_dbl_hinge_jamb_adaptor_type_case3': '', 'bx_dbl_hinge_jamb_opening_left_height_mm': '', 'bx_dbl_hinge_jamb_height_addition_mm': '20', 'bx_dbl_hinge_jamb_adaptor_left_length_mm': '', 'bx_dbl_hinge_jamb_opening_top_width_mm': '', 'bx_dbl_hinge_jamb_width_addition_mm': '40', 'bx_dbl_hinge_jamb_adaptor_top_length_mm': '', 'bx_dbl_hinge_jamb_opening_right_height_mm': '', 'bx_dbl_hinge_jamb_adaptor_right_length_mm': '', '_CALCULATED_deduction_assistance': 'no', '_CALCULATED_door_split_type': 'even', '_CALCULATED_lock_height': 0, '_CALCULATED_manual_left_height': 0, '_CALCULATED_manual_right_height': 0, '_CALCULATED_height_calculation_method': 'manual', '_CALCULATED_is_even_split': True, '_CALCULATED_is_manual_mode': True, '_CALCULATED_is_uneven_split': False, '_CALCULATED_largest_door_height': 0, '_CALCULATED_smallest_door_height': 20, '_CALCULATED_halfway_point': 0, '_CALCULATED_halfway_minus_79': -79, '_CALCULATED_halfway_plus_16': 16, '_CALCULATED_halfway_plus_32': 32, '_CALCULATED_halfway_plus_79': 79, '_CALCULATED_height_minus_1000': -1000, '_CALCULATED_height_minus_1003': -1003, '_CALCULATED_height_minus_1019': -1019, '_CALCULATED_height_minus_1090': -1090, '_CALCULATED_height_minus_1098': -1098, '_CALCULATED_height_minus_1137': -1137, '_CALCULATED_height_minus_1153': -1153, '_CALCULATED_height_minus_1169': -1169, '_CALCULATED_height_minus_1248': -1248, '_CALCULATED_height_minus_270': -270, '_CALCULATED_height_minus_317': -317, '_CALCULATED_height_minus_330': -330, '_CALCULATED_height_minus_333': -333, '_CALCULATED_height_minus_349': -349, '_CALCULATED_height_minus_428': -428, '_CALCULATED_height_minus_740': -740, '_CALCULATED_height_minus_787': -787, '_CALCULATED_height_minus_800': -800, '_CALCULATED_height_minus_803': -803, '_CALCULATED_height_minus_819': -819, '_CALCULATED_height_minus_898': -898, '_CALCULATED_height_minus_940': -940, '_CALCULATED_height_minus_987': -987, '_CALCULATED_Largest_Sum_Door_Width': 0, '_CALCULATED_door_width': 0, '_CALCULATED_even_bottom_width': 0, '_CALCULATED_even_middle_width': 0, '_CALCULATED_even_top_width': 0, '_CALCULATED_jamb_adaptor_left_length_mm': 20, '_CALCULATED_jamb_adaptor_top_length_mm': 40, '_CALCULATED_largest_door_width': 0, '_CALCULATED_largest_left_door_width': 0, '_CALCULATED_largest_right_door_width': 0, '_CALCULATED_left_Clamp_Product': 'no', '_CALCULATED_left_door_middle_width': 0, '_CALCULATED_left_height_door': 0, '_CALCULATED_manual_formula': 'min(0, 0) = 0', '_CALCULATED_right_Clamp_Product': 'no', '_CALCULATED_right_door_middle_width': 0, '_CALCULATED_right_height_door': 0, '_CALCULATED_smallest_door_width': 0, '_CALCULATED_mesh_required': False, '_CALCULATED_mesh_width': 0, '_CALCULATED_mesh_height': 0, '_CALCULATED_midrail_case1': False, '_CALCULATED_mesh_series': '', '_CALCULATED_midrail_case2': True, '_CALCULATED_midrail_height': 0, '_CALCULATED_mesh_area': 0, '_CALCULATED_mesh_operation_required': False, '_CALCULATED_mesh_operation_type': None, '_CALCULATED_mesh_area_m2': 0, '_CALCULATED_mesh_perimeter': 0, '_CALCULATED_mesh_aspect_ratio': 0, '_CALCULATED_mesh_size_category': None} 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Field values keys: ['bx_dbl_hinge_location', 'bx_dbl_hinge_location_other', 'bx_dbl_hinge_jamb_reveal_gt_20mm', 'bx_dbl_hinge_screen_clears_handle', 'bx_dbl_hinge_new_handle_clears_existing', 'bx_dbl_hinge_swing_path_clear', 'bx_dbl_hinge_obstruction_description', 'bx_dbl_hinge_quantity', 'bx_dbl_hinge_frame_colour', 'bx_dbl_hinge_powder_coat_colour', 'bx_dbl_hinge_custom_powder_coat_name', 'bx_dbl_hinge_custom_powder_coat_finish', 'bx_dbl_hinge_custom_powder_coat_code', 'bx_dbl_hinge_deduction_assistance', 'bx_dbl_hinge_door_split_type', 'bx_dbl_hinge_opening_height_mm_even', 'bx_dbl_hinge_height_deduction_mm_even', 'bx_dbl_hinge_make_each_door_height_mm_even', 'bx_dbl_hinge_opening_top_width_mm_even', 'bx_dbl_hinge_top_width_deduction_mm_even', 'bx_dbl_hinge_make_each_door_top_width_mm_even', 'bx_dbl_hinge_opening_middle_width_mm_even', 'bx_dbl_hinge_middle_width_deduction_mm_even', 'bx_dbl_hinge_make_each_door_middle_width_mm_even', 'bx_dbl_hinge_opening_bottom_width_mm_even', 'bx_dbl_hinge_bottom_width_deduction_mm_even', 'bx_dbl_hinge_make_each_door_bottom_width_mm_even', 'bx_dbl_hinge_left_opening_height_mm_uneven', 'bx_dbl_hinge_height_deduction_mm_uneven_left', 'bx_dbl_hinge_make_left_door_height_mm_uneven', 'bx_dbl_hinge_left_jamb_centre_top_mm_uneven', 'bx_dbl_hinge_top_left_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_left_door_top_width_mm_uneven', 'bx_dbl_hinge_left_jamb_centre_middle_mm_uneven', 'bx_dbl_hinge_middle_left_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_left_door_middle_width_mm_uneven', 'bx_dbl_hinge_left_jamb_centre_bottom_mm_uneven', 'bx_dbl_hinge_bottom_left_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_left_door_bottom_width_mm_uneven', 'bx_dbl_hinge_right_opening_height_mm_uneven', 'bx_dbl_hinge_height_deduction_mm_uneven_right', 'bx_dbl_hinge_make_right_door_height_mm_uneven', 'bx_dbl_hinge_right_jamb_centre_top_mm_uneven', 'bx_dbl_hinge_top_right_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_right_door_top_width_mm_uneven', 'bx_dbl_hinge_right_jamb_centre_middle_mm_uneven', 'bx_dbl_hinge_middle_right_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_right_door_middle_width_mm_uneven', 'bx_dbl_hinge_right_jamb_centre_bottom_mm_uneven', 'bx_dbl_hinge_bottom_right_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_right_door_bottom_width_mm_uneven', 'bx_dbl_hinge_make_left_door_height_mm_manual', 'bx_dbl_hinge_make_left_door_top_width_mm_manual', 'bx_dbl_hinge_make_left_door_middle_width_mm_manual', 'bx_dbl_hinge_make_left_door_bottom_width_mm_manual', 'bx_dbl_hinge_make_right_door_height_mm_manual', 'bx_dbl_hinge_make_right_door_top_width_mm_manual', 'bx_dbl_hinge_make_right_door_middle_width_mm_manual', 'bx_dbl_hinge_make_right_door_bottom_width_mm_manual', 'bx_dbl_hinge_swing', 'bx_dbl_hinge_t_section_mullion_inswing', 'bx_dbl_hinge_french_door_mullion_outswing', 'bx_dbl_hinge_lock_brand', 'bx_dbl_hinge_austral_elegance_xc_lock_colour', 'bx_dbl_hinge_austral_elegance_xc_cylinder', 'bx_dbl_hinge_austral_elegance_xc_lock_height_location', 'bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lt_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lt_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_handle', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_handle', 'bx_dbl_hinge_commandex_hinged_lock_colour', 'bx_dbl_hinge_commandex_hinged_cylinder', 'bx_dbl_hinge_commandex_hinged_lock_height_location', 'bx_dbl_hinge_commandex_hinged_lh_top_cut_out', 'bx_dbl_hinge_commandex_hinged_lt_top_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_lt_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_lt_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_centre_handle', 'bx_dbl_hinge_commandex_hinged_lt_centre_handle', 'bx_dbl_hinge_lockwood_8654_lock_colour', 'bx_dbl_hinge_lockwood_8654_cylinder', 'bx_dbl_hinge_lockwood_8654_lock_height_location', 'bx_dbl_hinge_lockwood_8654_lh_top_cut_out', 'bx_dbl_hinge_lockwood_8654_lt_top_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_lt_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_lt_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_centre_handle', 'bx_dbl_hinge_lockwood_8654_lt_centre_handle', 'bx_dbl_hinge_whitco_mk2_lock_colour', 'bx_dbl_hinge_whitco_mk2_cylinder', 'bx_dbl_hinge_whitco_mk2_lock_height_location', 'bx_dbl_hinge_whitco_mk2_lh_top_cut_out', 'bx_dbl_hinge_whitco_mk2_lt_top_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_lt_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_lt_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_centre_handle', 'bx_dbl_hinge_whitco_mk2_lt_centre_handle', 'bx_dbl_hinge_whitco_tasman_escape_lock_colour', 'bx_dbl_hinge_whitco_tasman_escape_cylinder', 'bx_dbl_hinge_whitco_tasman_escape_lock_height_location', 'bx_dbl_hinge_whitco_tasman_escape_lh_top_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lt_top_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_bottom_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lt_bottom_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_handle', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_handle', 'bx_dbl_hinge_austral_elegance_xc_co_cylinder', 'bx_dbl_hinge_austral_elegance_xc_co_lock_height_location', 'bx_dbl_hinge_austral_elegance_xc_co_lh_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lt_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lt_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_handle', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_handle', 'bx_dbl_hinge_commandex_hinged_co_cylinder', 'bx_dbl_hinge_commandex_hinged_co_lock_height_location', 'bx_dbl_hinge_commandex_hinged_co_lh_top_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lt_top_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lt_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_handle', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_handle', 'bx_dbl_hinge_lockwood_8654_co_cylinder', 'bx_dbl_hinge_lockwood_8654_co_lock_height_location', 'bx_dbl_hinge_lockwood_8654_co_lh_top_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lt_top_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lt_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_handle', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_handle', 'bx_dbl_hinge_whitco_mk2_co_cylinder', 'bx_dbl_hinge_whitco_mk2_co_lock_height_location', 'bx_dbl_hinge_whitco_mk2_co_lh_top_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lt_top_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lt_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_handle', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_handle', 'bx_dbl_hinge_lock_cut_out_side_dd', 'bx_dbl_hinge_non_lock_door_striker_cutouts', 'bx_dbl_hinge_non_lock_door_bolts', 'bx_dbl_hinge_patio_bolt_colour_non_lock', 'bx_dbl_hinge_midrail_case1', 'bx_dbl_hinge_midrail_height_mm_case1', 'bx_dbl_hinge_midrail_colour_std_case1', 'bx_dbl_hinge_midrail_colour_special_case1', 'bx_dbl_hinge_midrail_case2', 'bx_dbl_hinge_midrail_height_mm_case2', 'bx_dbl_hinge_midrail_colour_std_case2', 'bx_dbl_hinge_midrail_colour_special_case2', 'bx_dbl_hinge_num_sec_hinges_pickup', 'bx_dbl_hinge_num_sec_hinges_deliver', 'bx_dbl_hinge_spec_hinge_loc_2_pickup_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_pickup', 'bx_dbl_hinge_spec_hinge_loc_3_pickup_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_pickup', 'bx_dbl_hinge_spec_hinge_loc_4_pickup_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_pickup', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_pickup', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_pickup', 'bx_dbl_hinge_spec_hinge_loc_2_deliver_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_deliver', 'bx_dbl_hinge_spec_hinge_loc_3_deliver_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_deliver', 'bx_dbl_hinge_spec_hinge_loc_4_deliver_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_deliver', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_deliver', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_deliver', 'bx_dbl_hinge_sec_hinge_type', 'bx_dbl_hinge_sec_hinge_colour_std', 'bx_dbl_hinge_sec_hinge_colour_prong', 'bx_dbl_hinge_sec_hinge_colour_step', 'bx_dbl_hinge_sec_hinge_packers_qty', 'bx_dbl_hinge_closer', 'bx_dbl_hinge_closer_colour_austral', 'bx_dbl_hinge_closer_colour_whitco', 'bx_dbl_hinge_pet_door', 'bx_dbl_hinge_pet_door_colour', 'bx_dbl_hinge_pet_door_location', 'bx_dbl_hinge_pet_door_flexible_flap', 'bx_dbl_hinge_pet_door_surround_infill', 'bx_dbl_hinge_bug_seal_size', 'bx_dbl_hinge_left_bug_seal_length_mm', 'bx_dbl_hinge_right_bug_seal_length_mm', 'bx_dbl_hinge_stop_bead_colour', 'bx_dbl_hinge_stop_bead_left_length_mm', 'bx_dbl_hinge_stop_bead_top_length_mm', 'bx_dbl_hinge_stop_bead_right_length_mm', 'bx_dbl_hinge_adhesive_mohair', 'bx_dbl_hinge_adhesive_mohair_length_mm', 'bx_dbl_hinge_jamb_adaptor_type_case1', 'bx_dbl_hinge_jamb_adaptor_type_case2', 'bx_dbl_hinge_jamb_adaptor_type_case3', 'bx_dbl_hinge_jamb_opening_left_height_mm', 'bx_dbl_hinge_jamb_height_addition_mm', 'bx_dbl_hinge_jamb_adaptor_left_length_mm', 'bx_dbl_hinge_jamb_opening_top_width_mm', 'bx_dbl_hinge_jamb_width_addition_mm', 'bx_dbl_hinge_jamb_adaptor_top_length_mm', 'bx_dbl_hinge_jamb_opening_right_height_mm', 'bx_dbl_hinge_jamb_adaptor_right_length_mm', '_CALCULATED_deduction_assistance', '_CALCULATED_door_split_type', '_CALCULATED_lock_height', '_CALCULATED_manual_left_height', '_CALCULATED_manual_right_height', '_CALCULATED_height_calculation_method', '_CALCULATED_is_even_split', '_CALCULATED_is_manual_mode', '_CALCULATED_is_uneven_split', '_CALCULATED_largest_door_height', '_CALCULATED_smallest_door_height', '_CALCULATED_halfway_point', '_CALCULATED_halfway_minus_79', '_CALCULATED_halfway_plus_16', '_CALCULATED_halfway_plus_32', '_CALCULATED_halfway_plus_79', '_CALCULATED_height_minus_1000', '_CALCULATED_height_minus_1003', '_CALCULATED_height_minus_1019', '_CALCULATED_height_minus_1090', '_CALCULATED_height_minus_1098', '_CALCULATED_height_minus_1137', '_CALCULATED_height_minus_1153', '_CALCULATED_height_minus_1169', '_CALCULATED_height_minus_1248', '_CALCULATED_height_minus_270', '_CALCULATED_height_minus_317', '_CALCULATED_height_minus_330', '_CALCULATED_height_minus_333', '_CALCULATED_height_minus_349', '_CALCULATED_height_minus_428', '_CALCULATED_height_minus_740', '_CALCULATED_height_minus_787', '_CALCULATED_height_minus_800', '_CALCULATED_height_minus_803', '_CALCULATED_height_minus_819', '_CALCULATED_height_minus_898', '_CALCULATED_height_minus_940', '_CALCULATED_height_minus_987', '_CALCULATED_Largest_Sum_Door_Width', '_CALCULATED_door_width', '_CALCULATED_even_bottom_width', '_CALCULATED_even_middle_width', '_CALCULATED_even_top_width', '_CALCULATED_jamb_adaptor_left_length_mm', '_CALCULATED_jamb_adaptor_top_length_mm', '_CALCULATED_largest_door_width', '_CALCULATED_largest_left_door_width', '_CALCULATED_largest_right_door_width', '_CALCULATED_left_Clamp_Product', '_CALCULATED_left_door_middle_width', '_CALCULATED_left_height_door', '_CALCULATED_manual_formula', '_CALCULATED_right_Clamp_Product', '_CALCULATED_right_door_middle_width', '_CALCULATED_right_height_door', '_CALCULATED_smallest_door_width', '_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_mesh_height', '_CALCULATED_midrail_case1', '_CALCULATED_mesh_series', '_CALCULATED_midrail_case2', '_CALCULATED_midrail_height', '_CALCULATED_mesh_area', '_CALCULATED_mesh_operation_required', '_CALCULATED_mesh_operation_type', '_CALCULATED_mesh_area_m2', '_CALCULATED_mesh_perimeter', '_CALCULATED_mesh_aspect_ratio', '_CALCULATED_mesh_size_category'] 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Found quantity multiplier: 1.0 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Using field/option mapping calculation (no config_id) 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] WARNING: This may result in different operation costs than save config 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Returning 50 operations with total cost 221.871536 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COST_METRICS] {'config_id': None, 'operation_count': 50, 'total_cost': 221.87, 'calculation_method': 'field_option_mapping', 'timestamp': 'unknown'} 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COST_METRICS] WARNING: Using field/option mapping - may differ from save config 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Calculating operation costs for template 24 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Received field_values: {'bx_dbl_hinge_location': '', 'bx_dbl_hinge_location_other': '', 'bx_dbl_hinge_jamb_reveal_gt_20mm': '', 'bx_dbl_hinge_screen_clears_handle': '', 'bx_dbl_hinge_new_handle_clears_existing': '', 'bx_dbl_hinge_swing_path_clear': '', 'bx_dbl_hinge_obstruction_description': '', 'bx_dbl_hinge_quantity': '1', 'bx_dbl_hinge_frame_colour': 'black_custom_matt_gn248a', 'bx_dbl_hinge_powder_coat_colour': '', 'bx_dbl_hinge_custom_powder_coat_name': '', 'bx_dbl_hinge_custom_powder_coat_finish': '', 'bx_dbl_hinge_custom_powder_coat_code': '', 'bx_dbl_hinge_deduction_assistance': 'no', 'bx_dbl_hinge_door_split_type': 'even', 'bx_dbl_hinge_opening_height_mm_even': '', 'bx_dbl_hinge_height_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_height_mm_even': '', 'bx_dbl_hinge_opening_top_width_mm_even': '', 'bx_dbl_hinge_top_width_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_top_width_mm_even': '', 'bx_dbl_hinge_opening_middle_width_mm_even': '', 'bx_dbl_hinge_middle_width_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_middle_width_mm_even': '', 'bx_dbl_hinge_opening_bottom_width_mm_even': '', 'bx_dbl_hinge_bottom_width_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_bottom_width_mm_even': '', 'bx_dbl_hinge_left_opening_height_mm_uneven': '', 'bx_dbl_hinge_height_deduction_mm_uneven_left': '', 'bx_dbl_hinge_make_left_door_height_mm_uneven': '', 'bx_dbl_hinge_left_jamb_centre_top_mm_uneven': '', 'bx_dbl_hinge_top_left_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_left_door_top_width_mm_uneven': '', 'bx_dbl_hinge_left_jamb_centre_middle_mm_uneven': '', 'bx_dbl_hinge_middle_left_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_left_door_middle_width_mm_uneven': '', 'bx_dbl_hinge_left_jamb_centre_bottom_mm_uneven': '', 'bx_dbl_hinge_bottom_left_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_left_door_bottom_width_mm_uneven': '', 'bx_dbl_hinge_right_opening_height_mm_uneven': '', 'bx_dbl_hinge_height_deduction_mm_uneven_right': '', 'bx_dbl_hinge_make_right_door_height_mm_uneven': '', 'bx_dbl_hinge_right_jamb_centre_top_mm_uneven': '', 'bx_dbl_hinge_top_right_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_right_door_top_width_mm_uneven': '', 'bx_dbl_hinge_right_jamb_centre_middle_mm_uneven': '', 'bx_dbl_hinge_middle_right_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_right_door_middle_width_mm_uneven': '', 'bx_dbl_hinge_right_jamb_centre_bottom_mm_uneven': '', 'bx_dbl_hinge_bottom_right_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_right_door_bottom_width_mm_uneven': '', 'bx_dbl_hinge_make_left_door_height_mm_manual': '2110', 'bx_dbl_hinge_make_left_door_top_width_mm_manual': '860', 'bx_dbl_hinge_make_left_door_middle_width_mm_manual': '860', 'bx_dbl_hinge_make_left_door_bottom_width_mm_manual': '860', 'bx_dbl_hinge_make_right_door_height_mm_manual': '2110', 'bx_dbl_hinge_make_right_door_top_width_mm_manual': '860', 'bx_dbl_hinge_make_right_door_middle_width_mm_manual': '860', 'bx_dbl_hinge_make_right_door_bottom_width_mm_manual': '860', 'bx_dbl_hinge_swing': 'outswing', 'bx_dbl_hinge_t_section_mullion_inswing': 'yes', 'bx_dbl_hinge_french_door_mullion_outswing': 'yes', 'bx_dbl_hinge_lock_brand': 'commandex_hinged', 'bx_dbl_hinge_austral_elegance_xc_lock_colour': 'black', 'bx_dbl_hinge_austral_elegance_xc_cylinder': 'austral_5_pin_dbl', 'bx_dbl_hinge_austral_elegance_xc_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_lt_top_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_lh_bottom_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_handle': '', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_handle': 'single', 'bx_dbl_hinge_commandex_hinged_lock_colour': 'black', 'bx_dbl_hinge_commandex_hinged_cylinder': 'commandex_5_pin', 'bx_dbl_hinge_commandex_hinged_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_top_cut_out': '', 'bx_dbl_hinge_commandex_hinged_lt_top_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_lh_centre_cut_out': '', 'bx_dbl_hinge_commandex_hinged_lt_centre_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_lh_bottom_cut_out': '', 'bx_dbl_hinge_commandex_hinged_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_lh_centre_handle': '', 'bx_dbl_hinge_commandex_hinged_lt_centre_handle': 'single', 'bx_dbl_hinge_lockwood_8654_lock_colour': 'black', 'bx_dbl_hinge_lockwood_8654_cylinder': 'whitco_5_pin', 'bx_dbl_hinge_lockwood_8654_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_top_cut_out': '', 'bx_dbl_hinge_lockwood_8654_lt_top_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_lh_centre_cut_out': '', 'bx_dbl_hinge_lockwood_8654_lt_centre_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_lh_bottom_cut_out': '', 'bx_dbl_hinge_lockwood_8654_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_lh_centre_handle': '', 'bx_dbl_hinge_lockwood_8654_lt_centre_handle': 'single', 'bx_dbl_hinge_whitco_mk2_lock_colour': 'black', 'bx_dbl_hinge_whitco_mk2_cylinder': 'whitco_5_pin', 'bx_dbl_hinge_whitco_mk2_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_top_cut_out': '', 'bx_dbl_hinge_whitco_mk2_lt_top_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_lh_centre_cut_out': '', 'bx_dbl_hinge_whitco_mk2_lt_centre_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_lh_bottom_cut_out': '', 'bx_dbl_hinge_whitco_mk2_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_lh_centre_handle': '', 'bx_dbl_hinge_whitco_mk2_lt_centre_handle': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lock_colour': 'black', 'bx_dbl_hinge_whitco_tasman_escape_cylinder': 'whitco_escape_cylinder_turn_knob', 'bx_dbl_hinge_whitco_tasman_escape_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_top_cut_out': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_top_cut_out': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_cut_out': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_cut_out': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lh_bottom_cut_out': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_handle': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_handle': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_austral_elegance_xc_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_top_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_handle': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_handle': 'single', 'bx_dbl_hinge_commandex_hinged_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_commandex_hinged_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_top_cut_out': '', 'bx_dbl_hinge_commandex_hinged_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_commandex_hinged_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_cut_out': '', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_handle': '', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_handle': 'single', 'bx_dbl_hinge_lockwood_8654_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_lockwood_8654_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_top_cut_out': '', 'bx_dbl_hinge_lockwood_8654_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_cut_out': '', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_lockwood_8654_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_handle': '', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_handle': 'single', 'bx_dbl_hinge_whitco_mk2_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_whitco_mk2_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_top_cut_out': '', 'bx_dbl_hinge_whitco_mk2_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_cut_out': '', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_whitco_mk2_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_handle': '', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_handle': 'single', 'bx_dbl_hinge_lock_cut_out_side_dd': '', 'bx_dbl_hinge_non_lock_door_striker_cutouts': 'yes', 'bx_dbl_hinge_non_lock_door_bolts': 'top_bottom_flush_bolts', 'bx_dbl_hinge_patio_bolt_colour_non_lock': 'black', 'bx_dbl_hinge_midrail_case1': 'yes', 'bx_dbl_hinge_midrail_height_mm_case1': '', 'bx_dbl_hinge_midrail_colour_std_case1': '', 'bx_dbl_hinge_midrail_colour_special_case1': 'black', 'bx_dbl_hinge_midrail_case2': 'no', 'bx_dbl_hinge_midrail_height_mm_case2': '', 'bx_dbl_hinge_midrail_colour_std_case2': 'black', 'bx_dbl_hinge_midrail_colour_special_case2': 'black', 'bx_dbl_hinge_num_sec_hinges_pickup': '3_per_door_attached', 'bx_dbl_hinge_num_sec_hinges_deliver': '3_per_door_loose_drilled', 'bx_dbl_hinge_spec_hinge_loc_2_pickup_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_pickup': '', 'bx_dbl_hinge_spec_hinge_loc_3_pickup_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_pickup': '', 'bx_dbl_hinge_spec_hinge_loc_4_pickup_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_pickup': '', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_pickup': '', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_pickup': '', 'bx_dbl_hinge_spec_hinge_loc_2_deliver_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_deliver': '', 'bx_dbl_hinge_spec_hinge_loc_3_deliver_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_deliver': '', 'bx_dbl_hinge_spec_hinge_loc_4_deliver_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_deliver': '', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_deliver': '', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_deliver': '', 'bx_dbl_hinge_sec_hinge_type': 'security_hinge', 'bx_dbl_hinge_sec_hinge_colour_std': 'black', 'bx_dbl_hinge_sec_hinge_colour_prong': 'black', 'bx_dbl_hinge_sec_hinge_colour_step': 'black', 'bx_dbl_hinge_sec_hinge_packers_qty': '0', 'bx_dbl_hinge_closer': 'no', 'bx_dbl_hinge_closer_colour_austral': 'black', 'bx_dbl_hinge_closer_colour_whitco': 'black', 'bx_dbl_hinge_pet_door': 'no', 'bx_dbl_hinge_pet_door_colour': 'black', 'bx_dbl_hinge_pet_door_location': 'lock_door_lock_side', 'bx_dbl_hinge_pet_door_flexible_flap': 'no', 'bx_dbl_hinge_pet_door_surround_infill': 'no', 'bx_dbl_hinge_bug_seal_size': 'no', 'bx_dbl_hinge_left_bug_seal_length_mm': '', 'bx_dbl_hinge_right_bug_seal_length_mm': '', 'bx_dbl_hinge_stop_bead_colour': 'no', 'bx_dbl_hinge_stop_bead_left_length_mm': '', 'bx_dbl_hinge_stop_bead_top_length_mm': '', 'bx_dbl_hinge_stop_bead_right_length_mm': '', 'bx_dbl_hinge_adhesive_mohair': 'no', 'bx_dbl_hinge_adhesive_mohair_length_mm': '', 'bx_dbl_hinge_jamb_adaptor_type_case1': 'no', 'bx_dbl_hinge_jamb_adaptor_type_case2': '', 'bx_dbl_hinge_jamb_adaptor_type_case3': '', 'bx_dbl_hinge_jamb_opening_left_height_mm': '', 'bx_dbl_hinge_jamb_height_addition_mm': '20', 'bx_dbl_hinge_jamb_adaptor_left_length_mm': '', 'bx_dbl_hinge_jamb_opening_top_width_mm': '', 'bx_dbl_hinge_jamb_width_addition_mm': '40', 'bx_dbl_hinge_jamb_adaptor_top_length_mm': '', 'bx_dbl_hinge_jamb_opening_right_height_mm': '', 'bx_dbl_hinge_jamb_adaptor_right_length_mm': '', '_CALCULATED_deduction_assistance': 'no', '_CALCULATED_door_split_type': 'even', '_CALCULATED_lock_height': 0, '_CALCULATED_manual_left_height': 2110, '_CALCULATED_manual_right_height': 0, '_CALCULATED_height_calculation_method': 'manual', '_CALCULATED_is_even_split': True, '_CALCULATED_is_manual_mode': True, '_CALCULATED_is_uneven_split': False, '_CALCULATED_largest_door_height': 2110, '_CALCULATED_smallest_door_height': 20, '_CALCULATED_halfway_point': 0, '_CALCULATED_halfway_minus_79': -79, '_CALCULATED_halfway_plus_16': 16, '_CALCULATED_halfway_plus_32': 32, '_CALCULATED_halfway_plus_79': 79, '_CALCULATED_height_minus_1000': -1000, '_CALCULATED_height_minus_1003': -1003, '_CALCULATED_height_minus_1019': -1019, '_CALCULATED_height_minus_1090': -1090, '_CALCULATED_height_minus_1098': -1098, '_CALCULATED_height_minus_1137': -1137, '_CALCULATED_height_minus_1153': -1153, '_CALCULATED_height_minus_1169': -1169, '_CALCULATED_height_minus_1248': -1248, '_CALCULATED_height_minus_270': -270, '_CALCULATED_height_minus_317': -317, '_CALCULATED_height_minus_330': -330, '_CALCULATED_height_minus_333': -333, '_CALCULATED_height_minus_349': -349, '_CALCULATED_height_minus_428': -428, '_CALCULATED_height_minus_740': -740, '_CALCULATED_height_minus_787': -787, '_CALCULATED_height_minus_800': -800, '_CALCULATED_height_minus_803': -803, '_CALCULATED_height_minus_819': -819, '_CALCULATED_height_minus_898': -898, '_CALCULATED_height_minus_940': -940, '_CALCULATED_height_minus_987': -987, '_CALCULATED_Largest_Sum_Door_Width': 0, '_CALCULATED_door_width': 0, '_CALCULATED_even_bottom_width': 0, '_CALCULATED_even_middle_width': 0, '_CALCULATED_even_top_width': 0, '_CALCULATED_jamb_adaptor_left_length_mm': 20, '_CALCULATED_jamb_adaptor_top_length_mm': 40, '_CALCULATED_largest_door_width': 0, '_CALCULATED_largest_left_door_width': 0, '_CALCULATED_largest_right_door_width': 0, '_CALCULATED_left_Clamp_Product': 'no', '_CALCULATED_left_door_middle_width': 0, '_CALCULATED_left_height_door': 2110, '_CALCULATED_manual_formula': 'min(2110.0, 0) = 0', '_CALCULATED_right_Clamp_Product': 'no', '_CALCULATED_right_door_middle_width': 0, '_CALCULATED_right_height_door': 0, '_CALCULATED_smallest_door_width': 0, '_CALCULATED_mesh_required': False, '_CALCULATED_mesh_width': 0, '_CALCULATED_mesh_height': 0, '_CALCULATED_midrail_case1': False, '_CALCULATED_mesh_series': '', '_CALCULATED_midrail_case2': False, '_CALCULATED_midrail_height': 0, '_CALCULATED_mesh_area': 0, '_CALCULATED_mesh_operation_required': False, '_CALCULATED_mesh_operation_type': None, '_CALCULATED_mesh_area_m2': 0, '_CALCULATED_mesh_perimeter': 0, '_CALCULATED_mesh_aspect_ratio': 0, '_CALCULATED_mesh_size_category': None} 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Field values keys: ['bx_dbl_hinge_location', 'bx_dbl_hinge_location_other', 'bx_dbl_hinge_jamb_reveal_gt_20mm', 'bx_dbl_hinge_screen_clears_handle', 'bx_dbl_hinge_new_handle_clears_existing', 'bx_dbl_hinge_swing_path_clear', 'bx_dbl_hinge_obstruction_description', 'bx_dbl_hinge_quantity', 'bx_dbl_hinge_frame_colour', 'bx_dbl_hinge_powder_coat_colour', 'bx_dbl_hinge_custom_powder_coat_name', 'bx_dbl_hinge_custom_powder_coat_finish', 'bx_dbl_hinge_custom_powder_coat_code', 'bx_dbl_hinge_deduction_assistance', 'bx_dbl_hinge_door_split_type', 'bx_dbl_hinge_opening_height_mm_even', 'bx_dbl_hinge_height_deduction_mm_even', 'bx_dbl_hinge_make_each_door_height_mm_even', 'bx_dbl_hinge_opening_top_width_mm_even', 'bx_dbl_hinge_top_width_deduction_mm_even', 'bx_dbl_hinge_make_each_door_top_width_mm_even', 'bx_dbl_hinge_opening_middle_width_mm_even', 'bx_dbl_hinge_middle_width_deduction_mm_even', 'bx_dbl_hinge_make_each_door_middle_width_mm_even', 'bx_dbl_hinge_opening_bottom_width_mm_even', 'bx_dbl_hinge_bottom_width_deduction_mm_even', 'bx_dbl_hinge_make_each_door_bottom_width_mm_even', 'bx_dbl_hinge_left_opening_height_mm_uneven', 'bx_dbl_hinge_height_deduction_mm_uneven_left', 'bx_dbl_hinge_make_left_door_height_mm_uneven', 'bx_dbl_hinge_left_jamb_centre_top_mm_uneven', 'bx_dbl_hinge_top_left_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_left_door_top_width_mm_uneven', 'bx_dbl_hinge_left_jamb_centre_middle_mm_uneven', 'bx_dbl_hinge_middle_left_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_left_door_middle_width_mm_uneven', 'bx_dbl_hinge_left_jamb_centre_bottom_mm_uneven', 'bx_dbl_hinge_bottom_left_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_left_door_bottom_width_mm_uneven', 'bx_dbl_hinge_right_opening_height_mm_uneven', 'bx_dbl_hinge_height_deduction_mm_uneven_right', 'bx_dbl_hinge_make_right_door_height_mm_uneven', 'bx_dbl_hinge_right_jamb_centre_top_mm_uneven', 'bx_dbl_hinge_top_right_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_right_door_top_width_mm_uneven', 'bx_dbl_hinge_right_jamb_centre_middle_mm_uneven', 'bx_dbl_hinge_middle_right_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_right_door_middle_width_mm_uneven', 'bx_dbl_hinge_right_jamb_centre_bottom_mm_uneven', 'bx_dbl_hinge_bottom_right_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_right_door_bottom_width_mm_uneven', 'bx_dbl_hinge_make_left_door_height_mm_manual', 'bx_dbl_hinge_make_left_door_top_width_mm_manual', 'bx_dbl_hinge_make_left_door_middle_width_mm_manual', 'bx_dbl_hinge_make_left_door_bottom_width_mm_manual', 'bx_dbl_hinge_make_right_door_height_mm_manual', 'bx_dbl_hinge_make_right_door_top_width_mm_manual', 'bx_dbl_hinge_make_right_door_middle_width_mm_manual', 'bx_dbl_hinge_make_right_door_bottom_width_mm_manual', 'bx_dbl_hinge_swing', 'bx_dbl_hinge_t_section_mullion_inswing', 'bx_dbl_hinge_french_door_mullion_outswing', 'bx_dbl_hinge_lock_brand', 'bx_dbl_hinge_austral_elegance_xc_lock_colour', 'bx_dbl_hinge_austral_elegance_xc_cylinder', 'bx_dbl_hinge_austral_elegance_xc_lock_height_location', 'bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lt_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lt_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_handle', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_handle', 'bx_dbl_hinge_commandex_hinged_lock_colour', 'bx_dbl_hinge_commandex_hinged_cylinder', 'bx_dbl_hinge_commandex_hinged_lock_height_location', 'bx_dbl_hinge_commandex_hinged_lh_top_cut_out', 'bx_dbl_hinge_commandex_hinged_lt_top_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_lt_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_lt_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_centre_handle', 'bx_dbl_hinge_commandex_hinged_lt_centre_handle', 'bx_dbl_hinge_lockwood_8654_lock_colour', 'bx_dbl_hinge_lockwood_8654_cylinder', 'bx_dbl_hinge_lockwood_8654_lock_height_location', 'bx_dbl_hinge_lockwood_8654_lh_top_cut_out', 'bx_dbl_hinge_lockwood_8654_lt_top_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_lt_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_lt_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_centre_handle', 'bx_dbl_hinge_lockwood_8654_lt_centre_handle', 'bx_dbl_hinge_whitco_mk2_lock_colour', 'bx_dbl_hinge_whitco_mk2_cylinder', 'bx_dbl_hinge_whitco_mk2_lock_height_location', 'bx_dbl_hinge_whitco_mk2_lh_top_cut_out', 'bx_dbl_hinge_whitco_mk2_lt_top_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_lt_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_lt_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_centre_handle', 'bx_dbl_hinge_whitco_mk2_lt_centre_handle', 'bx_dbl_hinge_whitco_tasman_escape_lock_colour', 'bx_dbl_hinge_whitco_tasman_escape_cylinder', 'bx_dbl_hinge_whitco_tasman_escape_lock_height_location', 'bx_dbl_hinge_whitco_tasman_escape_lh_top_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lt_top_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_bottom_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lt_bottom_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_handle', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_handle', 'bx_dbl_hinge_austral_elegance_xc_co_cylinder', 'bx_dbl_hinge_austral_elegance_xc_co_lock_height_location', 'bx_dbl_hinge_austral_elegance_xc_co_lh_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lt_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lt_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_handle', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_handle', 'bx_dbl_hinge_commandex_hinged_co_cylinder', 'bx_dbl_hinge_commandex_hinged_co_lock_height_location', 'bx_dbl_hinge_commandex_hinged_co_lh_top_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lt_top_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lt_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_handle', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_handle', 'bx_dbl_hinge_lockwood_8654_co_cylinder', 'bx_dbl_hinge_lockwood_8654_co_lock_height_location', 'bx_dbl_hinge_lockwood_8654_co_lh_top_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lt_top_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lt_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_handle', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_handle', 'bx_dbl_hinge_whitco_mk2_co_cylinder', 'bx_dbl_hinge_whitco_mk2_co_lock_height_location', 'bx_dbl_hinge_whitco_mk2_co_lh_top_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lt_top_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lt_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_handle', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_handle', 'bx_dbl_hinge_lock_cut_out_side_dd', 'bx_dbl_hinge_non_lock_door_striker_cutouts', 'bx_dbl_hinge_non_lock_door_bolts', 'bx_dbl_hinge_patio_bolt_colour_non_lock', 'bx_dbl_hinge_midrail_case1', 'bx_dbl_hinge_midrail_height_mm_case1', 'bx_dbl_hinge_midrail_colour_std_case1', 'bx_dbl_hinge_midrail_colour_special_case1', 'bx_dbl_hinge_midrail_case2', 'bx_dbl_hinge_midrail_height_mm_case2', 'bx_dbl_hinge_midrail_colour_std_case2', 'bx_dbl_hinge_midrail_colour_special_case2', 'bx_dbl_hinge_num_sec_hinges_pickup', 'bx_dbl_hinge_num_sec_hinges_deliver', 'bx_dbl_hinge_spec_hinge_loc_2_pickup_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_pickup', 'bx_dbl_hinge_spec_hinge_loc_3_pickup_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_pickup', 'bx_dbl_hinge_spec_hinge_loc_4_pickup_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_pickup', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_pickup', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_pickup', 'bx_dbl_hinge_spec_hinge_loc_2_deliver_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_deliver', 'bx_dbl_hinge_spec_hinge_loc_3_deliver_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_deliver', 'bx_dbl_hinge_spec_hinge_loc_4_deliver_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_deliver', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_deliver', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_deliver', 'bx_dbl_hinge_sec_hinge_type', 'bx_dbl_hinge_sec_hinge_colour_std', 'bx_dbl_hinge_sec_hinge_colour_prong', 'bx_dbl_hinge_sec_hinge_colour_step', 'bx_dbl_hinge_sec_hinge_packers_qty', 'bx_dbl_hinge_closer', 'bx_dbl_hinge_closer_colour_austral', 'bx_dbl_hinge_closer_colour_whitco', 'bx_dbl_hinge_pet_door', 'bx_dbl_hinge_pet_door_colour', 'bx_dbl_hinge_pet_door_location', 'bx_dbl_hinge_pet_door_flexible_flap', 'bx_dbl_hinge_pet_door_surround_infill', 'bx_dbl_hinge_bug_seal_size', 'bx_dbl_hinge_left_bug_seal_length_mm', 'bx_dbl_hinge_right_bug_seal_length_mm', 'bx_dbl_hinge_stop_bead_colour', 'bx_dbl_hinge_stop_bead_left_length_mm', 'bx_dbl_hinge_stop_bead_top_length_mm', 'bx_dbl_hinge_stop_bead_right_length_mm', 'bx_dbl_hinge_adhesive_mohair', 'bx_dbl_hinge_adhesive_mohair_length_mm', 'bx_dbl_hinge_jamb_adaptor_type_case1', 'bx_dbl_hinge_jamb_adaptor_type_case2', 'bx_dbl_hinge_jamb_adaptor_type_case3', 'bx_dbl_hinge_jamb_opening_left_height_mm', 'bx_dbl_hinge_jamb_height_addition_mm', 'bx_dbl_hinge_jamb_adaptor_left_length_mm', 'bx_dbl_hinge_jamb_opening_top_width_mm', 'bx_dbl_hinge_jamb_width_addition_mm', 'bx_dbl_hinge_jamb_adaptor_top_length_mm', 'bx_dbl_hinge_jamb_opening_right_height_mm', 'bx_dbl_hinge_jamb_adaptor_right_length_mm', '_CALCULATED_deduction_assistance', '_CALCULATED_door_split_type', '_CALCULATED_lock_height', '_CALCULATED_manual_left_height', '_CALCULATED_manual_right_height', '_CALCULATED_height_calculation_method', '_CALCULATED_is_even_split', '_CALCULATED_is_manual_mode', '_CALCULATED_is_uneven_split', '_CALCULATED_largest_door_height', '_CALCULATED_smallest_door_height', '_CALCULATED_halfway_point', '_CALCULATED_halfway_minus_79', '_CALCULATED_halfway_plus_16', '_CALCULATED_halfway_plus_32', '_CALCULATED_halfway_plus_79', '_CALCULATED_height_minus_1000', '_CALCULATED_height_minus_1003', '_CALCULATED_height_minus_1019', '_CALCULATED_height_minus_1090', '_CALCULATED_height_minus_1098', '_CALCULATED_height_minus_1137', '_CALCULATED_height_minus_1153', '_CALCULATED_height_minus_1169', '_CALCULATED_height_minus_1248', '_CALCULATED_height_minus_270', '_CALCULATED_height_minus_317', '_CALCULATED_height_minus_330', '_CALCULATED_height_minus_333', '_CALCULATED_height_minus_349', '_CALCULATED_height_minus_428', '_CALCULATED_height_minus_740', '_CALCULATED_height_minus_787', '_CALCULATED_height_minus_800', '_CALCULATED_height_minus_803', '_CALCULATED_height_minus_819', '_CALCULATED_height_minus_898', '_CALCULATED_height_minus_940', '_CALCULATED_height_minus_987', '_CALCULATED_Largest_Sum_Door_Width', '_CALCULATED_door_width', '_CALCULATED_even_bottom_width', '_CALCULATED_even_middle_width', '_CALCULATED_even_top_width', '_CALCULATED_jamb_adaptor_left_length_mm', '_CALCULATED_jamb_adaptor_top_length_mm', '_CALCULATED_largest_door_width', '_CALCULATED_largest_left_door_width', '_CALCULATED_largest_right_door_width', '_CALCULATED_left_Clamp_Product', '_CALCULATED_left_door_middle_width', '_CALCULATED_left_height_door', '_CALCULATED_manual_formula', '_CALCULATED_right_Clamp_Product', '_CALCULATED_right_door_middle_width', '_CALCULATED_right_height_door', '_CALCULATED_smallest_door_width', '_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_mesh_height', '_CALCULATED_midrail_case1', '_CALCULATED_mesh_series', '_CALCULATED_midrail_case2', '_CALCULATED_midrail_height', '_CALCULATED_mesh_area', '_CALCULATED_mesh_operation_required', '_CALCULATED_mesh_operation_type', '_CALCULATED_mesh_area_m2', '_CALCULATED_mesh_perimeter', '_CALCULATED_mesh_aspect_ratio', '_CALCULATED_mesh_size_category'] 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Found quantity multiplier: 1.0 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Using field/option mapping calculation (no config_id) 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] WARNING: This may result in different operation costs than save config 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Returning 50 operations with total cost 221.871536 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COST_METRICS] {'config_id': None, 'operation_count': 50, 'total_cost': 221.87, 'calculation_method': 'field_option_mapping', 'timestamp': 'unknown'} 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COST_METRICS] WARNING: Using field/option mapping - may differ from save config 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Calculating operation costs for template 24 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Received field_values: {'bx_dbl_hinge_location': '', 'bx_dbl_hinge_location_other': '', 'bx_dbl_hinge_jamb_reveal_gt_20mm': '', 'bx_dbl_hinge_screen_clears_handle': '', 'bx_dbl_hinge_new_handle_clears_existing': '', 'bx_dbl_hinge_swing_path_clear': '', 'bx_dbl_hinge_obstruction_description': '', 'bx_dbl_hinge_quantity': '1', 'bx_dbl_hinge_frame_colour': 'black_custom_matt_gn248a', 'bx_dbl_hinge_powder_coat_colour': '', 'bx_dbl_hinge_custom_powder_coat_name': '', 'bx_dbl_hinge_custom_powder_coat_finish': '', 'bx_dbl_hinge_custom_powder_coat_code': '', 'bx_dbl_hinge_deduction_assistance': 'no', 'bx_dbl_hinge_door_split_type': 'even', 'bx_dbl_hinge_opening_height_mm_even': '', 'bx_dbl_hinge_height_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_height_mm_even': '', 'bx_dbl_hinge_opening_top_width_mm_even': '', 'bx_dbl_hinge_top_width_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_top_width_mm_even': '', 'bx_dbl_hinge_opening_middle_width_mm_even': '', 'bx_dbl_hinge_middle_width_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_middle_width_mm_even': '', 'bx_dbl_hinge_opening_bottom_width_mm_even': '', 'bx_dbl_hinge_bottom_width_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_bottom_width_mm_even': '', 'bx_dbl_hinge_left_opening_height_mm_uneven': '', 'bx_dbl_hinge_height_deduction_mm_uneven_left': '', 'bx_dbl_hinge_make_left_door_height_mm_uneven': '', 'bx_dbl_hinge_left_jamb_centre_top_mm_uneven': '', 'bx_dbl_hinge_top_left_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_left_door_top_width_mm_uneven': '', 'bx_dbl_hinge_left_jamb_centre_middle_mm_uneven': '', 'bx_dbl_hinge_middle_left_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_left_door_middle_width_mm_uneven': '', 'bx_dbl_hinge_left_jamb_centre_bottom_mm_uneven': '', 'bx_dbl_hinge_bottom_left_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_left_door_bottom_width_mm_uneven': '', 'bx_dbl_hinge_right_opening_height_mm_uneven': '', 'bx_dbl_hinge_height_deduction_mm_uneven_right': '', 'bx_dbl_hinge_make_right_door_height_mm_uneven': '', 'bx_dbl_hinge_right_jamb_centre_top_mm_uneven': '', 'bx_dbl_hinge_top_right_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_right_door_top_width_mm_uneven': '', 'bx_dbl_hinge_right_jamb_centre_middle_mm_uneven': '', 'bx_dbl_hinge_middle_right_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_right_door_middle_width_mm_uneven': '', 'bx_dbl_hinge_right_jamb_centre_bottom_mm_uneven': '', 'bx_dbl_hinge_bottom_right_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_right_door_bottom_width_mm_uneven': '', 'bx_dbl_hinge_make_left_door_height_mm_manual': '2110', 'bx_dbl_hinge_make_left_door_top_width_mm_manual': '860', 'bx_dbl_hinge_make_left_door_middle_width_mm_manual': '860', 'bx_dbl_hinge_make_left_door_bottom_width_mm_manual': '860', 'bx_dbl_hinge_make_right_door_height_mm_manual': '2110', 'bx_dbl_hinge_make_right_door_top_width_mm_manual': '860', 'bx_dbl_hinge_make_right_door_middle_width_mm_manual': '860', 'bx_dbl_hinge_make_right_door_bottom_width_mm_manual': '860', 'bx_dbl_hinge_swing': 'outswing', 'bx_dbl_hinge_t_section_mullion_inswing': 'yes', 'bx_dbl_hinge_french_door_mullion_outswing': 'yes', 'bx_dbl_hinge_lock_brand': 'commandex_hinged', 'bx_dbl_hinge_austral_elegance_xc_lock_colour': 'black', 'bx_dbl_hinge_austral_elegance_xc_cylinder': 'austral_5_pin_dbl', 'bx_dbl_hinge_austral_elegance_xc_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_lt_top_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_lh_bottom_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_handle': '', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_handle': 'single', 'bx_dbl_hinge_commandex_hinged_lock_colour': 'black', 'bx_dbl_hinge_commandex_hinged_cylinder': 'commandex_5_pin', 'bx_dbl_hinge_commandex_hinged_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_top_cut_out': '', 'bx_dbl_hinge_commandex_hinged_lt_top_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_lh_centre_cut_out': '', 'bx_dbl_hinge_commandex_hinged_lt_centre_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_lh_bottom_cut_out': '', 'bx_dbl_hinge_commandex_hinged_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_lh_centre_handle': '', 'bx_dbl_hinge_commandex_hinged_lt_centre_handle': 'single', 'bx_dbl_hinge_lockwood_8654_lock_colour': 'black', 'bx_dbl_hinge_lockwood_8654_cylinder': 'whitco_5_pin', 'bx_dbl_hinge_lockwood_8654_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_top_cut_out': '', 'bx_dbl_hinge_lockwood_8654_lt_top_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_lh_centre_cut_out': '', 'bx_dbl_hinge_lockwood_8654_lt_centre_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_lh_bottom_cut_out': '', 'bx_dbl_hinge_lockwood_8654_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_lh_centre_handle': '', 'bx_dbl_hinge_lockwood_8654_lt_centre_handle': 'single', 'bx_dbl_hinge_whitco_mk2_lock_colour': 'black', 'bx_dbl_hinge_whitco_mk2_cylinder': 'whitco_5_pin', 'bx_dbl_hinge_whitco_mk2_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_top_cut_out': '', 'bx_dbl_hinge_whitco_mk2_lt_top_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_lh_centre_cut_out': '', 'bx_dbl_hinge_whitco_mk2_lt_centre_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_lh_bottom_cut_out': '', 'bx_dbl_hinge_whitco_mk2_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_lh_centre_handle': '', 'bx_dbl_hinge_whitco_mk2_lt_centre_handle': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lock_colour': 'black', 'bx_dbl_hinge_whitco_tasman_escape_cylinder': 'whitco_escape_cylinder_turn_knob', 'bx_dbl_hinge_whitco_tasman_escape_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_top_cut_out': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_top_cut_out': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_cut_out': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_cut_out': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lh_bottom_cut_out': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_handle': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_handle': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_austral_elegance_xc_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_top_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_handle': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_handle': 'single', 'bx_dbl_hinge_commandex_hinged_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_commandex_hinged_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_top_cut_out': '', 'bx_dbl_hinge_commandex_hinged_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_commandex_hinged_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_cut_out': '', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_handle': '', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_handle': 'single', 'bx_dbl_hinge_lockwood_8654_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_lockwood_8654_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_top_cut_out': '', 'bx_dbl_hinge_lockwood_8654_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_cut_out': '', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_lockwood_8654_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_handle': '', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_handle': 'single', 'bx_dbl_hinge_whitco_mk2_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_whitco_mk2_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_top_cut_out': '', 'bx_dbl_hinge_whitco_mk2_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_cut_out': '', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_whitco_mk2_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_handle': '', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_handle': 'single', 'bx_dbl_hinge_lock_cut_out_side_dd': 'left_hand_door_right_hand_lock', 'bx_dbl_hinge_non_lock_door_striker_cutouts': 'yes', 'bx_dbl_hinge_non_lock_door_bolts': 'top_bottom_flush_bolts', 'bx_dbl_hinge_patio_bolt_colour_non_lock': 'black', 'bx_dbl_hinge_midrail_case1': 'yes', 'bx_dbl_hinge_midrail_height_mm_case1': '', 'bx_dbl_hinge_midrail_colour_std_case1': '', 'bx_dbl_hinge_midrail_colour_special_case1': 'black', 'bx_dbl_hinge_midrail_case2': 'no', 'bx_dbl_hinge_midrail_height_mm_case2': '', 'bx_dbl_hinge_midrail_colour_std_case2': 'black', 'bx_dbl_hinge_midrail_colour_special_case2': 'black', 'bx_dbl_hinge_num_sec_hinges_pickup': '3_per_door_attached', 'bx_dbl_hinge_num_sec_hinges_deliver': '3_per_door_loose_drilled', 'bx_dbl_hinge_spec_hinge_loc_2_pickup_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_pickup': '', 'bx_dbl_hinge_spec_hinge_loc_3_pickup_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_pickup': '', 'bx_dbl_hinge_spec_hinge_loc_4_pickup_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_pickup': '', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_pickup': '', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_pickup': '', 'bx_dbl_hinge_spec_hinge_loc_2_deliver_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_deliver': '', 'bx_dbl_hinge_spec_hinge_loc_3_deliver_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_deliver': '', 'bx_dbl_hinge_spec_hinge_loc_4_deliver_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_deliver': '', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_deliver': '', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_deliver': '', 'bx_dbl_hinge_sec_hinge_type': 'security_hinge', 'bx_dbl_hinge_sec_hinge_colour_std': 'black', 'bx_dbl_hinge_sec_hinge_colour_prong': 'black', 'bx_dbl_hinge_sec_hinge_colour_step': 'black', 'bx_dbl_hinge_sec_hinge_packers_qty': '0', 'bx_dbl_hinge_closer': 'no', 'bx_dbl_hinge_closer_colour_austral': 'black', 'bx_dbl_hinge_closer_colour_whitco': 'black', 'bx_dbl_hinge_pet_door': 'no', 'bx_dbl_hinge_pet_door_colour': 'black', 'bx_dbl_hinge_pet_door_location': 'lock_door_lock_side', 'bx_dbl_hinge_pet_door_flexible_flap': 'no', 'bx_dbl_hinge_pet_door_surround_infill': 'no', 'bx_dbl_hinge_bug_seal_size': 'no', 'bx_dbl_hinge_left_bug_seal_length_mm': '', 'bx_dbl_hinge_right_bug_seal_length_mm': '', 'bx_dbl_hinge_stop_bead_colour': 'no', 'bx_dbl_hinge_stop_bead_left_length_mm': '', 'bx_dbl_hinge_stop_bead_top_length_mm': '', 'bx_dbl_hinge_stop_bead_right_length_mm': '', 'bx_dbl_hinge_adhesive_mohair': 'no', 'bx_dbl_hinge_adhesive_mohair_length_mm': '', 'bx_dbl_hinge_jamb_adaptor_type_case1': 'no', 'bx_dbl_hinge_jamb_adaptor_type_case2': '', 'bx_dbl_hinge_jamb_adaptor_type_case3': '', 'bx_dbl_hinge_jamb_opening_left_height_mm': '', 'bx_dbl_hinge_jamb_height_addition_mm': '20', 'bx_dbl_hinge_jamb_adaptor_left_length_mm': '', 'bx_dbl_hinge_jamb_opening_top_width_mm': '', 'bx_dbl_hinge_jamb_width_addition_mm': '40', 'bx_dbl_hinge_jamb_adaptor_top_length_mm': '', 'bx_dbl_hinge_jamb_opening_right_height_mm': '', 'bx_dbl_hinge_jamb_adaptor_right_length_mm': '', '_CALCULATED_deduction_assistance': 'no', '_CALCULATED_door_split_type': 'even', '_CALCULATED_lock_height': 0, '_CALCULATED_manual_left_height': 2110, '_CALCULATED_manual_right_height': 0, '_CALCULATED_height_calculation_method': 'manual', '_CALCULATED_is_even_split': True, '_CALCULATED_is_manual_mode': True, '_CALCULATED_is_uneven_split': False, '_CALCULATED_largest_door_height': 2110, '_CALCULATED_smallest_door_height': 20, '_CALCULATED_halfway_point': 0, '_CALCULATED_halfway_minus_79': -79, '_CALCULATED_halfway_plus_16': 16, '_CALCULATED_halfway_plus_32': 32, '_CALCULATED_halfway_plus_79': 79, '_CALCULATED_height_minus_1000': -1000, '_CALCULATED_height_minus_1003': -1003, '_CALCULATED_height_minus_1019': -1019, '_CALCULATED_height_minus_1090': -1090, '_CALCULATED_height_minus_1098': -1098, '_CALCULATED_height_minus_1137': -1137, '_CALCULATED_height_minus_1153': -1153, '_CALCULATED_height_minus_1169': -1169, '_CALCULATED_height_minus_1248': -1248, '_CALCULATED_height_minus_270': -270, '_CALCULATED_height_minus_317': -317, '_CALCULATED_height_minus_330': -330, '_CALCULATED_height_minus_333': -333, '_CALCULATED_height_minus_349': -349, '_CALCULATED_height_minus_428': -428, '_CALCULATED_height_minus_740': -740, '_CALCULATED_height_minus_787': -787, '_CALCULATED_height_minus_800': -800, '_CALCULATED_height_minus_803': -803, '_CALCULATED_height_minus_819': -819, '_CALCULATED_height_minus_898': -898, '_CALCULATED_height_minus_940': -940, '_CALCULATED_height_minus_987': -987, '_CALCULATED_Largest_Sum_Door_Width': 430, '_CALCULATED_door_width': 1720, '_CALCULATED_even_bottom_width': 0, '_CALCULATED_even_middle_width': 0, '_CALCULATED_even_top_width': 0, '_CALCULATED_jamb_adaptor_left_length_mm': 20, '_CALCULATED_jamb_adaptor_top_length_mm': 40, '_CALCULATED_largest_door_width': 860, '_CALCULATED_largest_left_door_width': 860, '_CALCULATED_largest_right_door_width': 0, '_CALCULATED_left_Clamp_Product': 'yes', '_CALCULATED_left_door_middle_width': 860, '_CALCULATED_left_height_door': 2110, '_CALCULATED_manual_formula': 'min(2110.0, 0) = 0', '_CALCULATED_right_Clamp_Product': 'no', '_CALCULATED_right_door_middle_width': 0, '_CALCULATED_right_height_door': 0, '_CALCULATED_smallest_door_width': 0, '_CALCULATED_mesh_required': True, '_CALCULATED_mesh_width': 860, '_CALCULATED_mesh_height': 2110, '_CALCULATED_midrail_case1': False, '_CALCULATED_mesh_series': 'basix', '_CALCULATED_midrail_case2': False, '_CALCULATED_midrail_height': 0, '_CALCULATED_mesh_area': 1814600, '_CALCULATED_mesh_operation_required': True, '_CALCULATED_mesh_operation_type': None, '_CALCULATED_mesh_area_m2': 1.8146, '_CALCULATED_mesh_perimeter': 5940, '_CALCULATED_mesh_aspect_ratio': 0.4075829383886256, '_CALCULATED_mesh_size_category': None} 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Field values keys: ['bx_dbl_hinge_location', 'bx_dbl_hinge_location_other', 'bx_dbl_hinge_jamb_reveal_gt_20mm', 'bx_dbl_hinge_screen_clears_handle', 'bx_dbl_hinge_new_handle_clears_existing', 'bx_dbl_hinge_swing_path_clear', 'bx_dbl_hinge_obstruction_description', 'bx_dbl_hinge_quantity', 'bx_dbl_hinge_frame_colour', 'bx_dbl_hinge_powder_coat_colour', 'bx_dbl_hinge_custom_powder_coat_name', 'bx_dbl_hinge_custom_powder_coat_finish', 'bx_dbl_hinge_custom_powder_coat_code', 'bx_dbl_hinge_deduction_assistance', 'bx_dbl_hinge_door_split_type', 'bx_dbl_hinge_opening_height_mm_even', 'bx_dbl_hinge_height_deduction_mm_even', 'bx_dbl_hinge_make_each_door_height_mm_even', 'bx_dbl_hinge_opening_top_width_mm_even', 'bx_dbl_hinge_top_width_deduction_mm_even', 'bx_dbl_hinge_make_each_door_top_width_mm_even', 'bx_dbl_hinge_opening_middle_width_mm_even', 'bx_dbl_hinge_middle_width_deduction_mm_even', 'bx_dbl_hinge_make_each_door_middle_width_mm_even', 'bx_dbl_hinge_opening_bottom_width_mm_even', 'bx_dbl_hinge_bottom_width_deduction_mm_even', 'bx_dbl_hinge_make_each_door_bottom_width_mm_even', 'bx_dbl_hinge_left_opening_height_mm_uneven', 'bx_dbl_hinge_height_deduction_mm_uneven_left', 'bx_dbl_hinge_make_left_door_height_mm_uneven', 'bx_dbl_hinge_left_jamb_centre_top_mm_uneven', 'bx_dbl_hinge_top_left_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_left_door_top_width_mm_uneven', 'bx_dbl_hinge_left_jamb_centre_middle_mm_uneven', 'bx_dbl_hinge_middle_left_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_left_door_middle_width_mm_uneven', 'bx_dbl_hinge_left_jamb_centre_bottom_mm_uneven', 'bx_dbl_hinge_bottom_left_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_left_door_bottom_width_mm_uneven', 'bx_dbl_hinge_right_opening_height_mm_uneven', 'bx_dbl_hinge_height_deduction_mm_uneven_right', 'bx_dbl_hinge_make_right_door_height_mm_uneven', 'bx_dbl_hinge_right_jamb_centre_top_mm_uneven', 'bx_dbl_hinge_top_right_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_right_door_top_width_mm_uneven', 'bx_dbl_hinge_right_jamb_centre_middle_mm_uneven', 'bx_dbl_hinge_middle_right_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_right_door_middle_width_mm_uneven', 'bx_dbl_hinge_right_jamb_centre_bottom_mm_uneven', 'bx_dbl_hinge_bottom_right_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_right_door_bottom_width_mm_uneven', 'bx_dbl_hinge_make_left_door_height_mm_manual', 'bx_dbl_hinge_make_left_door_top_width_mm_manual', 'bx_dbl_hinge_make_left_door_middle_width_mm_manual', 'bx_dbl_hinge_make_left_door_bottom_width_mm_manual', 'bx_dbl_hinge_make_right_door_height_mm_manual', 'bx_dbl_hinge_make_right_door_top_width_mm_manual', 'bx_dbl_hinge_make_right_door_middle_width_mm_manual', 'bx_dbl_hinge_make_right_door_bottom_width_mm_manual', 'bx_dbl_hinge_swing', 'bx_dbl_hinge_t_section_mullion_inswing', 'bx_dbl_hinge_french_door_mullion_outswing', 'bx_dbl_hinge_lock_brand', 'bx_dbl_hinge_austral_elegance_xc_lock_colour', 'bx_dbl_hinge_austral_elegance_xc_cylinder', 'bx_dbl_hinge_austral_elegance_xc_lock_height_location', 'bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lt_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lt_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_handle', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_handle', 'bx_dbl_hinge_commandex_hinged_lock_colour', 'bx_dbl_hinge_commandex_hinged_cylinder', 'bx_dbl_hinge_commandex_hinged_lock_height_location', 'bx_dbl_hinge_commandex_hinged_lh_top_cut_out', 'bx_dbl_hinge_commandex_hinged_lt_top_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_lt_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_lt_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_centre_handle', 'bx_dbl_hinge_commandex_hinged_lt_centre_handle', 'bx_dbl_hinge_lockwood_8654_lock_colour', 'bx_dbl_hinge_lockwood_8654_cylinder', 'bx_dbl_hinge_lockwood_8654_lock_height_location', 'bx_dbl_hinge_lockwood_8654_lh_top_cut_out', 'bx_dbl_hinge_lockwood_8654_lt_top_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_lt_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_lt_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_centre_handle', 'bx_dbl_hinge_lockwood_8654_lt_centre_handle', 'bx_dbl_hinge_whitco_mk2_lock_colour', 'bx_dbl_hinge_whitco_mk2_cylinder', 'bx_dbl_hinge_whitco_mk2_lock_height_location', 'bx_dbl_hinge_whitco_mk2_lh_top_cut_out', 'bx_dbl_hinge_whitco_mk2_lt_top_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_lt_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_lt_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_centre_handle', 'bx_dbl_hinge_whitco_mk2_lt_centre_handle', 'bx_dbl_hinge_whitco_tasman_escape_lock_colour', 'bx_dbl_hinge_whitco_tasman_escape_cylinder', 'bx_dbl_hinge_whitco_tasman_escape_lock_height_location', 'bx_dbl_hinge_whitco_tasman_escape_lh_top_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lt_top_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_bottom_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lt_bottom_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_handle', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_handle', 'bx_dbl_hinge_austral_elegance_xc_co_cylinder', 'bx_dbl_hinge_austral_elegance_xc_co_lock_height_location', 'bx_dbl_hinge_austral_elegance_xc_co_lh_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lt_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lt_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_handle', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_handle', 'bx_dbl_hinge_commandex_hinged_co_cylinder', 'bx_dbl_hinge_commandex_hinged_co_lock_height_location', 'bx_dbl_hinge_commandex_hinged_co_lh_top_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lt_top_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lt_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_handle', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_handle', 'bx_dbl_hinge_lockwood_8654_co_cylinder', 'bx_dbl_hinge_lockwood_8654_co_lock_height_location', 'bx_dbl_hinge_lockwood_8654_co_lh_top_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lt_top_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lt_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_handle', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_handle', 'bx_dbl_hinge_whitco_mk2_co_cylinder', 'bx_dbl_hinge_whitco_mk2_co_lock_height_location', 'bx_dbl_hinge_whitco_mk2_co_lh_top_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lt_top_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lt_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_handle', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_handle', 'bx_dbl_hinge_lock_cut_out_side_dd', 'bx_dbl_hinge_non_lock_door_striker_cutouts', 'bx_dbl_hinge_non_lock_door_bolts', 'bx_dbl_hinge_patio_bolt_colour_non_lock', 'bx_dbl_hinge_midrail_case1', 'bx_dbl_hinge_midrail_height_mm_case1', 'bx_dbl_hinge_midrail_colour_std_case1', 'bx_dbl_hinge_midrail_colour_special_case1', 'bx_dbl_hinge_midrail_case2', 'bx_dbl_hinge_midrail_height_mm_case2', 'bx_dbl_hinge_midrail_colour_std_case2', 'bx_dbl_hinge_midrail_colour_special_case2', 'bx_dbl_hinge_num_sec_hinges_pickup', 'bx_dbl_hinge_num_sec_hinges_deliver', 'bx_dbl_hinge_spec_hinge_loc_2_pickup_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_pickup', 'bx_dbl_hinge_spec_hinge_loc_3_pickup_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_pickup', 'bx_dbl_hinge_spec_hinge_loc_4_pickup_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_pickup', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_pickup', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_pickup', 'bx_dbl_hinge_spec_hinge_loc_2_deliver_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_deliver', 'bx_dbl_hinge_spec_hinge_loc_3_deliver_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_deliver', 'bx_dbl_hinge_spec_hinge_loc_4_deliver_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_deliver', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_deliver', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_deliver', 'bx_dbl_hinge_sec_hinge_type', 'bx_dbl_hinge_sec_hinge_colour_std', 'bx_dbl_hinge_sec_hinge_colour_prong', 'bx_dbl_hinge_sec_hinge_colour_step', 'bx_dbl_hinge_sec_hinge_packers_qty', 'bx_dbl_hinge_closer', 'bx_dbl_hinge_closer_colour_austral', 'bx_dbl_hinge_closer_colour_whitco', 'bx_dbl_hinge_pet_door', 'bx_dbl_hinge_pet_door_colour', 'bx_dbl_hinge_pet_door_location', 'bx_dbl_hinge_pet_door_flexible_flap', 'bx_dbl_hinge_pet_door_surround_infill', 'bx_dbl_hinge_bug_seal_size', 'bx_dbl_hinge_left_bug_seal_length_mm', 'bx_dbl_hinge_right_bug_seal_length_mm', 'bx_dbl_hinge_stop_bead_colour', 'bx_dbl_hinge_stop_bead_left_length_mm', 'bx_dbl_hinge_stop_bead_top_length_mm', 'bx_dbl_hinge_stop_bead_right_length_mm', 'bx_dbl_hinge_adhesive_mohair', 'bx_dbl_hinge_adhesive_mohair_length_mm', 'bx_dbl_hinge_jamb_adaptor_type_case1', 'bx_dbl_hinge_jamb_adaptor_type_case2', 'bx_dbl_hinge_jamb_adaptor_type_case3', 'bx_dbl_hinge_jamb_opening_left_height_mm', 'bx_dbl_hinge_jamb_height_addition_mm', 'bx_dbl_hinge_jamb_adaptor_left_length_mm', 'bx_dbl_hinge_jamb_opening_top_width_mm', 'bx_dbl_hinge_jamb_width_addition_mm', 'bx_dbl_hinge_jamb_adaptor_top_length_mm', 'bx_dbl_hinge_jamb_opening_right_height_mm', 'bx_dbl_hinge_jamb_adaptor_right_length_mm', '_CALCULATED_deduction_assistance', '_CALCULATED_door_split_type', '_CALCULATED_lock_height', '_CALCULATED_manual_left_height', '_CALCULATED_manual_right_height', '_CALCULATED_height_calculation_method', '_CALCULATED_is_even_split', '_CALCULATED_is_manual_mode', '_CALCULATED_is_uneven_split', '_CALCULATED_largest_door_height', '_CALCULATED_smallest_door_height', '_CALCULATED_halfway_point', '_CALCULATED_halfway_minus_79', '_CALCULATED_halfway_plus_16', '_CALCULATED_halfway_plus_32', '_CALCULATED_halfway_plus_79', '_CALCULATED_height_minus_1000', '_CALCULATED_height_minus_1003', '_CALCULATED_height_minus_1019', '_CALCULATED_height_minus_1090', '_CALCULATED_height_minus_1098', '_CALCULATED_height_minus_1137', '_CALCULATED_height_minus_1153', '_CALCULATED_height_minus_1169', '_CALCULATED_height_minus_1248', '_CALCULATED_height_minus_270', '_CALCULATED_height_minus_317', '_CALCULATED_height_minus_330', '_CALCULATED_height_minus_333', '_CALCULATED_height_minus_349', '_CALCULATED_height_minus_428', '_CALCULATED_height_minus_740', '_CALCULATED_height_minus_787', '_CALCULATED_height_minus_800', '_CALCULATED_height_minus_803', '_CALCULATED_height_minus_819', '_CALCULATED_height_minus_898', '_CALCULATED_height_minus_940', '_CALCULATED_height_minus_987', '_CALCULATED_Largest_Sum_Door_Width', '_CALCULATED_door_width', '_CALCULATED_even_bottom_width', '_CALCULATED_even_middle_width', '_CALCULATED_even_top_width', '_CALCULATED_jamb_adaptor_left_length_mm', '_CALCULATED_jamb_adaptor_top_length_mm', '_CALCULATED_largest_door_width', '_CALCULATED_largest_left_door_width', '_CALCULATED_largest_right_door_width', '_CALCULATED_left_Clamp_Product', '_CALCULATED_left_door_middle_width', '_CALCULATED_left_height_door', '_CALCULATED_manual_formula', '_CALCULATED_right_Clamp_Product', '_CALCULATED_right_door_middle_width', '_CALCULATED_right_height_door', '_CALCULATED_smallest_door_width', '_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_mesh_height', '_CALCULATED_midrail_case1', '_CALCULATED_mesh_series', '_CALCULATED_midrail_case2', '_CALCULATED_midrail_height', '_CALCULATED_mesh_area', '_CALCULATED_mesh_operation_required', '_CALCULATED_mesh_operation_type', '_CALCULATED_mesh_area_m2', '_CALCULATED_mesh_perimeter', '_CALCULATED_mesh_aspect_ratio', '_CALCULATED_mesh_size_category'] 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Found quantity multiplier: 1.0 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Using field/option mapping calculation (no config_id) 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] WARNING: This may result in different operation costs than save config 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Returning 50 operations with total cost 221.871536 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COST_METRICS] {'config_id': None, 'operation_count': 50, 'total_cost': 221.87, 'calculation_method': 'field_option_mapping', 'timestamp': 'unknown'} 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COST_METRICS] WARNING: Using field/option mapping - may differ from save config 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Calculating operation costs for template 24 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Received field_values: {'bx_dbl_hinge_location': '', 'bx_dbl_hinge_location_other': '', 'bx_dbl_hinge_jamb_reveal_gt_20mm': '', 'bx_dbl_hinge_screen_clears_handle': '', 'bx_dbl_hinge_new_handle_clears_existing': '', 'bx_dbl_hinge_swing_path_clear': '', 'bx_dbl_hinge_obstruction_description': '', 'bx_dbl_hinge_quantity': '1', 'bx_dbl_hinge_frame_colour': 'black_custom_matt_gn248a', 'bx_dbl_hinge_powder_coat_colour': '', 'bx_dbl_hinge_custom_powder_coat_name': '', 'bx_dbl_hinge_custom_powder_coat_finish': '', 'bx_dbl_hinge_custom_powder_coat_code': '', 'bx_dbl_hinge_deduction_assistance': 'no', 'bx_dbl_hinge_door_split_type': 'even', 'bx_dbl_hinge_opening_height_mm_even': '', 'bx_dbl_hinge_height_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_height_mm_even': '', 'bx_dbl_hinge_opening_top_width_mm_even': '', 'bx_dbl_hinge_top_width_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_top_width_mm_even': '', 'bx_dbl_hinge_opening_middle_width_mm_even': '', 'bx_dbl_hinge_middle_width_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_middle_width_mm_even': '', 'bx_dbl_hinge_opening_bottom_width_mm_even': '', 'bx_dbl_hinge_bottom_width_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_bottom_width_mm_even': '', 'bx_dbl_hinge_left_opening_height_mm_uneven': '', 'bx_dbl_hinge_height_deduction_mm_uneven_left': '', 'bx_dbl_hinge_make_left_door_height_mm_uneven': '', 'bx_dbl_hinge_left_jamb_centre_top_mm_uneven': '', 'bx_dbl_hinge_top_left_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_left_door_top_width_mm_uneven': '', 'bx_dbl_hinge_left_jamb_centre_middle_mm_uneven': '', 'bx_dbl_hinge_middle_left_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_left_door_middle_width_mm_uneven': '', 'bx_dbl_hinge_left_jamb_centre_bottom_mm_uneven': '', 'bx_dbl_hinge_bottom_left_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_left_door_bottom_width_mm_uneven': '', 'bx_dbl_hinge_right_opening_height_mm_uneven': '', 'bx_dbl_hinge_height_deduction_mm_uneven_right': '', 'bx_dbl_hinge_make_right_door_height_mm_uneven': '', 'bx_dbl_hinge_right_jamb_centre_top_mm_uneven': '', 'bx_dbl_hinge_top_right_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_right_door_top_width_mm_uneven': '', 'bx_dbl_hinge_right_jamb_centre_middle_mm_uneven': '', 'bx_dbl_hinge_middle_right_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_right_door_middle_width_mm_uneven': '', 'bx_dbl_hinge_right_jamb_centre_bottom_mm_uneven': '', 'bx_dbl_hinge_bottom_right_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_right_door_bottom_width_mm_uneven': '', 'bx_dbl_hinge_make_left_door_height_mm_manual': '2110', 'bx_dbl_hinge_make_left_door_top_width_mm_manual': '860', 'bx_dbl_hinge_make_left_door_middle_width_mm_manual': '860', 'bx_dbl_hinge_make_left_door_bottom_width_mm_manual': '860', 'bx_dbl_hinge_make_right_door_height_mm_manual': '2110', 'bx_dbl_hinge_make_right_door_top_width_mm_manual': '860', 'bx_dbl_hinge_make_right_door_middle_width_mm_manual': '860', 'bx_dbl_hinge_make_right_door_bottom_width_mm_manual': '860', 'bx_dbl_hinge_swing': 'outswing', 'bx_dbl_hinge_t_section_mullion_inswing': 'yes', 'bx_dbl_hinge_french_door_mullion_outswing': 'yes', 'bx_dbl_hinge_lock_brand': 'commandex_hinged', 'bx_dbl_hinge_austral_elegance_xc_lock_colour': 'black', 'bx_dbl_hinge_austral_elegance_xc_cylinder': 'austral_5_pin_dbl', 'bx_dbl_hinge_austral_elegance_xc_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_lt_top_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_lh_bottom_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_handle': '', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_handle': 'single', 'bx_dbl_hinge_commandex_hinged_lock_colour': 'black', 'bx_dbl_hinge_commandex_hinged_cylinder': 'commandex_5_pin', 'bx_dbl_hinge_commandex_hinged_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_top_cut_out': '', 'bx_dbl_hinge_commandex_hinged_lt_top_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_lh_centre_cut_out': '', 'bx_dbl_hinge_commandex_hinged_lt_centre_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_lh_bottom_cut_out': '', 'bx_dbl_hinge_commandex_hinged_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_lh_centre_handle': '', 'bx_dbl_hinge_commandex_hinged_lt_centre_handle': 'single', 'bx_dbl_hinge_lockwood_8654_lock_colour': 'black', 'bx_dbl_hinge_lockwood_8654_cylinder': 'whitco_5_pin', 'bx_dbl_hinge_lockwood_8654_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_top_cut_out': '', 'bx_dbl_hinge_lockwood_8654_lt_top_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_lh_centre_cut_out': '', 'bx_dbl_hinge_lockwood_8654_lt_centre_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_lh_bottom_cut_out': '', 'bx_dbl_hinge_lockwood_8654_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_lh_centre_handle': '', 'bx_dbl_hinge_lockwood_8654_lt_centre_handle': 'single', 'bx_dbl_hinge_whitco_mk2_lock_colour': 'black', 'bx_dbl_hinge_whitco_mk2_cylinder': 'whitco_5_pin', 'bx_dbl_hinge_whitco_mk2_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_top_cut_out': '', 'bx_dbl_hinge_whitco_mk2_lt_top_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_lh_centre_cut_out': '', 'bx_dbl_hinge_whitco_mk2_lt_centre_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_lh_bottom_cut_out': '', 'bx_dbl_hinge_whitco_mk2_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_lh_centre_handle': '', 'bx_dbl_hinge_whitco_mk2_lt_centre_handle': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lock_colour': 'black', 'bx_dbl_hinge_whitco_tasman_escape_cylinder': 'whitco_escape_cylinder_turn_knob', 'bx_dbl_hinge_whitco_tasman_escape_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_top_cut_out': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_top_cut_out': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_cut_out': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_cut_out': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lh_bottom_cut_out': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_handle': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_handle': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_austral_elegance_xc_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_top_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_handle': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_handle': 'single', 'bx_dbl_hinge_commandex_hinged_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_commandex_hinged_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_top_cut_out': '', 'bx_dbl_hinge_commandex_hinged_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_commandex_hinged_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_cut_out': '', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_handle': '', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_handle': 'single', 'bx_dbl_hinge_lockwood_8654_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_lockwood_8654_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_top_cut_out': '', 'bx_dbl_hinge_lockwood_8654_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_cut_out': '', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_lockwood_8654_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_handle': '', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_handle': 'single', 'bx_dbl_hinge_whitco_mk2_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_whitco_mk2_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_top_cut_out': '', 'bx_dbl_hinge_whitco_mk2_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_cut_out': '', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_whitco_mk2_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_handle': '', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_handle': 'single', 'bx_dbl_hinge_lock_cut_out_side_dd': 'left_hand_door_right_hand_lock', 'bx_dbl_hinge_non_lock_door_striker_cutouts': 'yes', 'bx_dbl_hinge_non_lock_door_bolts': 'top_bottom_flush_bolts', 'bx_dbl_hinge_patio_bolt_colour_non_lock': 'black', 'bx_dbl_hinge_midrail_case1': 'yes', 'bx_dbl_hinge_midrail_height_mm_case1': '', 'bx_dbl_hinge_midrail_colour_std_case1': '', 'bx_dbl_hinge_midrail_colour_special_case1': 'black', 'bx_dbl_hinge_midrail_case2': 'yes', 'bx_dbl_hinge_midrail_height_mm_case2': '', 'bx_dbl_hinge_midrail_colour_std_case2': 'black', 'bx_dbl_hinge_midrail_colour_special_case2': 'black', 'bx_dbl_hinge_num_sec_hinges_pickup': '3_per_door_attached', 'bx_dbl_hinge_num_sec_hinges_deliver': '3_per_door_loose_drilled', 'bx_dbl_hinge_spec_hinge_loc_2_pickup_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_pickup': '', 'bx_dbl_hinge_spec_hinge_loc_3_pickup_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_pickup': '', 'bx_dbl_hinge_spec_hinge_loc_4_pickup_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_pickup': '', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_pickup': '', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_pickup': '', 'bx_dbl_hinge_spec_hinge_loc_2_deliver_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_deliver': '', 'bx_dbl_hinge_spec_hinge_loc_3_deliver_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_deliver': '', 'bx_dbl_hinge_spec_hinge_loc_4_deliver_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_deliver': '', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_deliver': '', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_deliver': '', 'bx_dbl_hinge_sec_hinge_type': 'security_hinge', 'bx_dbl_hinge_sec_hinge_colour_std': 'black', 'bx_dbl_hinge_sec_hinge_colour_prong': 'black', 'bx_dbl_hinge_sec_hinge_colour_step': 'black', 'bx_dbl_hinge_sec_hinge_packers_qty': '0', 'bx_dbl_hinge_closer': 'no', 'bx_dbl_hinge_closer_colour_austral': 'black', 'bx_dbl_hinge_closer_colour_whitco': 'black', 'bx_dbl_hinge_pet_door': 'no', 'bx_dbl_hinge_pet_door_colour': 'black', 'bx_dbl_hinge_pet_door_location': 'lock_door_lock_side', 'bx_dbl_hinge_pet_door_flexible_flap': 'no', 'bx_dbl_hinge_pet_door_surround_infill': 'no', 'bx_dbl_hinge_bug_seal_size': 'no', 'bx_dbl_hinge_left_bug_seal_length_mm': '', 'bx_dbl_hinge_right_bug_seal_length_mm': '', 'bx_dbl_hinge_stop_bead_colour': 'no', 'bx_dbl_hinge_stop_bead_left_length_mm': '', 'bx_dbl_hinge_stop_bead_top_length_mm': '', 'bx_dbl_hinge_stop_bead_right_length_mm': '', 'bx_dbl_hinge_adhesive_mohair': 'no', 'bx_dbl_hinge_adhesive_mohair_length_mm': '', 'bx_dbl_hinge_jamb_adaptor_type_case1': 'no', 'bx_dbl_hinge_jamb_adaptor_type_case2': '', 'bx_dbl_hinge_jamb_adaptor_type_case3': '', 'bx_dbl_hinge_jamb_opening_left_height_mm': '', 'bx_dbl_hinge_jamb_height_addition_mm': '20', 'bx_dbl_hinge_jamb_adaptor_left_length_mm': '', 'bx_dbl_hinge_jamb_opening_top_width_mm': '', 'bx_dbl_hinge_jamb_width_addition_mm': '40', 'bx_dbl_hinge_jamb_adaptor_top_length_mm': '', 'bx_dbl_hinge_jamb_opening_right_height_mm': '', 'bx_dbl_hinge_jamb_adaptor_right_length_mm': '', '_CALCULATED_deduction_assistance': 'no', '_CALCULATED_door_split_type': 'even', '_CALCULATED_lock_height': 0, '_CALCULATED_manual_left_height': 2110, '_CALCULATED_manual_right_height': 0, '_CALCULATED_height_calculation_method': 'manual', '_CALCULATED_is_even_split': True, '_CALCULATED_is_manual_mode': True, '_CALCULATED_is_uneven_split': False, '_CALCULATED_largest_door_height': 2110, '_CALCULATED_smallest_door_height': 20, '_CALCULATED_halfway_point': 0, '_CALCULATED_halfway_minus_79': -79, '_CALCULATED_halfway_plus_16': 16, '_CALCULATED_halfway_plus_32': 32, '_CALCULATED_halfway_plus_79': 79, '_CALCULATED_height_minus_1000': -1000, '_CALCULATED_height_minus_1003': -1003, '_CALCULATED_height_minus_1019': -1019, '_CALCULATED_height_minus_1090': -1090, '_CALCULATED_height_minus_1098': -1098, '_CALCULATED_height_minus_1137': -1137, '_CALCULATED_height_minus_1153': -1153, '_CALCULATED_height_minus_1169': -1169, '_CALCULATED_height_minus_1248': -1248, '_CALCULATED_height_minus_270': -270, '_CALCULATED_height_minus_317': -317, '_CALCULATED_height_minus_330': -330, '_CALCULATED_height_minus_333': -333, '_CALCULATED_height_minus_349': -349, '_CALCULATED_height_minus_428': -428, '_CALCULATED_height_minus_740': -740, '_CALCULATED_height_minus_787': -787, '_CALCULATED_height_minus_800': -800, '_CALCULATED_height_minus_803': -803, '_CALCULATED_height_minus_819': -819, '_CALCULATED_height_minus_898': -898, '_CALCULATED_height_minus_940': -940, '_CALCULATED_height_minus_987': -987, '_CALCULATED_Largest_Sum_Door_Width': 430, '_CALCULATED_door_width': 1720, '_CALCULATED_even_bottom_width': 0, '_CALCULATED_even_middle_width': 0, '_CALCULATED_even_top_width': 0, '_CALCULATED_jamb_adaptor_left_length_mm': 20, '_CALCULATED_jamb_adaptor_top_length_mm': 40, '_CALCULATED_largest_door_width': 860, '_CALCULATED_largest_left_door_width': 860, '_CALCULATED_largest_right_door_width': 0, '_CALCULATED_left_Clamp_Product': 'yes', '_CALCULATED_left_door_middle_width': 860, '_CALCULATED_left_height_door': 2110, '_CALCULATED_manual_formula': 'min(2110.0, 0) = 0', '_CALCULATED_right_Clamp_Product': 'no', '_CALCULATED_right_door_middle_width': 0, '_CALCULATED_right_height_door': 0, '_CALCULATED_smallest_door_width': 0, '_CALCULATED_mesh_required': True, '_CALCULATED_mesh_width': 860, '_CALCULATED_mesh_height': 2110, '_CALCULATED_midrail_case1': False, '_CALCULATED_mesh_series': 'basix', '_CALCULATED_midrail_case2': False, '_CALCULATED_midrail_height': 0, '_CALCULATED_mesh_area': 1814600, '_CALCULATED_mesh_operation_required': True, '_CALCULATED_mesh_operation_type': None, '_CALCULATED_mesh_area_m2': 1.8146, '_CALCULATED_mesh_perimeter': 5940, '_CALCULATED_mesh_aspect_ratio': 0.4075829383886256, '_CALCULATED_mesh_size_category': None} 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Field values keys: ['bx_dbl_hinge_location', 'bx_dbl_hinge_location_other', 'bx_dbl_hinge_jamb_reveal_gt_20mm', 'bx_dbl_hinge_screen_clears_handle', 'bx_dbl_hinge_new_handle_clears_existing', 'bx_dbl_hinge_swing_path_clear', 'bx_dbl_hinge_obstruction_description', 'bx_dbl_hinge_quantity', 'bx_dbl_hinge_frame_colour', 'bx_dbl_hinge_powder_coat_colour', 'bx_dbl_hinge_custom_powder_coat_name', 'bx_dbl_hinge_custom_powder_coat_finish', 'bx_dbl_hinge_custom_powder_coat_code', 'bx_dbl_hinge_deduction_assistance', 'bx_dbl_hinge_door_split_type', 'bx_dbl_hinge_opening_height_mm_even', 'bx_dbl_hinge_height_deduction_mm_even', 'bx_dbl_hinge_make_each_door_height_mm_even', 'bx_dbl_hinge_opening_top_width_mm_even', 'bx_dbl_hinge_top_width_deduction_mm_even', 'bx_dbl_hinge_make_each_door_top_width_mm_even', 'bx_dbl_hinge_opening_middle_width_mm_even', 'bx_dbl_hinge_middle_width_deduction_mm_even', 'bx_dbl_hinge_make_each_door_middle_width_mm_even', 'bx_dbl_hinge_opening_bottom_width_mm_even', 'bx_dbl_hinge_bottom_width_deduction_mm_even', 'bx_dbl_hinge_make_each_door_bottom_width_mm_even', 'bx_dbl_hinge_left_opening_height_mm_uneven', 'bx_dbl_hinge_height_deduction_mm_uneven_left', 'bx_dbl_hinge_make_left_door_height_mm_uneven', 'bx_dbl_hinge_left_jamb_centre_top_mm_uneven', 'bx_dbl_hinge_top_left_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_left_door_top_width_mm_uneven', 'bx_dbl_hinge_left_jamb_centre_middle_mm_uneven', 'bx_dbl_hinge_middle_left_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_left_door_middle_width_mm_uneven', 'bx_dbl_hinge_left_jamb_centre_bottom_mm_uneven', 'bx_dbl_hinge_bottom_left_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_left_door_bottom_width_mm_uneven', 'bx_dbl_hinge_right_opening_height_mm_uneven', 'bx_dbl_hinge_height_deduction_mm_uneven_right', 'bx_dbl_hinge_make_right_door_height_mm_uneven', 'bx_dbl_hinge_right_jamb_centre_top_mm_uneven', 'bx_dbl_hinge_top_right_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_right_door_top_width_mm_uneven', 'bx_dbl_hinge_right_jamb_centre_middle_mm_uneven', 'bx_dbl_hinge_middle_right_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_right_door_middle_width_mm_uneven', 'bx_dbl_hinge_right_jamb_centre_bottom_mm_uneven', 'bx_dbl_hinge_bottom_right_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_right_door_bottom_width_mm_uneven', 'bx_dbl_hinge_make_left_door_height_mm_manual', 'bx_dbl_hinge_make_left_door_top_width_mm_manual', 'bx_dbl_hinge_make_left_door_middle_width_mm_manual', 'bx_dbl_hinge_make_left_door_bottom_width_mm_manual', 'bx_dbl_hinge_make_right_door_height_mm_manual', 'bx_dbl_hinge_make_right_door_top_width_mm_manual', 'bx_dbl_hinge_make_right_door_middle_width_mm_manual', 'bx_dbl_hinge_make_right_door_bottom_width_mm_manual', 'bx_dbl_hinge_swing', 'bx_dbl_hinge_t_section_mullion_inswing', 'bx_dbl_hinge_french_door_mullion_outswing', 'bx_dbl_hinge_lock_brand', 'bx_dbl_hinge_austral_elegance_xc_lock_colour', 'bx_dbl_hinge_austral_elegance_xc_cylinder', 'bx_dbl_hinge_austral_elegance_xc_lock_height_location', 'bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lt_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lt_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_handle', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_handle', 'bx_dbl_hinge_commandex_hinged_lock_colour', 'bx_dbl_hinge_commandex_hinged_cylinder', 'bx_dbl_hinge_commandex_hinged_lock_height_location', 'bx_dbl_hinge_commandex_hinged_lh_top_cut_out', 'bx_dbl_hinge_commandex_hinged_lt_top_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_lt_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_lt_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_centre_handle', 'bx_dbl_hinge_commandex_hinged_lt_centre_handle', 'bx_dbl_hinge_lockwood_8654_lock_colour', 'bx_dbl_hinge_lockwood_8654_cylinder', 'bx_dbl_hinge_lockwood_8654_lock_height_location', 'bx_dbl_hinge_lockwood_8654_lh_top_cut_out', 'bx_dbl_hinge_lockwood_8654_lt_top_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_lt_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_lt_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_centre_handle', 'bx_dbl_hinge_lockwood_8654_lt_centre_handle', 'bx_dbl_hinge_whitco_mk2_lock_colour', 'bx_dbl_hinge_whitco_mk2_cylinder', 'bx_dbl_hinge_whitco_mk2_lock_height_location', 'bx_dbl_hinge_whitco_mk2_lh_top_cut_out', 'bx_dbl_hinge_whitco_mk2_lt_top_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_lt_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_lt_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_centre_handle', 'bx_dbl_hinge_whitco_mk2_lt_centre_handle', 'bx_dbl_hinge_whitco_tasman_escape_lock_colour', 'bx_dbl_hinge_whitco_tasman_escape_cylinder', 'bx_dbl_hinge_whitco_tasman_escape_lock_height_location', 'bx_dbl_hinge_whitco_tasman_escape_lh_top_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lt_top_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_bottom_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lt_bottom_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_handle', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_handle', 'bx_dbl_hinge_austral_elegance_xc_co_cylinder', 'bx_dbl_hinge_austral_elegance_xc_co_lock_height_location', 'bx_dbl_hinge_austral_elegance_xc_co_lh_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lt_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lt_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_handle', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_handle', 'bx_dbl_hinge_commandex_hinged_co_cylinder', 'bx_dbl_hinge_commandex_hinged_co_lock_height_location', 'bx_dbl_hinge_commandex_hinged_co_lh_top_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lt_top_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lt_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_handle', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_handle', 'bx_dbl_hinge_lockwood_8654_co_cylinder', 'bx_dbl_hinge_lockwood_8654_co_lock_height_location', 'bx_dbl_hinge_lockwood_8654_co_lh_top_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lt_top_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lt_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_handle', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_handle', 'bx_dbl_hinge_whitco_mk2_co_cylinder', 'bx_dbl_hinge_whitco_mk2_co_lock_height_location', 'bx_dbl_hinge_whitco_mk2_co_lh_top_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lt_top_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lt_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_handle', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_handle', 'bx_dbl_hinge_lock_cut_out_side_dd', 'bx_dbl_hinge_non_lock_door_striker_cutouts', 'bx_dbl_hinge_non_lock_door_bolts', 'bx_dbl_hinge_patio_bolt_colour_non_lock', 'bx_dbl_hinge_midrail_case1', 'bx_dbl_hinge_midrail_height_mm_case1', 'bx_dbl_hinge_midrail_colour_std_case1', 'bx_dbl_hinge_midrail_colour_special_case1', 'bx_dbl_hinge_midrail_case2', 'bx_dbl_hinge_midrail_height_mm_case2', 'bx_dbl_hinge_midrail_colour_std_case2', 'bx_dbl_hinge_midrail_colour_special_case2', 'bx_dbl_hinge_num_sec_hinges_pickup', 'bx_dbl_hinge_num_sec_hinges_deliver', 'bx_dbl_hinge_spec_hinge_loc_2_pickup_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_pickup', 'bx_dbl_hinge_spec_hinge_loc_3_pickup_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_pickup', 'bx_dbl_hinge_spec_hinge_loc_4_pickup_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_pickup', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_pickup', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_pickup', 'bx_dbl_hinge_spec_hinge_loc_2_deliver_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_deliver', 'bx_dbl_hinge_spec_hinge_loc_3_deliver_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_deliver', 'bx_dbl_hinge_spec_hinge_loc_4_deliver_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_deliver', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_deliver', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_deliver', 'bx_dbl_hinge_sec_hinge_type', 'bx_dbl_hinge_sec_hinge_colour_std', 'bx_dbl_hinge_sec_hinge_colour_prong', 'bx_dbl_hinge_sec_hinge_colour_step', 'bx_dbl_hinge_sec_hinge_packers_qty', 'bx_dbl_hinge_closer', 'bx_dbl_hinge_closer_colour_austral', 'bx_dbl_hinge_closer_colour_whitco', 'bx_dbl_hinge_pet_door', 'bx_dbl_hinge_pet_door_colour', 'bx_dbl_hinge_pet_door_location', 'bx_dbl_hinge_pet_door_flexible_flap', 'bx_dbl_hinge_pet_door_surround_infill', 'bx_dbl_hinge_bug_seal_size', 'bx_dbl_hinge_left_bug_seal_length_mm', 'bx_dbl_hinge_right_bug_seal_length_mm', 'bx_dbl_hinge_stop_bead_colour', 'bx_dbl_hinge_stop_bead_left_length_mm', 'bx_dbl_hinge_stop_bead_top_length_mm', 'bx_dbl_hinge_stop_bead_right_length_mm', 'bx_dbl_hinge_adhesive_mohair', 'bx_dbl_hinge_adhesive_mohair_length_mm', 'bx_dbl_hinge_jamb_adaptor_type_case1', 'bx_dbl_hinge_jamb_adaptor_type_case2', 'bx_dbl_hinge_jamb_adaptor_type_case3', 'bx_dbl_hinge_jamb_opening_left_height_mm', 'bx_dbl_hinge_jamb_height_addition_mm', 'bx_dbl_hinge_jamb_adaptor_left_length_mm', 'bx_dbl_hinge_jamb_opening_top_width_mm', 'bx_dbl_hinge_jamb_width_addition_mm', 'bx_dbl_hinge_jamb_adaptor_top_length_mm', 'bx_dbl_hinge_jamb_opening_right_height_mm', 'bx_dbl_hinge_jamb_adaptor_right_length_mm', '_CALCULATED_deduction_assistance', '_CALCULATED_door_split_type', '_CALCULATED_lock_height', '_CALCULATED_manual_left_height', '_CALCULATED_manual_right_height', '_CALCULATED_height_calculation_method', '_CALCULATED_is_even_split', '_CALCULATED_is_manual_mode', '_CALCULATED_is_uneven_split', '_CALCULATED_largest_door_height', '_CALCULATED_smallest_door_height', '_CALCULATED_halfway_point', '_CALCULATED_halfway_minus_79', '_CALCULATED_halfway_plus_16', '_CALCULATED_halfway_plus_32', '_CALCULATED_halfway_plus_79', '_CALCULATED_height_minus_1000', '_CALCULATED_height_minus_1003', '_CALCULATED_height_minus_1019', '_CALCULATED_height_minus_1090', '_CALCULATED_height_minus_1098', '_CALCULATED_height_minus_1137', '_CALCULATED_height_minus_1153', '_CALCULATED_height_minus_1169', '_CALCULATED_height_minus_1248', '_CALCULATED_height_minus_270', '_CALCULATED_height_minus_317', '_CALCULATED_height_minus_330', '_CALCULATED_height_minus_333', '_CALCULATED_height_minus_349', '_CALCULATED_height_minus_428', '_CALCULATED_height_minus_740', '_CALCULATED_height_minus_787', '_CALCULATED_height_minus_800', '_CALCULATED_height_minus_803', '_CALCULATED_height_minus_819', '_CALCULATED_height_minus_898', '_CALCULATED_height_minus_940', '_CALCULATED_height_minus_987', '_CALCULATED_Largest_Sum_Door_Width', '_CALCULATED_door_width', '_CALCULATED_even_bottom_width', '_CALCULATED_even_middle_width', '_CALCULATED_even_top_width', '_CALCULATED_jamb_adaptor_left_length_mm', '_CALCULATED_jamb_adaptor_top_length_mm', '_CALCULATED_largest_door_width', '_CALCULATED_largest_left_door_width', '_CALCULATED_largest_right_door_width', '_CALCULATED_left_Clamp_Product', '_CALCULATED_left_door_middle_width', '_CALCULATED_left_height_door', '_CALCULATED_manual_formula', '_CALCULATED_right_Clamp_Product', '_CALCULATED_right_door_middle_width', '_CALCULATED_right_height_door', '_CALCULATED_smallest_door_width', '_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_mesh_height', '_CALCULATED_midrail_case1', '_CALCULATED_mesh_series', '_CALCULATED_midrail_case2', '_CALCULATED_midrail_height', '_CALCULATED_mesh_area', '_CALCULATED_mesh_operation_required', '_CALCULATED_mesh_operation_type', '_CALCULATED_mesh_area_m2', '_CALCULATED_mesh_perimeter', '_CALCULATED_mesh_aspect_ratio', '_CALCULATED_mesh_size_category'] 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Found quantity multiplier: 1.0 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Using field/option mapping calculation (no config_id) 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] WARNING: This may result in different operation costs than save config 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Returning 50 operations with total cost 221.871536 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COST_METRICS] {'config_id': None, 'operation_count': 50, 'total_cost': 221.87, 'calculation_method': 'field_option_mapping', 'timestamp': 'unknown'} 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COST_METRICS] WARNING: Using field/option mapping - may differ from save config 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 3_per_door_attached, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Calculating operation costs for template 24 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Received field_values: {'bx_dbl_hinge_location': '', 'bx_dbl_hinge_location_other': '', 'bx_dbl_hinge_jamb_reveal_gt_20mm': '', 'bx_dbl_hinge_screen_clears_handle': '', 'bx_dbl_hinge_new_handle_clears_existing': '', 'bx_dbl_hinge_swing_path_clear': '', 'bx_dbl_hinge_obstruction_description': '', 'bx_dbl_hinge_quantity': '1', 'bx_dbl_hinge_frame_colour': 'black_custom_matt_gn248a', 'bx_dbl_hinge_powder_coat_colour': '', 'bx_dbl_hinge_custom_powder_coat_name': '', 'bx_dbl_hinge_custom_powder_coat_finish': '', 'bx_dbl_hinge_custom_powder_coat_code': '', 'bx_dbl_hinge_deduction_assistance': 'no', 'bx_dbl_hinge_door_split_type': 'even', 'bx_dbl_hinge_opening_height_mm_even': '', 'bx_dbl_hinge_height_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_height_mm_even': '', 'bx_dbl_hinge_opening_top_width_mm_even': '', 'bx_dbl_hinge_top_width_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_top_width_mm_even': '', 'bx_dbl_hinge_opening_middle_width_mm_even': '', 'bx_dbl_hinge_middle_width_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_middle_width_mm_even': '', 'bx_dbl_hinge_opening_bottom_width_mm_even': '', 'bx_dbl_hinge_bottom_width_deduction_mm_even': '', 'bx_dbl_hinge_make_each_door_bottom_width_mm_even': '', 'bx_dbl_hinge_left_opening_height_mm_uneven': '', 'bx_dbl_hinge_height_deduction_mm_uneven_left': '', 'bx_dbl_hinge_make_left_door_height_mm_uneven': '', 'bx_dbl_hinge_left_jamb_centre_top_mm_uneven': '', 'bx_dbl_hinge_top_left_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_left_door_top_width_mm_uneven': '', 'bx_dbl_hinge_left_jamb_centre_middle_mm_uneven': '', 'bx_dbl_hinge_middle_left_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_left_door_middle_width_mm_uneven': '', 'bx_dbl_hinge_left_jamb_centre_bottom_mm_uneven': '', 'bx_dbl_hinge_bottom_left_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_left_door_bottom_width_mm_uneven': '', 'bx_dbl_hinge_right_opening_height_mm_uneven': '', 'bx_dbl_hinge_height_deduction_mm_uneven_right': '', 'bx_dbl_hinge_make_right_door_height_mm_uneven': '', 'bx_dbl_hinge_right_jamb_centre_top_mm_uneven': '', 'bx_dbl_hinge_top_right_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_right_door_top_width_mm_uneven': '', 'bx_dbl_hinge_right_jamb_centre_middle_mm_uneven': '', 'bx_dbl_hinge_middle_right_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_right_door_middle_width_mm_uneven': '', 'bx_dbl_hinge_right_jamb_centre_bottom_mm_uneven': '', 'bx_dbl_hinge_bottom_right_jamb_centre_deduction_mm_uneven': '', 'bx_dbl_hinge_make_right_door_bottom_width_mm_uneven': '', 'bx_dbl_hinge_make_left_door_height_mm_manual': '2110', 'bx_dbl_hinge_make_left_door_top_width_mm_manual': '860', 'bx_dbl_hinge_make_left_door_middle_width_mm_manual': '860', 'bx_dbl_hinge_make_left_door_bottom_width_mm_manual': '860', 'bx_dbl_hinge_make_right_door_height_mm_manual': '2110', 'bx_dbl_hinge_make_right_door_top_width_mm_manual': '860', 'bx_dbl_hinge_make_right_door_middle_width_mm_manual': '860', 'bx_dbl_hinge_make_right_door_bottom_width_mm_manual': '860', 'bx_dbl_hinge_swing': 'outswing', 'bx_dbl_hinge_t_section_mullion_inswing': 'yes', 'bx_dbl_hinge_french_door_mullion_outswing': 'yes', 'bx_dbl_hinge_lock_brand': 'commandex_hinged', 'bx_dbl_hinge_austral_elegance_xc_lock_colour': 'black', 'bx_dbl_hinge_austral_elegance_xc_cylinder': 'austral_5_pin_dbl', 'bx_dbl_hinge_austral_elegance_xc_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_lt_top_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_lh_bottom_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_handle': '', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_handle': 'single', 'bx_dbl_hinge_commandex_hinged_lock_colour': 'black', 'bx_dbl_hinge_commandex_hinged_cylinder': 'commandex_5_pin', 'bx_dbl_hinge_commandex_hinged_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_top_cut_out': '', 'bx_dbl_hinge_commandex_hinged_lt_top_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_lh_centre_cut_out': '', 'bx_dbl_hinge_commandex_hinged_lt_centre_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_lh_bottom_cut_out': '', 'bx_dbl_hinge_commandex_hinged_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_lh_centre_handle': '', 'bx_dbl_hinge_commandex_hinged_lt_centre_handle': 'single', 'bx_dbl_hinge_lockwood_8654_lock_colour': 'black', 'bx_dbl_hinge_lockwood_8654_cylinder': 'whitco_5_pin', 'bx_dbl_hinge_lockwood_8654_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_top_cut_out': '', 'bx_dbl_hinge_lockwood_8654_lt_top_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_lh_centre_cut_out': '', 'bx_dbl_hinge_lockwood_8654_lt_centre_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_lh_bottom_cut_out': '', 'bx_dbl_hinge_lockwood_8654_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_lh_centre_handle': '', 'bx_dbl_hinge_lockwood_8654_lt_centre_handle': 'single', 'bx_dbl_hinge_whitco_mk2_lock_colour': 'black', 'bx_dbl_hinge_whitco_mk2_cylinder': 'whitco_5_pin', 'bx_dbl_hinge_whitco_mk2_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_top_cut_out': '', 'bx_dbl_hinge_whitco_mk2_lt_top_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_lh_centre_cut_out': '', 'bx_dbl_hinge_whitco_mk2_lt_centre_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_lh_bottom_cut_out': '', 'bx_dbl_hinge_whitco_mk2_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_lh_centre_handle': '', 'bx_dbl_hinge_whitco_mk2_lt_centre_handle': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lock_colour': 'black', 'bx_dbl_hinge_whitco_tasman_escape_cylinder': 'whitco_escape_cylinder_turn_knob', 'bx_dbl_hinge_whitco_tasman_escape_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_top_cut_out': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_top_cut_out': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_cut_out': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_cut_out': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lh_bottom_cut_out': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_handle': '', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_handle': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_austral_elegance_xc_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_top_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_handle': '', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_handle': 'single', 'bx_dbl_hinge_commandex_hinged_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_commandex_hinged_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_top_cut_out': '', 'bx_dbl_hinge_commandex_hinged_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_commandex_hinged_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_cut_out': '', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_handle': '', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_handle': 'single', 'bx_dbl_hinge_lockwood_8654_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_lockwood_8654_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_top_cut_out': '', 'bx_dbl_hinge_lockwood_8654_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_cut_out': '', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_lockwood_8654_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_handle': '', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_handle': 'single', 'bx_dbl_hinge_whitco_mk2_co_cylinder': 'no_cylinder', 'bx_dbl_hinge_whitco_mk2_co_lock_height_location': 'top_of_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_top_cut_out': '', 'bx_dbl_hinge_whitco_mk2_co_lt_top_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_cut_out': '', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_co_lh_bottom_cut_out': '', 'bx_dbl_hinge_whitco_mk2_co_lt_bottom_cut_out': 'single', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_handle': '', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_handle': 'single', 'bx_dbl_hinge_lock_cut_out_side_dd': 'left_hand_door_right_hand_lock', 'bx_dbl_hinge_non_lock_door_striker_cutouts': 'yes', 'bx_dbl_hinge_non_lock_door_bolts': 'top_bottom_flush_bolts', 'bx_dbl_hinge_patio_bolt_colour_non_lock': 'black', 'bx_dbl_hinge_midrail_case1': 'yes', 'bx_dbl_hinge_midrail_height_mm_case1': '', 'bx_dbl_hinge_midrail_colour_std_case1': '', 'bx_dbl_hinge_midrail_colour_special_case1': 'black', 'bx_dbl_hinge_midrail_case2': 'yes', 'bx_dbl_hinge_midrail_height_mm_case2': '', 'bx_dbl_hinge_midrail_colour_std_case2': 'black', 'bx_dbl_hinge_midrail_colour_special_case2': 'black', 'bx_dbl_hinge_num_sec_hinges_pickup': '0', 'bx_dbl_hinge_num_sec_hinges_deliver': '3_per_door_loose_drilled', 'bx_dbl_hinge_spec_hinge_loc_2_pickup_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_pickup': '', 'bx_dbl_hinge_spec_hinge_loc_3_pickup_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_pickup': '', 'bx_dbl_hinge_spec_hinge_loc_4_pickup_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_pickup': '', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_pickup': '', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_pickup': '', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_pickup': '', 'bx_dbl_hinge_spec_hinge_loc_2_deliver_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_deliver': '', 'bx_dbl_hinge_spec_hinge_loc_3_deliver_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_deliver': '', 'bx_dbl_hinge_spec_hinge_loc_4_deliver_dd': 'no', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_deliver': '', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_deliver': '', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_deliver': '', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_deliver': '', 'bx_dbl_hinge_sec_hinge_type': 'security_hinge', 'bx_dbl_hinge_sec_hinge_colour_std': 'black', 'bx_dbl_hinge_sec_hinge_colour_prong': 'black', 'bx_dbl_hinge_sec_hinge_colour_step': 'black', 'bx_dbl_hinge_sec_hinge_packers_qty': '0', 'bx_dbl_hinge_closer': 'no', 'bx_dbl_hinge_closer_colour_austral': 'black', 'bx_dbl_hinge_closer_colour_whitco': 'black', 'bx_dbl_hinge_pet_door': 'no', 'bx_dbl_hinge_pet_door_colour': 'black', 'bx_dbl_hinge_pet_door_location': 'lock_door_lock_side', 'bx_dbl_hinge_pet_door_flexible_flap': 'no', 'bx_dbl_hinge_pet_door_surround_infill': 'no', 'bx_dbl_hinge_bug_seal_size': 'no', 'bx_dbl_hinge_left_bug_seal_length_mm': '', 'bx_dbl_hinge_right_bug_seal_length_mm': '', 'bx_dbl_hinge_stop_bead_colour': 'no', 'bx_dbl_hinge_stop_bead_left_length_mm': '', 'bx_dbl_hinge_stop_bead_top_length_mm': '', 'bx_dbl_hinge_stop_bead_right_length_mm': '', 'bx_dbl_hinge_adhesive_mohair': 'no', 'bx_dbl_hinge_adhesive_mohair_length_mm': '', 'bx_dbl_hinge_jamb_adaptor_type_case1': 'no', 'bx_dbl_hinge_jamb_adaptor_type_case2': '', 'bx_dbl_hinge_jamb_adaptor_type_case3': '', 'bx_dbl_hinge_jamb_opening_left_height_mm': '', 'bx_dbl_hinge_jamb_height_addition_mm': '20', 'bx_dbl_hinge_jamb_adaptor_left_length_mm': '', 'bx_dbl_hinge_jamb_opening_top_width_mm': '', 'bx_dbl_hinge_jamb_width_addition_mm': '40', 'bx_dbl_hinge_jamb_adaptor_top_length_mm': '', 'bx_dbl_hinge_jamb_opening_right_height_mm': '', 'bx_dbl_hinge_jamb_adaptor_right_length_mm': '', '_CALCULATED_deduction_assistance': 'no', '_CALCULATED_door_split_type': 'even', '_CALCULATED_lock_height': 0, '_CALCULATED_manual_left_height': 2110, '_CALCULATED_manual_right_height': 0, '_CALCULATED_height_calculation_method': 'manual', '_CALCULATED_is_even_split': True, '_CALCULATED_is_manual_mode': True, '_CALCULATED_is_uneven_split': False, '_CALCULATED_largest_door_height': 2110, '_CALCULATED_smallest_door_height': 20, '_CALCULATED_halfway_point': 0, '_CALCULATED_halfway_minus_79': -79, '_CALCULATED_halfway_plus_16': 16, '_CALCULATED_halfway_plus_32': 32, '_CALCULATED_halfway_plus_79': 79, '_CALCULATED_height_minus_1000': -1000, '_CALCULATED_height_minus_1003': -1003, '_CALCULATED_height_minus_1019': -1019, '_CALCULATED_height_minus_1090': -1090, '_CALCULATED_height_minus_1098': -1098, '_CALCULATED_height_minus_1137': -1137, '_CALCULATED_height_minus_1153': -1153, '_CALCULATED_height_minus_1169': -1169, '_CALCULATED_height_minus_1248': -1248, '_CALCULATED_height_minus_270': -270, '_CALCULATED_height_minus_317': -317, '_CALCULATED_height_minus_330': -330, '_CALCULATED_height_minus_333': -333, '_CALCULATED_height_minus_349': -349, '_CALCULATED_height_minus_428': -428, '_CALCULATED_height_minus_740': -740, '_CALCULATED_height_minus_787': -787, '_CALCULATED_height_minus_800': -800, '_CALCULATED_height_minus_803': -803, '_CALCULATED_height_minus_819': -819, '_CALCULATED_height_minus_898': -898, '_CALCULATED_height_minus_940': -940, '_CALCULATED_height_minus_987': -987, '_CALCULATED_Largest_Sum_Door_Width': 430, '_CALCULATED_door_width': 1720, '_CALCULATED_even_bottom_width': 0, '_CALCULATED_even_middle_width': 0, '_CALCULATED_even_top_width': 0, '_CALCULATED_jamb_adaptor_left_length_mm': 20, '_CALCULATED_jamb_adaptor_top_length_mm': 40, '_CALCULATED_largest_door_width': 860, '_CALCULATED_largest_left_door_width': 860, '_CALCULATED_largest_right_door_width': 0, '_CALCULATED_left_Clamp_Product': 'yes', '_CALCULATED_left_door_middle_width': 860, '_CALCULATED_left_height_door': 2110, '_CALCULATED_manual_formula': 'min(2110.0, 0) = 0', '_CALCULATED_right_Clamp_Product': 'yes', '_CALCULATED_right_door_middle_width': 0, '_CALCULATED_right_height_door': 0, '_CALCULATED_smallest_door_width': 0, '_CALCULATED_mesh_required': True, '_CALCULATED_mesh_width': 860, '_CALCULATED_mesh_height': 2110, '_CALCULATED_midrail_case1': False, '_CALCULATED_mesh_series': 'basix', '_CALCULATED_midrail_case2': False, '_CALCULATED_midrail_height': 0, '_CALCULATED_mesh_area': 1814600, '_CALCULATED_mesh_operation_required': True, '_CALCULATED_mesh_operation_type': None, '_CALCULATED_mesh_area_m2': 1.8146, '_CALCULATED_mesh_perimeter': 5940, '_CALCULATED_mesh_aspect_ratio': 0.4075829383886256, '_CALCULATED_mesh_size_category': None} 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Field values keys: ['bx_dbl_hinge_location', 'bx_dbl_hinge_location_other', 'bx_dbl_hinge_jamb_reveal_gt_20mm', 'bx_dbl_hinge_screen_clears_handle', 'bx_dbl_hinge_new_handle_clears_existing', 'bx_dbl_hinge_swing_path_clear', 'bx_dbl_hinge_obstruction_description', 'bx_dbl_hinge_quantity', 'bx_dbl_hinge_frame_colour', 'bx_dbl_hinge_powder_coat_colour', 'bx_dbl_hinge_custom_powder_coat_name', 'bx_dbl_hinge_custom_powder_coat_finish', 'bx_dbl_hinge_custom_powder_coat_code', 'bx_dbl_hinge_deduction_assistance', 'bx_dbl_hinge_door_split_type', 'bx_dbl_hinge_opening_height_mm_even', 'bx_dbl_hinge_height_deduction_mm_even', 'bx_dbl_hinge_make_each_door_height_mm_even', 'bx_dbl_hinge_opening_top_width_mm_even', 'bx_dbl_hinge_top_width_deduction_mm_even', 'bx_dbl_hinge_make_each_door_top_width_mm_even', 'bx_dbl_hinge_opening_middle_width_mm_even', 'bx_dbl_hinge_middle_width_deduction_mm_even', 'bx_dbl_hinge_make_each_door_middle_width_mm_even', 'bx_dbl_hinge_opening_bottom_width_mm_even', 'bx_dbl_hinge_bottom_width_deduction_mm_even', 'bx_dbl_hinge_make_each_door_bottom_width_mm_even', 'bx_dbl_hinge_left_opening_height_mm_uneven', 'bx_dbl_hinge_height_deduction_mm_uneven_left', 'bx_dbl_hinge_make_left_door_height_mm_uneven', 'bx_dbl_hinge_left_jamb_centre_top_mm_uneven', 'bx_dbl_hinge_top_left_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_left_door_top_width_mm_uneven', 'bx_dbl_hinge_left_jamb_centre_middle_mm_uneven', 'bx_dbl_hinge_middle_left_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_left_door_middle_width_mm_uneven', 'bx_dbl_hinge_left_jamb_centre_bottom_mm_uneven', 'bx_dbl_hinge_bottom_left_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_left_door_bottom_width_mm_uneven', 'bx_dbl_hinge_right_opening_height_mm_uneven', 'bx_dbl_hinge_height_deduction_mm_uneven_right', 'bx_dbl_hinge_make_right_door_height_mm_uneven', 'bx_dbl_hinge_right_jamb_centre_top_mm_uneven', 'bx_dbl_hinge_top_right_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_right_door_top_width_mm_uneven', 'bx_dbl_hinge_right_jamb_centre_middle_mm_uneven', 'bx_dbl_hinge_middle_right_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_right_door_middle_width_mm_uneven', 'bx_dbl_hinge_right_jamb_centre_bottom_mm_uneven', 'bx_dbl_hinge_bottom_right_jamb_centre_deduction_mm_uneven', 'bx_dbl_hinge_make_right_door_bottom_width_mm_uneven', 'bx_dbl_hinge_make_left_door_height_mm_manual', 'bx_dbl_hinge_make_left_door_top_width_mm_manual', 'bx_dbl_hinge_make_left_door_middle_width_mm_manual', 'bx_dbl_hinge_make_left_door_bottom_width_mm_manual', 'bx_dbl_hinge_make_right_door_height_mm_manual', 'bx_dbl_hinge_make_right_door_top_width_mm_manual', 'bx_dbl_hinge_make_right_door_middle_width_mm_manual', 'bx_dbl_hinge_make_right_door_bottom_width_mm_manual', 'bx_dbl_hinge_swing', 'bx_dbl_hinge_t_section_mullion_inswing', 'bx_dbl_hinge_french_door_mullion_outswing', 'bx_dbl_hinge_lock_brand', 'bx_dbl_hinge_austral_elegance_xc_lock_colour', 'bx_dbl_hinge_austral_elegance_xc_cylinder', 'bx_dbl_hinge_austral_elegance_xc_lock_height_location', 'bx_dbl_hinge_austral_elegance_xc_lh_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lt_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lt_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_lh_centre_handle', 'bx_dbl_hinge_austral_elegance_xc_lt_centre_handle', 'bx_dbl_hinge_commandex_hinged_lock_colour', 'bx_dbl_hinge_commandex_hinged_cylinder', 'bx_dbl_hinge_commandex_hinged_lock_height_location', 'bx_dbl_hinge_commandex_hinged_lh_top_cut_out', 'bx_dbl_hinge_commandex_hinged_lt_top_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_lt_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_lt_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_lh_centre_handle', 'bx_dbl_hinge_commandex_hinged_lt_centre_handle', 'bx_dbl_hinge_lockwood_8654_lock_colour', 'bx_dbl_hinge_lockwood_8654_cylinder', 'bx_dbl_hinge_lockwood_8654_lock_height_location', 'bx_dbl_hinge_lockwood_8654_lh_top_cut_out', 'bx_dbl_hinge_lockwood_8654_lt_top_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_lt_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_lt_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_lh_centre_handle', 'bx_dbl_hinge_lockwood_8654_lt_centre_handle', 'bx_dbl_hinge_whitco_mk2_lock_colour', 'bx_dbl_hinge_whitco_mk2_cylinder', 'bx_dbl_hinge_whitco_mk2_lock_height_location', 'bx_dbl_hinge_whitco_mk2_lh_top_cut_out', 'bx_dbl_hinge_whitco_mk2_lt_top_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_lt_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_lt_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_lh_centre_handle', 'bx_dbl_hinge_whitco_mk2_lt_centre_handle', 'bx_dbl_hinge_whitco_tasman_escape_lock_colour', 'bx_dbl_hinge_whitco_tasman_escape_cylinder', 'bx_dbl_hinge_whitco_tasman_escape_lock_height_location', 'bx_dbl_hinge_whitco_tasman_escape_lh_top_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lt_top_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_bottom_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lt_bottom_cut_out', 'bx_dbl_hinge_whitco_tasman_escape_lh_centre_handle', 'bx_dbl_hinge_whitco_tasman_escape_lt_centre_handle', 'bx_dbl_hinge_austral_elegance_xc_co_cylinder', 'bx_dbl_hinge_austral_elegance_xc_co_lock_height_location', 'bx_dbl_hinge_austral_elegance_xc_co_lh_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lt_top_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lt_bottom_cut_out', 'bx_dbl_hinge_austral_elegance_xc_co_lh_centre_handle', 'bx_dbl_hinge_austral_elegance_xc_co_lt_centre_handle', 'bx_dbl_hinge_commandex_hinged_co_cylinder', 'bx_dbl_hinge_commandex_hinged_co_lock_height_location', 'bx_dbl_hinge_commandex_hinged_co_lh_top_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lt_top_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lt_bottom_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_cut_out', 'bx_dbl_hinge_commandex_hinged_co_lh_centre_handle', 'bx_dbl_hinge_commandex_hinged_co_lt_centre_handle', 'bx_dbl_hinge_lockwood_8654_co_cylinder', 'bx_dbl_hinge_lockwood_8654_co_lock_height_location', 'bx_dbl_hinge_lockwood_8654_co_lh_top_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lt_top_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lt_bottom_cut_out', 'bx_dbl_hinge_lockwood_8654_co_lh_centre_handle', 'bx_dbl_hinge_lockwood_8654_co_lt_centre_handle', 'bx_dbl_hinge_whitco_mk2_co_cylinder', 'bx_dbl_hinge_whitco_mk2_co_lock_height_location', 'bx_dbl_hinge_whitco_mk2_co_lh_top_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lt_top_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lt_bottom_cut_out', 'bx_dbl_hinge_whitco_mk2_co_lh_centre_handle', 'bx_dbl_hinge_whitco_mk2_co_lt_centre_handle', 'bx_dbl_hinge_lock_cut_out_side_dd', 'bx_dbl_hinge_non_lock_door_striker_cutouts', 'bx_dbl_hinge_non_lock_door_bolts', 'bx_dbl_hinge_patio_bolt_colour_non_lock', 'bx_dbl_hinge_midrail_case1', 'bx_dbl_hinge_midrail_height_mm_case1', 'bx_dbl_hinge_midrail_colour_std_case1', 'bx_dbl_hinge_midrail_colour_special_case1', 'bx_dbl_hinge_midrail_case2', 'bx_dbl_hinge_midrail_height_mm_case2', 'bx_dbl_hinge_midrail_colour_std_case2', 'bx_dbl_hinge_midrail_colour_special_case2', 'bx_dbl_hinge_num_sec_hinges_pickup', 'bx_dbl_hinge_num_sec_hinges_deliver', 'bx_dbl_hinge_spec_hinge_loc_2_pickup_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_pickup', 'bx_dbl_hinge_spec_hinge_loc_3_pickup_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_pickup', 'bx_dbl_hinge_spec_hinge_loc_4_pickup_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_pickup', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_pickup', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_pickup', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_pickup', 'bx_dbl_hinge_spec_hinge_loc_2_deliver_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_2_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_top_2_hinges_deliver', 'bx_dbl_hinge_spec_hinge_loc_3_deliver_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_3_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_middle_3_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_top_3_hinges_deliver', 'bx_dbl_hinge_spec_hinge_loc_4_deliver_dd', 'bx_dbl_hinge_bottom_to_centre_bottom_4_hinges_deliver', 'bx_dbl_hinge_second_hinge_bottom_to_centre_4_hinges_deliver', 'bx_dbl_hinge_third_hinge_bottom_to_centre_4_hinges_deliver', 'bx_dbl_hinge_bottom_to_centre_top_4_hinges_deliver', 'bx_dbl_hinge_sec_hinge_type', 'bx_dbl_hinge_sec_hinge_colour_std', 'bx_dbl_hinge_sec_hinge_colour_prong', 'bx_dbl_hinge_sec_hinge_colour_step', 'bx_dbl_hinge_sec_hinge_packers_qty', 'bx_dbl_hinge_closer', 'bx_dbl_hinge_closer_colour_austral', 'bx_dbl_hinge_closer_colour_whitco', 'bx_dbl_hinge_pet_door', 'bx_dbl_hinge_pet_door_colour', 'bx_dbl_hinge_pet_door_location', 'bx_dbl_hinge_pet_door_flexible_flap', 'bx_dbl_hinge_pet_door_surround_infill', 'bx_dbl_hinge_bug_seal_size', 'bx_dbl_hinge_left_bug_seal_length_mm', 'bx_dbl_hinge_right_bug_seal_length_mm', 'bx_dbl_hinge_stop_bead_colour', 'bx_dbl_hinge_stop_bead_left_length_mm', 'bx_dbl_hinge_stop_bead_top_length_mm', 'bx_dbl_hinge_stop_bead_right_length_mm', 'bx_dbl_hinge_adhesive_mohair', 'bx_dbl_hinge_adhesive_mohair_length_mm', 'bx_dbl_hinge_jamb_adaptor_type_case1', 'bx_dbl_hinge_jamb_adaptor_type_case2', 'bx_dbl_hinge_jamb_adaptor_type_case3', 'bx_dbl_hinge_jamb_opening_left_height_mm', 'bx_dbl_hinge_jamb_height_addition_mm', 'bx_dbl_hinge_jamb_adaptor_left_length_mm', 'bx_dbl_hinge_jamb_opening_top_width_mm', 'bx_dbl_hinge_jamb_width_addition_mm', 'bx_dbl_hinge_jamb_adaptor_top_length_mm', 'bx_dbl_hinge_jamb_opening_right_height_mm', 'bx_dbl_hinge_jamb_adaptor_right_length_mm', '_CALCULATED_deduction_assistance', '_CALCULATED_door_split_type', '_CALCULATED_lock_height', '_CALCULATED_manual_left_height', '_CALCULATED_manual_right_height', '_CALCULATED_height_calculation_method', '_CALCULATED_is_even_split', '_CALCULATED_is_manual_mode', '_CALCULATED_is_uneven_split', '_CALCULATED_largest_door_height', '_CALCULATED_smallest_door_height', '_CALCULATED_halfway_point', '_CALCULATED_halfway_minus_79', '_CALCULATED_halfway_plus_16', '_CALCULATED_halfway_plus_32', '_CALCULATED_halfway_plus_79', '_CALCULATED_height_minus_1000', '_CALCULATED_height_minus_1003', '_CALCULATED_height_minus_1019', '_CALCULATED_height_minus_1090', '_CALCULATED_height_minus_1098', '_CALCULATED_height_minus_1137', '_CALCULATED_height_minus_1153', '_CALCULATED_height_minus_1169', '_CALCULATED_height_minus_1248', '_CALCULATED_height_minus_270', '_CALCULATED_height_minus_317', '_CALCULATED_height_minus_330', '_CALCULATED_height_minus_333', '_CALCULATED_height_minus_349', '_CALCULATED_height_minus_428', '_CALCULATED_height_minus_740', '_CALCULATED_height_minus_787', '_CALCULATED_height_minus_800', '_CALCULATED_height_minus_803', '_CALCULATED_height_minus_819', '_CALCULATED_height_minus_898', '_CALCULATED_height_minus_940', '_CALCULATED_height_minus_987', '_CALCULATED_Largest_Sum_Door_Width', '_CALCULATED_door_width', '_CALCULATED_even_bottom_width', '_CALCULATED_even_middle_width', '_CALCULATED_even_top_width', '_CALCULATED_jamb_adaptor_left_length_mm', '_CALCULATED_jamb_adaptor_top_length_mm', '_CALCULATED_largest_door_width', '_CALCULATED_largest_left_door_width', '_CALCULATED_largest_right_door_width', '_CALCULATED_left_Clamp_Product', '_CALCULATED_left_door_middle_width', '_CALCULATED_left_height_door', '_CALCULATED_manual_formula', '_CALCULATED_right_Clamp_Product', '_CALCULATED_right_door_middle_width', '_CALCULATED_right_height_door', '_CALCULATED_smallest_door_width', '_CALCULATED_mesh_required', '_CALCULATED_mesh_width', '_CALCULATED_mesh_height', '_CALCULATED_midrail_case1', '_CALCULATED_mesh_series', '_CALCULATED_midrail_case2', '_CALCULATED_midrail_height', '_CALCULATED_mesh_area', '_CALCULATED_mesh_operation_required', '_CALCULATED_mesh_operation_type', '_CALCULATED_mesh_area_m2', '_CALCULATED_mesh_perimeter', '_CALCULATED_mesh_aspect_ratio', '_CALCULATED_mesh_size_category'] 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Found quantity multiplier: 1.0 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Using field/option mapping calculation (no config_id) 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] WARNING: This may result in different operation costs than save config 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COSTS] Returning 44 operations with total cost 203.631536 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COST_METRICS] {'config_id': None, 'operation_count': 44, 'total_cost': 203.63, 'calculation_method': 'field_option_mapping', 'timestamp': 'unknown'} 
odoo.addons.canbrax_configmatrix.controllers.configuration_controller: [OPERATION_COST_METRICS] WARNING: Using field/option mapping - may differ from save config 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] Received parameters: ['template_id', 'product_id', 'order_line_id', 'config_data', 'configuration_price', 'configuration_price_matrix', 'total_price', 'field_3451', 'field_3452', 'field_3453', 'field_3454', 'field_3455', 'field_3456', 'field_3457', 'field_3458', 'field_3459', 'field_3460', 'field_3461', 'field_3462', 'field_3463', 'field_3464', 'field_3465', 'field_3466', 'field_3467', 'field_3468', 'field_3469', 'field_3470', 'field_3471', 'field_3472', 'field_3473', 'field_3474', 'field_3475', 'field_3476', 'field_3477', 'field_3478', 'field_3479', 'field_3480', 'field_3481', 'field_3482', 'field_3483', 'field_3484', 'field_3485', 'field_3486', 'field_3487', 'field_3488', 'field_3489', 'field_3490', 'field_3491', 'field_3492', 'field_3493', 'field_3494', 'field_3495', 'field_3496', 'field_3497', 'field_3498', 'field_3499', 'field_3500', 'field_3501', 'field_3502', 'field_3503', 'field_3504', 'field_3505', 'field_3506', 'field_3507', 'field_3508', 'field_3509', 'field_3510', 'field_3511', 'field_3512', 'field_3513', 'field_3514', 'field_3515', 'field_3516', 'field_3517', 'field_3518', 'field_3519', 'field_3520', 'field_3521', 'field_3522', 'field_3523', 'field_3524', 'field_3525', 'field_3526', 'field_3527', 'field_3528', 'field_3529', 'field_3530', 'field_3531', 'field_3532', 'field_3533', 'field_3534', 'field_3535', 'field_3536', 'field_3537', 'field_3538', 'field_3539', 'field_3540', 'field_3541', 'field_3542', 'field_3543', 'field_3544', 'field_3545', 'field_3546', 'field_3547', 'field_3548', 'field_3549', 'field_3550', 'field_3551', 'field_3552', 'field_3553', 'field_3554', 'field_3555', 'field_3556', 'field_3557', 'field_3558', 'field_3559', 'field_3560', 'field_3561', 'field_3562', 'field_3563', 'field_3564', 'field_3565', 'field_3566', 'field_3567', 'field_3568', 'field_3569', 'field_3570', 'field_3571', 'field_3572', 'field_3573', 'field_3574', 'field_3575', 'field_3576', 'field_3577', 'field_3578', 'field_3579', 'field_3580', 'field_3581', 'field_3582', 'field_3585', 'field_3586', 'field_3583', 'field_3584', 'field_3587', 'field_3588', 'field_3589', 'field_3590', 'field_3591', 'field_3592', 'field_3593', 'field_3594', 'field_3595', 'field_3596', 'field_3597', 'field_3598', 'field_3599', 'field_3600', 'field_3601', 'field_3602', 'field_3603', 'field_3604', 'field_3605', 'field_3606', 'field_3607', 'field_3608', 'field_3609', 'field_3610', 'field_3611', 'field_3612', 'field_3613', 'field_3614', 'field_3615', 'field_3616', 'field_3617', 'field_3618', 'field_3619', 'field_3620', 'field_3621', 'field_3622', 'field_3623', 'field_3624', 'field_3625', 'field_3626', 'field_3627', 'field_3628', 'field_3629', 'field_3630', 'field_3631', 'field_3632', 'field_3633', 'field_3634', 'field_3635', 'field_3636', 'field_3637', 'field_3638', 'field_3639', 'field_3640', 'field_3641', 'field_3642', 'field_3643', 'field_3644', 'field_3645', 'field_3646', 'field_3647', 'field_3648', 'field_3649', 'field_4140', 'field_3650', 'field_3651', 'field_3652', 'field_3653', 'field_3654', 'field_3655', 'field_3656', 'field_3657', 'field_3658', 'field_3661', 'field_3662', 'field_3663', 'field_3664', 'field_3665', 'field_3666', 'field_3667', 'field_3668', 'field_3669', 'field_3670', 'field_3671', 'field_3672', 'field_3673', 'field_3674', 'field_3675', 'field_3676', 'field_3677', 'field_3678', 'field_3679', 'field_3680'] 
odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] configuration_price_matrix in kw: True 
odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] configuration_price in kw: True 
odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] Extracted price_matrix: 717.74 
odoo.addons.canbrax_configmatrix.controllers.main: [SAVE_CONFIG_DEBUG] Extracted price_component: 111.92540000000001 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' has no quantity formula, returning 1.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_option_quantity(161): Option 'Top & Bottom Flush Bolts' quantity formula 'False' evaluated to 2 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(171): Found field Number of Security Hinges (Pick Up) (ID: 3621, tech_name: bx_dbl_hinge_num_sec_hinges_pickup), raw_value: 0, converted: 0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(172): Found field Number of Security Hinges (Deliver/Site) (ID: 3622, tech_name: bx_dbl_hinge_num_sec_hinges_deliver), raw_value: 3_per_door_loose_drilled, converted: 3 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
odoo.addons.canbrax_configmatrix.models.config_matrix_operation_template: [OPERATION_COSTS] get_question(201): Found field Security Hinge Packers Quantity (ID: 3650, tech_name: bx_dbl_hinge_sec_hinge_packers_qty), raw_value: 0, converted: 0.0 
