/**
 * Operation Costs Handler for Product Configurator
 * Handles the display and calculation of operation costs in the right panel
 */

class OperationCostsHandler {
    constructor() {
        this.operationCosts = [];
        this.totalCost = 0.0;
        this.templateId = null;
        this.configId = null;
        this.currentValues = {};
        this.priceMatrix = 0.0;
        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            this.init();
        }
    }

    init() {
        // Check global state for calculated fields
        this.checkGlobalCalculatedFieldsState();

        // Get template ID from the page
        this.templateId = this.getTemplateId();

        // Get config ID from the page
        this.configId = this.getConfigId();

        // Set up event listeners
        this.setupEventListeners();

        // Wait for configurator to be fully ready before initial load
        this.waitForConfiguratorReady();
    }

    waitForConfiguratorReady() {
        const maxAttempts = 10;
        let attempts = 0;

        const checkReady = () => {
            attempts++;

            // Check if configurator is ready
            const isReady = this.isConfiguratorReady();

            if (isReady) {
                this.refreshOperationCosts();
            } else if (attempts < maxAttempts) {
                // Retry after delay
                setTimeout(checkReady, 1000);
            } else {
                console.warn('[OperationCosts] Configurator not ready after maximum attempts, proceeding anyway');
                this.refreshOperationCosts();
            }
        };

        // Start checking after a short delay
        setTimeout(checkReady, 500);
    }

    isConfiguratorReady() {
        const hasCalculatedFields = window.calculatedFieldsDefinitions &&
            window.calculatedFieldsDefinitions.length > 0;

        const hasCalculateFunction = typeof window.calculateDynamicFields === 'function';

        const hasFieldValues = document.querySelectorAll('.config-field').length > 0;

        const configuratorLoaded = typeof window.configurator !== 'undefined' ||
            typeof window.configMatrix !== 'undefined';

        return hasCalculateFunction && hasFieldValues;
    }

    checkGlobalCalculatedFieldsState() {
        const hasCalculateFunction = typeof window.calculateDynamicFields === 'function';
        const hasCalculatedFields = window.calculatedFieldsDefinitions &&
            window.calculatedFieldsDefinitions.length > 0;

        if (!hasCalculateFunction) {
            console.warn('[OperationCosts] Global calculateDynamicFields function not available');
        }

        if (!hasCalculatedFields) {
            console.warn('[OperationCosts] No calculated fields definitions found');
        }
    }

    getTemplateId() {
        const templateInput = document.querySelector('input[name="template_id"]');
        if (templateInput) {
            return parseInt(templateInput.value);
        }
        
        const urlParams = new URLSearchParams(window.location.search);
        const templateId = urlParams.get('template_id') || urlParams.get('matrix_id');
        if (templateId) {
            return parseInt(templateId);
        }
        
        return null;
    }

    getConfigId() {
        const configInput = document.querySelector('input[name="config_id"]');
        if (configInput) {
            return parseInt(configInput.value);
        }

        const urlParams = new URLSearchParams(window.location.search);
        const configId = urlParams.get('config_id');
        if (configId) {
            return parseInt(configId);
        }

        return null;
    }

    updateConfigId(newConfigId) {
        this.configId = newConfigId;
        console.log(`[OPERATION_COSTS_JS] Config ID updated to: ${this.configId}`);
    }

    setupEventListeners() {
        document.addEventListener('change', (event) => {
            if (event.target.classList.contains('config-field')) {
                this.onFieldChange(event);
            }
        });

        document.addEventListener('configurator:field-changed', (event) => {
            this.onFieldChange(event);
        });

        document.addEventListener('configurator:values-updated', (event) => {
            this.currentValues = event.detail.values || {};
            this.refreshOperationCosts();
        });

        document.addEventListener('configurator:ready', (event) => {
            this.onConfiguratorReady();
        });

        // Listen for config ID changes
        document.addEventListener('configurator:config-id-changed', (event) => {
            this.updateConfigId(event.detail.configId);
            this.refreshOperationCosts();
        });

        document.addEventListener('configurator:calculated-fields-ready', (event) => {
            this.onConfiguratorReady();
        });

        const observer = new MutationObserver((mutations) => {
            // Check if calculated fields definitions have been added
            if (window.calculatedFieldsDefinitions && window.calculatedFieldsDefinitions.length > 0) {
                this.onConfiguratorReady();
                observer.disconnect(); // Stop observing once we detect it
            }
        });

        // Start observing
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    onConfiguratorReady() {
        // Clear any existing retry timers
        if (this.retryTimer) {
            clearTimeout(this.retryTimer);
            this.retryTimer = null;
        }

        // Refresh operation costs
        this.refreshOperationCosts();
    }


    manualRefresh() {
        this.refreshOperationCosts();
    }


    forceRefresh() {
        this.refreshOperationCosts();
    }

    onFieldChange(event) {
        // Debounce the refresh to avoid too many calls
        clearTimeout(this.refreshTimeout);
        this.refreshTimeout = setTimeout(() => {
            this.refreshOperationCosts();
        }, 300);
    }

    async refreshOperationCosts() {
        if (!this.templateId) {
            console.warn('[OperationCosts] No template ID available');
            return;
        }

        try {
            // Get current field values
            this.currentValues = this.getCurrentFieldValues();
            console.log('[OperationCosts] Refreshing operation costs with values:', this.currentValues);
            console.log('[OperationCosts] Template ID:', this.templateId);
            // Check if we got the expected calculated fields
            const calculatedFieldsCount = Object.keys(this.currentValues).filter(key => key.startsWith('_CALCULATED_')).length;

            // If we have very few calculated fields, the configurator might not be fully ready
            if (calculatedFieldsCount < 5) {
                console.warn('[OperationCosts] Very few calculated fields found, configurator might not be ready');
                console.warn('[OperationCosts] Retrying in 2 seconds...');

                setTimeout(() => {
                    this.refreshOperationCosts();
                }, 2000);
                return;
            }

            if (!this.templateId) {
                console.warn('[OperationCosts] No template ID - skipping operation costs calculation');
                this.showError('No template ID available');
                return;
            }

            // Call the backend to calculate operation costs
            const url = '/config_matrix/calculate_operation_costs';

            console.log(`[OPERATION_COSTS_BUG] ===== FRONTEND OPERATION COST REQUEST =====`);
            console.log(`[OPERATION_COSTS_BUG] Template ID: ${this.templateId}`);
            console.log(`[OPERATION_COSTS_BUG] Config ID: ${this.configId}`);
            console.log(`[OPERATION_COSTS_BUG] Field values count: ${Object.keys(this.currentValues).length}`);
            console.log(`[OPERATION_COSTS_BUG] Will use: ${this.configId ? 'BOM-based calculation' : 'Field/option mapping'}`);

            const requestBody = {
                jsonrpc: "2.0",
                method: "call",
                params: {
                    template_id: this.templateId,
                    field_values: this.currentValues,
                    config_id: this.configId
                },
                id: new Date().getTime()
            };
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(requestBody)
            });
            console.log('[OperationCosts] Response status:', response.status);
            console.log('[OperationCosts] Response ok:', response.ok);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const responseText = await response.text();
            console.log('[OperationCosts] Raw response (first 500 chars):', responseText.substring(0, 500));

            let data;
            try {
                data = JSON.parse(responseText);
            } catch (parseError) {
                console.error('[OperationCosts] JSON parse error:', parseError);
                console.error('[OperationCosts] Full response:', responseText);
                throw new Error(`Invalid JSON response: ${parseError.message}`);
            }

            if (data.result) {
                // Check if the result indicates success
                if (data.result.success === false) {
                    console.error('[OperationCosts] Backend error:', data.result.error);
                    this.showError(data.result.error || 'Failed to calculate operation costs');
                } else {
                    // Assume success if no explicit success field or if success is true

                    console.log(`[OPERATION_COSTS_BUG] ===== FRONTEND OPERATION COST RESULT =====`);
                    console.log(`[OPERATION_COSTS_BUG] Operations found: ${data.result.operations ? data.result.operations.length : 0}`);
                    console.log(`[OPERATION_COSTS_BUG] Total cost: $${data.result.total_cost || 0.0}`);
                    console.log(`[OPERATION_COSTS_BUG] Calculation method: ${this.configId ? 'BOM-based' : 'Field/option mapping'}`);
                    console.log(`[OPERATION_COSTS_BUG] ===== END RESULT =====`);

                    console.log('[OperationCosts] Success! Result:', data.result);
                    console.log('[OperationCosts] Operations data:', data.result.operations);

                    // Debug each operation
                    if (data.result.operations) {
                        data.result.operations.forEach((op, index) => {
                            console.log(`[OperationCosts] Operation ${index}:`, {
                                name: op.name,
                                question_number: op.question_number,
                                field_name: op.field_name,
                                source_type: op.source_type,
                                formula: op.formula,
                                cost: op.cost
                            });
                        });
                    }
                    this.operationCosts = data.result.operations || [];
                    this.totalCost = data.result.total_cost || 0.0;
                    this.updateDisplay();
                }
            } else if (data.error) {
                console.error('[OperationCosts] JSON-RPC error:', data.error);
                this.showError(data.error.message || data.error || 'Failed to calculate operation costs');
            } else {
                console.error('[OperationCosts] Unexpected response format:', data);
                this.showError('Unexpected response format');
            }

        } catch (error) {
            console.error('[OperationCosts] Error refreshing operation costs:', error);
            this.showError(`Network error: ${error.message || 'Unknown error'}`);
        }
    }

    getCurrentFieldValues() {
        const values = {};

        // Get values from all config fields
        const fields = document.querySelectorAll('.config-field');

        fields.forEach((field) => {
            const fieldId = field.getAttribute('data-field-id');
            const technicalName = field.getAttribute('data-technical-name');
            const fieldType = field.type;
            const fieldValue = field.value;
            const fieldChecked = field.checked;

            if (technicalName) {
                if (fieldType === 'checkbox') {
                    values[technicalName] = fieldChecked;
                } else if (fieldType === 'radio') {
                    if (fieldChecked) {
                        values[technicalName] = fieldValue;
                    }
                } else {
                    values[technicalName] = fieldValue;
                }
            }
        });

        // Also get values from select fields
        const selects = document.querySelectorAll('select.config-field');

        selects.forEach((select) => {
            const technicalName = select.getAttribute('data-technical-name');
            const selectValue = select.value;

            if (technicalName) {
                values[technicalName] = selectValue;
            }
        });

        // Include calculated fields using the same logic as visibility_conditions.js
        try {
            // Check if calculateDynamicFields function is available globally
            if (typeof window.calculateDynamicFields === 'function') {
                const calculatedFields = window.calculateDynamicFields(values);

                // Merge calculated fields with regular field values
                Object.assign(values, calculatedFields);
                console.log('[OperationCosts] Added calculated fields:', Object.keys(calculatedFields));

            } else {
                // Fallback: try to access the function from visibility conditions module
                const calculatedFields = this.calculateDynamicFieldsFallback(values);

                if (calculatedFields && Object.keys(calculatedFields).length > 0) {
                    Object.assign(values, calculatedFields);
                    console.log('[OperationCosts] Added calculated fields (fallback):', Object.keys(calculatedFields));
                }
            }
        } catch (error) {
            console.warn('[OperationCosts] Error calculating dynamic fields:', error);
            // Continue without calculated fields - operation costs can still work with basic field values
        }

        return values;
    }

    /**
     * Fallback method to calculate dynamic fields when the global function is not available
     * This implements a simplified version of the calculateDynamicFields logic from visibility_conditions.js
     */
    calculateDynamicFieldsFallback(fieldValues) {
        try {
            // Check if we can access calculated field definitions from the global scope
            if (typeof window.calculatedFieldsDefinitions !== 'undefined' &&
                window.calculatedFieldsDefinitions &&
                window.calculatedFieldsDefinitions.length > 0) {

                const results = {};
                const context = { ...fieldValues };

                // Add math functions to context
                context.Math = Math;
                context.min = Math.min;
                context.max = Math.max;
                context.abs = Math.abs;
                context.round = Math.round;
                context.ceil = Math.ceil;
                context.floor = Math.floor;
                context.sqrt = Math.sqrt;

                // Add parseFloat function for JavaScript compatibility
                context.parseFloat = (value) => {
                    if (value === null || value === undefined || value === '') return 0;
                    try {
                        return parseFloat(String(value));
                    } catch {
                        return 0;
                    }
                };

                // Calculate fields with multiple passes to handle dependencies
                const maxPasses = 5;
                let hasChanges = true;
                let pass = 0;

                while (hasChanges && pass < maxPasses) {
                    hasChanges = false;
                    pass++;

                    window.calculatedFieldsDefinitions.forEach((calcField) => {
                        try {
                            // Update context with latest results
                            Object.assign(context, results);

                            // Create a safe context that returns null for undefined variables
                            const safeContext = new Proxy(context, {
                                get: (target, prop) => {
                                    if (prop in target) {
                                        return target[prop];
                                    }
                                    // Return null for missing calculated fields to prevent ReferenceError
                                    if (prop.startsWith('_CALCULATED_')) {
                                        return null;
                                    }
                                    // Handle specific mesh series dependencies
                                    if (prop === 'mesh_series' || prop === 'template_mesh_series') {
                                        return null;
                                    }
                                    return null;
                                }
                            });

                            // Build argument list and values
                            const argNames = Object.keys(safeContext);
                            const argValues = argNames.map(name => safeContext[name]);

                            // Evaluate the formula with error handling
                            const safeFormula = `
                                try {
                                    const result = (${calcField.formula});
                                    return result === undefined ? null : result;
                                } catch (e) {
                                    if (e instanceof ReferenceError) {
                                        // Silently return null for missing dependencies
                                        return null;
                                    }
                                    console.error('Error in ${calcField.name}:', e);
                                    return null;
                                }
                            `;

                            const func = new Function(...argNames, safeFormula);
                            const result = func(...argValues);

                            // Only update if the result has changed
                            if (results[calcField.name] !== result) {
                                results[calcField.name] = result;
                                hasChanges = true;
                            }

                        } catch (error) {
                            console.error(`Error calculating ${calcField.name}:`, error);
                            results[calcField.name] = null;
                        }
                    });
                }

                return results;
            }
        } catch (error) {
            console.warn('Error in calculateDynamicFieldsFallback:', error);
        }

        return {};
    }

    updateDisplay() {
        const tableBody = document.getElementById('operation-costs-table-body');
        const totalElement = document.getElementById('configuration-operation-costs');
        
        if (!tableBody || !totalElement) {
            console.warn('[OperationCosts] Display elements not found');
            return;
        }

        // Clear existing content
        tableBody.innerHTML = '';

        if (this.operationCosts.length === 0) {
            // Show empty state
            tableBody.innerHTML = `
                <tr>
                    <td colspan="2" class="text-center text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        No operations calculated yet
                    </td>
                </tr>
            `;
            totalElement.textContent = '$0.00';
            return;
        }

        // Add operation rows
        this.operationCosts.forEach(operation => {
            const row = document.createElement('tr');

            // Create question badge if question number exists
            let questionBadge = '';
            if (operation.question_number) {
                questionBadge = `<span class="badge bg-primary me-2" title="Question ${operation.question_number}">Q${operation.question_number}</span>`;
            }

            // Create source badge
            let sourceBadge = '';
            if (operation.source_type === 'field') {
                sourceBadge = `<span class="badge bg-info me-1" title="Field: ${this.escapeHtml(operation.field_name)}">Field</span>`;
            } else if (operation.source_type === 'option') {
                sourceBadge = `<span class="badge bg-warning me-1" title="Option: ${this.escapeHtml(operation.option_name)}">Option</span>`;
            }

            row.innerHTML = `
                <td>
                    <div class="d-flex align-items-center">
                        ${questionBadge}
                        ${sourceBadge}
                        <span class="fw-bold">${this.escapeHtml(operation.name)}</span>
                    </div>
                </td>
                <td class="text-end">
                    <span class="fw-bold text-success">$${operation.cost.toFixed(2)}</span>
                    ${operation.duration ? `<br><small class="text-muted">${operation.duration.toFixed(1)} min</small>` : ''}
                </td>
            `;
            tableBody.appendChild(row);
        });

        // Update total
        totalElement.textContent = `$${this.totalCost.toFixed(2)}`;

        // Update the total aggregated price (async)
        this.updateTotalAggregatedPrice().catch(error => {
            console.error('[OperationCosts] Error updating total aggregated price:', error);
        });

        // Trigger sale prices refresh if price matrix handler is available
        if (window.priceMatrixHandler && typeof window.priceMatrixHandler.refresh === 'function') {
            window.priceMatrixHandler.refresh();
        }
    }

    showError(message) {
        const tableBody = document.getElementById('operation-costs-table-body');
        if (tableBody) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="2" class="text-center text-danger">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        ${this.escapeHtml(message)}
                    </td>
                </tr>
            `;
        }
        
        const totalElement = document.getElementById('configuration-operation-costs');
        if (totalElement) {
            totalElement.textContent = '$0.00';
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Public method to manually refresh (can be called from other scripts)
    refresh() {
        this.refreshOperationCosts();
    }

    // Public method to get current total cost
    getTotalCost() {
        return this.totalCost;
    }

    // Public method to get operation costs data
    getOperationCosts() {
        return this.operationCosts;
    }



    async updateTotalAggregatedPrice() {
        // Get component total
        const componentTotal = this.getComponentTotal();

        // Get operation total (this.totalCost)
        const operationTotal = this.totalCost;

        // Get sale prices total from price matrix handler
        let salePricesTotal = 0.0;
        if (window.priceMatrixHandler && typeof window.priceMatrixHandler.getTotalPrice === 'function') {
            salePricesTotal = window.priceMatrixHandler.getTotalPrice() || 0.0;
        }

        // Update individual totals in the aggregated price section
        const totalComponentsElement = document.getElementById('total-components');
        const totalOperationsElement = document.getElementById('total-operations');
        const priceMatrixElement = document.getElementById('configuration-price-matrix');
        const grandTotalElement = document.getElementById('configuration-total-price');

        if (totalComponentsElement) {
            totalComponentsElement.textContent = `$${componentTotal.toFixed(2)}`;
        }

        if (totalOperationsElement) {
            totalOperationsElement.textContent = `$${operationTotal.toFixed(2)}`;
        }

        if (priceMatrixElement) {
            priceMatrixElement.textContent = `$${salePricesTotal.toFixed(2)}`;
        }

        if (grandTotalElement) {
            const grandTotal = componentTotal + operationTotal + salePricesTotal;
            grandTotalElement.textContent = `$${grandTotal.toFixed(2)}`;
        }

        // console.log('[OperationCosts] Updated total aggregated price: Components:', componentTotal, 'Operations:', operationTotal, 'Sale Prices:', salePricesTotal);
    }

    getComponentTotal() {
        // Get the component total from the components section
        const componentPriceElement = document.getElementById('configuration-price');
        if (componentPriceElement) {
            const priceText = componentPriceElement.textContent.replace('$', '').replace(',', '');
            return parseFloat(priceText) || 0;
        }
        return 0;
    }

    // Method to get the grand total for saving to sales order
    async getGrandTotal() {
        const grandTotalElement = document.getElementById('configuration-total-price');
        if (grandTotalElement) {
            const priceText = grandTotalElement.textContent.replace('$', '').replace(',', '');
            return parseFloat(priceText) || 0;
        }
        // Fallback: calculate manually
        let salePricesTotal = 0.0;
        if (window.priceMatrixHandler && typeof window.priceMatrixHandler.getTotalPrice === 'function') {
            salePricesTotal = window.priceMatrixHandler.getTotalPrice() || 0.0;
        }
        return this.getComponentTotal() + this.totalCost + salePricesTotal;
    }
}

// Create global instance when script loads
window.operationCostsHandler = new OperationCostsHandler();

// Global functions for other scripts to use
window.refreshOperationCosts = () => {
    if (window.operationCostsHandler) {
        window.operationCostsHandler.manualRefresh();
    } else {
        console.warn('[OperationCosts] Operation costs handler not available');
    }
};

window.forceRefreshOperationCosts = () => {
    if (window.operationCostsHandler) {
        window.operationCostsHandler.forceRefresh();
    } else {
        console.warn('[OperationCosts] Operation costs handler not available');
    }
};

window.isOperationCostsReady = () => {
    if (window.operationCostsHandler) {
        return window.operationCostsHandler.isConfiguratorReady();
    }
    return false;
};

console.log('[OperationCosts] Global functions registered: refreshOperationCosts(), forceRefreshOperationCosts(), isOperationCostsReady()');

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = OperationCostsHandler;
}
