/** @odoo-module **/

import { registry } from "@web/core/registry";
import { Component, onWillStart, useState, onMounted, onWillUnmount } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";
import { standardWidgetProps } from "@web/views/widgets/standard_widget_props";

class ConfigMatrixConfigurator extends Component {
    setup() {
        this.state = useState({
            template: null,
            sections: [],
            values: {},
            loading: true,
            saving: false,
            error: null,
            currentSection: 0,
            dirty: false,
            // NEW: Track field visibility states
            fieldVisibility: {},
            hiddenFieldsWithValues: []
        });

        this.getServices();
        this.extractParams();

        // Initialize calculated fields cache
        this.calculatedFieldsCache = null;
        this.lastFieldValuesHash = null;

        // NEW: Dependency management
        this.dependencyGraph = new Map(); // field -> Set of fields that depend on it
        this.reverseDependencyGraph = new Map(); // field -> Set of fields it depends on
        this.allFields = new Map(); // technical_name -> field object

        // PERFORMANCE: Expression caching
        this.expressionCache = new Map();
        this.maxCacheSize = 1000;

        // PERFORMANCE: Performance monitoring
        this.performanceMetrics = {
            fieldChanges: 0,
            evaluations: 0,
            cacheHits: 0,
            cacheMisses: 0,
            totalTime: 0,
            dependencyUpdates: 0
        };

        // PERFORMANCE: Debounced functions
        this.debouncedUpdateVisibility = this.debounce(this.updateVisibilityAll.bind(this), 100);
        this.debouncedUpdateDynamicLabels = this.debounce(this.updateDynamicLabels.bind(this), 200);
        this.debouncedUpdateDynamicDefaults = this.debounce(this.updateDynamicDefaults.bind(this), 150);

        // PERFORMANCE: Memory management
        this.memoryCleanupInterval = null;
        this.lastCleanupTime = Date.now();
        this.fieldAccessTimes = new Map(); // Track when fields were last accessed

        onWillStart(async () => {
            await this.loadTemplate();
        });

        onMounted(() => {
            try {
                this.buildDependencyGraph();
                this.updateVisibilityAll(); // Use full update on initial load
                this.syncStateToDOM();

                // Initialize dynamic help text and defaults on mount
                setTimeout(() => {
                    try {
                        this.updateDynamicLabels();
                        this.updateDynamicDefaults();
                    } catch (error) {
                        console.error('Error during dynamic initialization on mount:', error);
                        // Don't prevent the configurator from loading
                    }
                }, 100);
            } catch (error) {
                console.error('Error during configurator mount:', error);
                // Set loading to false to show the form even if there are errors
                this.state.loading = false;
            }

            // NEW: Expose global functions for issues panel
            this.exposeGlobalFunctions();

            // PERFORMANCE: Start memory management
            this.startMemoryManagement();
        });

        onWillUnmount(() => {
            // PERFORMANCE: Cleanup on component destruction
            this.stopMemoryManagement();
            this.clearExpressionCache();
            console.log('[PERF] ConfigMatrix component unmounted, memory cleaned up');
        });
    }

    // PERFORMANCE: Utility function for debouncing
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // PERFORMANCE: Expression cache management
    getCachedExpression(key) {
        if (this.expressionCache.has(key)) {
            // Move to end (LRU)
            const value = this.expressionCache.get(key);
            this.expressionCache.delete(key);
            this.expressionCache.set(key, value);
            this.performanceMetrics.cacheHits++;
            return value;
        }
        this.performanceMetrics.cacheMisses++;
        return null;
    }

    setCachedExpression(key, value) {
        if (this.expressionCache.size >= this.maxCacheSize) {
            // Remove oldest entry (LRU)
            const firstKey = this.expressionCache.keys().next().value;
            this.expressionCache.delete(firstKey);
        }
        this.expressionCache.set(key, value);
    }

    clearExpressionCache() {
        this.expressionCache.clear();
        console.log('[PERF] Expression cache cleared');
    }

    // PERFORMANCE: Selective cache clearing for specific fields
    clearExpressionCacheForField(fieldName) {
        let clearedCount = 0;
        for (const [key, value] of this.expressionCache.entries()) {
            // Clear cache entries that might be affected by this field
            if (key.includes(fieldName) || key.includes(`"${fieldName}"`)) {
                this.expressionCache.delete(key);
                clearedCount++;
            }
        }
        if (clearedCount > 0) {
            // console.log(`[PERF] Cleared ${clearedCount} cache entries for field ${fieldName}`);
        }
    }

    // PERFORMANCE: Performance monitoring utilities
    startTimer(operation) {
        return {
            operation,
            startTime: performance.now()
        };
    }

    endTimer(timer) {
        const duration = performance.now() - timer.startTime;
        this.performanceMetrics.totalTime += duration;
        console.log(`[PERF] ${timer.operation}: ${duration.toFixed(2)}ms`);
        return duration;
    }

    logPerformanceMetrics() {
        const cacheHitRate = this.performanceMetrics.cacheHits /
            (this.performanceMetrics.cacheHits + this.performanceMetrics.cacheMisses) * 100;

        const avgResponseTime = this.performanceMetrics.totalTime /
            Math.max(this.performanceMetrics.fieldChanges, 1);

        console.table({
            'Field Changes': this.performanceMetrics.fieldChanges,
            'Evaluations': this.performanceMetrics.evaluations,
            'Dependency Updates': this.performanceMetrics.dependencyUpdates,
            'Cache Hit Rate': `${cacheHitRate.toFixed(1)}%`,
            'Average Response Time': `${avgResponseTime.toFixed(2)}ms`,
            'Total Time': `${this.performanceMetrics.totalTime.toFixed(2)}ms`,
            'Cache Size': this.expressionCache.size,
            'Memory Usage': this.getMemoryUsage()
        });
    }

    // PERFORMANCE: Memory management methods
    startMemoryManagement() {
        // Clean up memory every 2 minutes
        this.memoryCleanupInterval = setInterval(() => {
            this.performMemoryCleanup();
        }, 120000); // 2 minutes

        console.log('[MEMORY] Memory management started');
    }

    stopMemoryManagement() {
        if (this.memoryCleanupInterval) {
            clearInterval(this.memoryCleanupInterval);
            this.memoryCleanupInterval = null;
        }
        console.log('[MEMORY] Memory management stopped');
    }

    performMemoryCleanup() {
        const startTime = performance.now();
        console.log('[MEMORY] Starting memory cleanup...');

        // Clear expression cache if it's getting too large
        if (this.expressionCache.size > 500) {
            const oldSize = this.expressionCache.size;
            this.clearExpressionCache();
            console.log(`[MEMORY] Cleared expression cache: ${oldSize} entries`);
        }

        // Clean up old field access times
        this.cleanupOldFieldAccess();

        // Clean up calculated fields cache periodically
        if (this.calculatedFieldsCache && Date.now() - this.lastCleanupTime > 300000) { // 5 minutes
            this.calculatedFieldsCache = null;
            this.lastFieldValuesHash = null;
            console.log('[MEMORY] Cleared calculated fields cache');
        }

        // Update last cleanup time
        this.lastCleanupTime = Date.now();

        const duration = performance.now() - startTime;
        console.log(`[MEMORY] Memory cleanup completed in ${duration.toFixed(2)}ms`);

        // Force garbage collection if available (development only)
        if (window.gc && typeof window.gc === 'function') {
            window.gc();
            console.log('[MEMORY] Forced garbage collection');
        }
    }

    cleanupOldFieldAccess() {
        const cutoff = Date.now() - (10 * 60 * 1000); // 10 minutes
        let cleanedCount = 0;

        for (const [fieldName, accessTime] of this.fieldAccessTimes.entries()) {
            if (accessTime < cutoff) {
                this.fieldAccessTimes.delete(fieldName);
                cleanedCount++;
            }
        }

        if (cleanedCount > 0) {
            console.log(`[MEMORY] Cleaned up ${cleanedCount} old field access records`);
        }
    }

    getMemoryUsage() {
        if (performance.memory) {
            const used = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
            const total = Math.round(performance.memory.totalJSHeapSize / 1024 / 1024);
            return `${used}MB / ${total}MB`;
        }
        return 'N/A';
    }

    trackFieldAccess(fieldName) {
        this.fieldAccessTimes.set(fieldName, Date.now());
    }

    getServices() {
        const safeUseService = (name, fallback) => {
            try {
                return useService(name);
            } catch (error) {
                console.warn(`${name} service not available:`, error);
                return fallback;
            }
        };

        this.orm = safeUseService("orm", {
            call: () => Promise.resolve({}),
            read: () => Promise.resolve([]),
            create: () => Promise.resolve(0),
            write: () => Promise.resolve(true),
        });

        this.rpc = safeUseService("rpc", {
            query: () => Promise.resolve({}),
        });

        this.notification = safeUseService("notification", {
            add: (message) => {}, // Notification service placeholder
        });

        this.action = safeUseService("action", {
            doAction: () => Promise.resolve({}),
        });
    }

    extractParams() {
        this.params = this.props.params || {};

        if (Object.keys(this.params).length === 0) {
            const urlParams = new URLSearchParams(window.location.search);

            const templateId = urlParams.get('template_id') || urlParams.get('matrix_id');
            if (templateId) {
                this.params.template_id = parseInt(templateId, 10);
                this.params.matrix_id = parseInt(templateId, 10);
            }

            const productId = urlParams.get('product_id');
            if (productId) {
                this.params.product_id = parseInt(productId, 10);
            }

            const orderLineId = urlParams.get('order_line_id');
            if (orderLineId) {
                this.params.order_line_id = parseInt(orderLineId, 10);
            }

            const configId = urlParams.get('config_id');
            if (configId) {
                this.params.config_id = parseInt(configId, 10);
            }
        }
    }

    async loadTemplate(useCase = null) {
        try {
            this.state.loading = true;

            const templateId = this.params.template_id || this.params.matrix_id;
            const configId = this.params.config_id;

            if (!templateId) {
                throw new Error("No template ID provided");
            }

            const fromSalesOrder = this.params.order_line_id ? true : false;

            const template = await this.orm.call(
                "config.matrix.template",
                "get_template_structure",
                [templateId, useCase],
                { from_sale_order: fromSalesOrder }
            );

            if (!template || template.error) {
                throw new Error(template?.error || "Failed to load template");
            }

            // Load calculated fields definitions
            await this.loadCalculatedFields(templateId);

            this.state.template = template;
            this.state.sections = template.sections || [];
            this.state.activeUseCase = template.active_use_case || 'check_measure';
            this.state.enabledUseCases = template.enabled_use_cases || ['check_measure', 'sales', 'online'];

            // NEW: Build field registry for quick lookup
            this.buildFieldRegistry();

            if (configId) {
                const config = await this.orm.read(
                    "config.matrix.configuration",
                    [configId],
                    ["config_data"]
                );

                if (config.length && config[0].config_data) {
                    this.state.values = JSON.parse(config[0].config_data);
                }
            }

            this.state.loading = false;

            // Initialize dynamic help text after template loads
            setTimeout(() => {
                try {
                    const initialCalculatedFields = this.calculateDynamicFields(this.state.values);
                } catch (error) {
                    console.error('Error during initial calculated fields setup:', error);
                }

                try {
                    this.updateDynamicLabels();
                } catch (error) {
                    console.error('Error updating dynamic labels after template load:', error);
                }

                try {
                    this.updateDynamicDefaults();
                } catch (error) {
                    console.error('Error updating dynamic defaults after template load:', error);
                }
            }, 100);
        } catch (error) {
            this.state.error = error.message || "Failed to load configuration template";
            this.state.loading = false;
            this.notification.add(this.state.error, {
                type: "danger",
                sticky: true,
                title: "Configuration Template Error",
            });
            console.error("Template loading error:", error);
        }
    }

    // NEW: Build field registry for quick lookup
    buildFieldRegistry() {
        this.allFields.clear();
        
        for (const section of this.state.sections) {
            for (const field of section.fields) {
                this.allFields.set(field.technical_name, field);
                // Initialize visibility state
                this.state.fieldVisibility[field.technical_name] = true;
            }
        }
    }

    // NEW: Build dependency graph to track field relationships
    buildDependencyGraph() {
        this.dependencyGraph.clear();
        this.reverseDependencyGraph.clear();

        console.log('Building dependency graph...');

        for (const [fieldName, field] of this.allFields) {
            // Initialize dependency sets
            if (!this.dependencyGraph.has(fieldName)) {
                this.dependencyGraph.set(fieldName, new Set());
            }
            if (!this.reverseDependencyGraph.has(fieldName)) {
                this.reverseDependencyGraph.set(fieldName, new Set());
            }

            // Parse visibility condition to find dependencies
            if (field.visibility_condition && field.visibility_condition !== 'true') {
                const dependencies = this.extractFieldDependencies(field.visibility_condition);
                
                for (const dependency of dependencies) {
                    // dependency -> fieldName (dependency affects fieldName)
                    if (!this.dependencyGraph.has(dependency)) {
                        this.dependencyGraph.set(dependency, new Set());
                    }
                    this.dependencyGraph.get(dependency).add(fieldName);

                    // fieldName depends on dependency
                    this.reverseDependencyGraph.get(fieldName).add(dependency);
                }

                console.log(`Field ${fieldName} depends on:`, Array.from(dependencies));
            }
        }

        console.log('Dependency graph built:', {
            forward: Object.fromEntries(this.dependencyGraph),
            reverse: Object.fromEntries(this.reverseDependencyGraph)
        });
    }

    // NEW: Extract field dependencies from visibility conditions
    extractFieldDependencies(condition) {
        const dependencies = new Set();
        
        try {
            // Handle JSON conditions
            if (condition.startsWith('__JSON__')) {
                const jsonStr = condition.substring(8);
                const conditions = JSON.parse(jsonStr);
                
                for (const cond of conditions) {
                    const fieldNames = this.extractFieldNamesFromExpression(cond.condition);
                    fieldNames.forEach(name => dependencies.add(name));
                }
            } else {
                // Handle regular conditions
                const fieldNames = this.extractFieldNamesFromExpression(condition);
                fieldNames.forEach(name => dependencies.add(name));
            }
        } catch (error) {
            console.error(`Error parsing condition: ${condition}`, error);
        }

        return dependencies;
    }

    // NEW: Extract field names from an expression
    extractFieldNamesFromExpression(expression) {
        const fieldNames = new Set();
        
        // Regular expression to find field names (alphanumeric + underscores)
        const fieldNameRegex = /\b([a-zA-Z][a-zA-Z0-9_]*)\b/g;
        const matches = expression.match(fieldNameRegex);
        
        if (matches) {
            // Filter out keywords and functions
            const keywords = [
                'and', 'or', 'not', 'true', 'false', 'null', 'undefined',
                'Math', 'min', 'max', 'abs', 'round', 'floor', 'ceil',
                'sqrt', 'in', 'parseFloat', 'parseInt'
            ];
            
            for (const match of matches) {
                if (!keywords.includes(match) && 
                    !match.match(/^\d+$/) && // Not a number
                    match.length > 1 && // Reasonable length
                    this.allFields.has(match)) { // Actually exists as a field
                    fieldNames.add(match);
                }
            }
        }
        
        return fieldNames;
    }



    // PERFORMANCE: Update visibility for all fields (fallback method)
    updateVisibilityAll() {
        if (!this.state.sections.length) return;

        const timer = this.startTimer('updateVisibilityAll');
        console.log('[PERF] Updating visibility for ALL fields...');

        const changedFields = new Set();

        try {
            // First pass: Update all field visibility
            for (const section of this.state.sections) {
                for (const field of section.fields) {
                    const wasVisible = this.state.fieldVisibility[field.technical_name];
                    const isVisible = this.evaluateVisibility(field);

                    field.visible = isVisible;
                    this.state.fieldVisibility[field.technical_name] = isVisible;

                    if (wasVisible !== isVisible) {
                        changedFields.add(field.technical_name);
                        console.log(`[PERF] Field ${field.technical_name} visibility changed: ${wasVisible} -> ${isVisible}`);
                    }
                }
            }

            // Second pass: Find hidden fields with values for issues detection
            this.updateHiddenFieldsWithValues();

            // Sync state to DOM
            this.syncStateToDOM();

            console.log('[PERF] Full visibility update complete. Changed fields:', Array.from(changedFields));
        } catch (error) {
            this.notification.add(
                `Error updating field visibility: ${error.message}`,
                {
                    type: "warning",
                    title: "Visibility Calculation Error",
                }
            );
            console.error("Full visibility update error:", error);
        }

        this.endTimer(timer);
    }

    // NEW: Update hidden fields with values tracking
    updateHiddenFieldsWithValues() {
        const hiddenFieldsWithValues = [];

        for (const [fieldName, field] of this.allFields) {
            const isVisible = this.state.fieldVisibility[fieldName];
            const hasValue = this.hasNonDefaultValue(field, this.state.values[fieldName]);

            if (!isVisible && hasValue) {
                // Direct hidden field with value
                hiddenFieldsWithValues.push({
                    technical_name: fieldName,
                    field: field,
                    value: this.state.values[fieldName],
                    reason: 'hidden_with_value'
                });
            } else if (isVisible && hasValue) {
                // Check for cascade dependency (visible field depending on hidden field)
                const dependsOnHiddenField = this.checkCascadeDependency(fieldName);
                if (dependsOnHiddenField) {
                    hiddenFieldsWithValues.push({
                        technical_name: fieldName,
                        field: field,
                        value: this.state.values[fieldName],
                        reason: `depends_on_hidden_field: ${dependsOnHiddenField}`
                    });
                }
            }
        }

        this.state.hiddenFieldsWithValues = hiddenFieldsWithValues;
        console.log('Hidden fields with values:', hiddenFieldsWithValues);
    }

    // NEW: Check if a field depends on any hidden fields
    checkCascadeDependency(fieldName) {
        const dependencies = this.reverseDependencyGraph.get(fieldName);
        if (!dependencies) return null;

        for (const dependency of dependencies) {
            if (!this.state.fieldVisibility[dependency]) {
                return dependency; // Found a hidden dependency
            }
            
            // Check transitive dependencies
            const transitiveDep = this.checkCascadeDependency(dependency);
            if (transitiveDep) {
                return transitiveDep;
            }
        }

        return null;
    }

    // NEW: Check if field has meaningful value
    hasNonDefaultValue(field, value) {
        if (value === undefined || value === null || value === '') {
            return false;
        }

        if (field.field_type === 'boolean') {
            return value === true || value === 'true' || value === 'on';
        } else if (field.field_type === 'number') {
            const numValue = parseFloat(value);
            return !isNaN(numValue) && numValue !== 0;
        } else {
            const stringValue = String(value).trim();
            return stringValue !== '' && stringValue !== 'undefined' && stringValue !== 'null';
        }
    }

    // NEW: Sync component state to DOM
    syncStateToDOM() {
        for (const [fieldName, isVisible] of Object.entries(this.state.fieldVisibility)) {
            const field = this.allFields.get(fieldName);
            if (!field) continue;

            // Find field container in DOM
            const container = document.querySelector(`[data-technical-name="${fieldName}"]`)?.closest('.config-field-container');
            if (!container) continue;

            // Update DOM classes based on visibility
            container.classList.remove('conditional-field-pending', 'conditional-visible');
            
            if (field.visibility_condition && field.visibility_condition !== 'true') {
                if (isVisible) {
                    container.classList.add('conditional-visible');
                } else {
                    container.classList.add('conditional-field-pending');
                }
            }

            // Update display style
            container.style.display = isVisible ? '' : 'none';
        }

        // Update the global window objects that the issues panel expects
        if (typeof window !== 'undefined') {
            window.fieldValues = { ...this.state.values };
            window.configuratorComponent = this;
        }
    }

    // NEW: Expose global functions for issues panel integration
    exposeGlobalFunctions() {
        if (typeof window === 'undefined') return;

        // Make configurator component globally accessible
        window.configuratorComponent = this;

        // Override the global functions with proper implementations
        window.findHiddenFieldsWithValues = () => {
            return this.state.hiddenFieldsWithValues.map(item => ({
                id: item.field.id,
                name: item.technical_name,
                value: item.value,
                element: document.querySelector(`[data-technical-name="${item.technical_name}"]`),
                hiddenReason: item.reason
            }));
        };

        window.clearAllIssues = () => {
            const hiddenFields = this.state.hiddenFieldsWithValues;
            let clearedCount = 0;

            for (const hiddenField of hiddenFields) {
                this.clearField(hiddenField.technical_name);
                clearedCount++;
            }

            console.log(`Cleared ${clearedCount} hidden fields with values`);
            
            // Update visibility after clearing
            setTimeout(() => {
                this.updateVisibilityAll(); // Full update after clearing fields
            }, 100);

            return clearedCount;
        };

        window.setFieldValue = (fieldName, value) => {
            this.setFieldValue(fieldName, value);
        };

        window.updateFieldVisibility = () => {
            this.updateVisibilityAll();
        };

        // PERFORMANCE: Expose performance monitoring functions
        window.showPerformanceDashboard = () => {
            this.logPerformanceMetrics();
        };

        window.clearPerformanceCache = () => {
            this.clearExpressionCache();
            this.performanceMetrics = {
                fieldChanges: 0,
                evaluations: 0,
                cacheHits: 0,
                cacheMisses: 0,
                totalTime: 0,
                dependencyUpdates: 0
            };
            console.log('[PERF] Performance metrics and cache cleared');
        };

        window.runPerformanceTest = () => {
            this.runComprehensivePerformanceTest();
        };

        window.runStressTest = () => {
            this.runStressTest();
        };

        window.validateOptimizations = () => {
            this.validateOptimizations();
        };
    }

    // NEW: Clear a specific field
    clearField(fieldName) {
        const field = this.allFields.get(fieldName);
        if (!field) {
            console.warn(`Field ${fieldName} not found`);
            return;
        }

        // Clear from component state
        delete this.state.values[fieldName];
        
        // Clear from DOM
        const fieldElement = document.querySelector(`[data-technical-name="${fieldName}"]`);
        if (fieldElement) {
            if (field.field_type === 'boolean') {
                fieldElement.checked = false;
            } else {
                fieldElement.value = '';
            }

            // Trigger change event
            fieldElement.dispatchEvent(new Event('change', { bubbles: true }));
        }

        // Clear hidden form field
        const hiddenField = document.getElementById(`hidden_field_${field.id}`);
        if (hiddenField) {
            hiddenField.value = '';
        }

        console.log(`Cleared field: ${fieldName}`);
    }

    // NEW: Set field value programmatically
    setFieldValue(fieldName, value) {
        const field = this.allFields.get(fieldName);
        if (!field) {
            console.warn(`Field ${fieldName} not found`);
            return;
        }

        // Update component state
        this.state.values[fieldName] = value;
        this.state.dirty = true;

        // Update DOM
        const fieldElement = document.querySelector(`[data-technical-name="${fieldName}"]`);
        if (fieldElement) {
            if (field.field_type === 'boolean') {
                fieldElement.checked = value === true || value === 'true' || value === 'on';
            } else {
                fieldElement.value = value;
            }
        }

        // Update hidden form field
        const hiddenField = document.getElementById(`hidden_field_${field.id}`);
        if (hiddenField) {
            hiddenField.value = value;
        }

        // Invalidate calculated fields cache
        this.calculatedFieldsCache = null;
        this.lastFieldValuesHash = null;

        // Update visibility and dynamic labels
        this.updateVisibilityAll(); // Full update when setting field value programmatically
        this.updateDynamicLabels();
    }

    // PERFORMANCE: Enhanced visibility evaluation with caching
    evaluateVisibility(field) {
        if (!field.visibility_condition || field.visibility_condition === 'true') {
            return true;
        }

        this.performanceMetrics.evaluations++;

        // Track field access for memory management
        this.trackFieldAccess(field.technical_name);

        // Create cache key from field condition and current values
        const contextValues = { ...this.state.values };

        // Add calculated fields to context for cache key
        try {
            const calculatedFields = this.calculateDynamicFields(this.state.values);
            Object.assign(contextValues, calculatedFields);
        } catch (error) {
            console.warn('Error calculating fields for visibility evaluation:', error);
        }

        const cacheKey = `${field.technical_name}:${field.visibility_condition}:${JSON.stringify(contextValues)}`;

        // Check cache first
        const cachedResult = this.getCachedExpression(cacheKey);
        if (cachedResult !== null) {
            return cachedResult;
        }

        try {
            const context = { ...contextValues };

            const mathFunctions = ["abs", "ceil", "floor", "max", "min", "round", "sqrt"];
            for (const func of mathFunctions) {
                context[func] = Math[func];
            }

            let result;

            if (field.visibility_condition.startsWith('__JSON__')) {
                result = this.evaluateJsonCondition(field.visibility_condition, context);
            } else if (this.isRangeCondition(field.visibility_condition)) {
                result = this.evaluateRangeCondition(field.visibility_condition, context);
            } else {
                // Try simple condition first (faster)
                const simpleConditionRegex = /^([a-zA-Z0-9_]+)\s*(==|!=|>|<|>=|<=|in|not in)\s*(.+)$/;
                const match = field.visibility_condition.match(simpleConditionRegex);

                if (match) {
                    result = this.evaluateSimpleCondition(match, context);
                } else {
                    // Fall back to complex expression evaluation
                    const cleanCondition = this.cleanExpressionForJS(field.visibility_condition);
                    result = new Function(...Object.keys(context), `return ${cleanCondition};`)(...Object.values(context));
                }
            }

            // Cache the result
            this.setCachedExpression(cacheKey, result);
            return result;

        } catch (error) {
            console.error(`Error evaluating visibility for ${field.name}:`, error);
            // Cache the default result to avoid repeated failures
            this.setCachedExpression(cacheKey, true);
            return true;
        }
    }

    // PERFORMANCE: Optimized simple condition evaluation
    evaluateSimpleCondition(match, context) {
        const [_, fieldName, operator, rawValue] = match;
        const fieldValue = context[fieldName];

        if ((operator === 'in' || operator === 'not in') && rawValue.trim().startsWith('[') && rawValue.trim().endsWith(']')) {
            const arrayContent = rawValue.trim().substring(1, rawValue.trim().length - 1);
            const listValues = arrayContent.split(',').map(item => {
                return item.trim().replace(/^['"]|['"]$/g, '');
            });

            const isInList = listValues.includes(fieldValue);
            return operator === 'in' ? isInList : !isInList;
        }

        let conditionValue;
        try {
            conditionValue = eval(rawValue);
        } catch (e) {
            conditionValue = rawValue.replace(/['"]/g, '');
        }

        switch (operator) {
            case '==': return fieldValue == conditionValue;
            case '!=': return fieldValue != conditionValue;
            case '>': return fieldValue > conditionValue;
            case '<': return fieldValue < conditionValue;
            case '>=': return fieldValue >= conditionValue;
            case '<=': return fieldValue <= conditionValue;
            case 'in': return Array.isArray(conditionValue) && conditionValue.includes(fieldValue);
            case 'not in': return Array.isArray(conditionValue) && !conditionValue.includes(fieldValue);
            default: return false;
        }
    }

    isRangeCondition(condition) {
        return condition.includes('?') && condition.includes(':') ||
               condition.includes('abs(') ||
               (condition.includes('>=') && condition.includes('<=') && condition.includes('&&'));
    }

    evaluateRangeCondition(condition, context) {
        try {
            context.abs = Math.abs;
            const cleanCondition = this.cleanExpressionForJS(condition);
            const result = new Function(...Object.keys(context), `return ${cleanCondition};`)(...Object.values(context));
            return Boolean(result);
        } catch (error) {
            console.error(`Error evaluating range condition: ${condition}`, error);
            return true;
        }
    }

    evaluateJsonCondition(jsonCondition, context) {
        try {
            const jsonStr = jsonCondition.substring(8);
            const conditions = JSON.parse(jsonStr);

            const andConditions = conditions.filter(c => c.logic === 'and');
            const orConditions = conditions.filter(c => c.logic === 'or');

            const evaluateSingleCondition = (condition) => {
                if (this.isRangeCondition(condition)) {
                    return this.evaluateRangeCondition(condition, context);
                }

                const inListRegex = /^([a-zA-Z0-9_]+)\s+(in|not\s+in)\s+\[(.*)\]$/i;
                const inListMatch = condition.match(inListRegex);

                if (inListMatch) {
                    const [_, fieldName, operator, valueList] = inListMatch;
                    const fieldValue = context[fieldName];
                    const listValues = valueList.split(',').map(item => {
                        return item.trim().replace(/^['"]|['"]$/g, '');
                    });
                    const isInList = listValues.includes(fieldValue);
                    return operator.toLowerCase() === 'in' ? isInList : !isInList;
                }

                try {
                    const cleanCondition = this.cleanExpressionForJS(condition);
                    return new Function(...Object.keys(context), `return ${cleanCondition};`)(...Object.values(context));
                } catch (error) {
                    console.error(`Error evaluating condition: ${condition}`, error);
                    return true;
                }
            };

            const andResult = andConditions.length === 0 ||
                andConditions.every(c => evaluateSingleCondition(c.condition));

            const orResult = orConditions.length === 0 ||
                orConditions.some(c => evaluateSingleCondition(c.condition));

            return andResult && orResult;
        } catch (error) {
            console.error('Error parsing JSON condition:', error);
            return true;
        }
    }

    // PERFORMANCE: Optimized field change handler with selective updates
    onFieldChange(field, value) {
        const timer = this.startTimer(`onFieldChange(${field.technical_name})`);
        this.performanceMetrics.fieldChanges++;

        try {
            console.log(`[PERF] Field ${field.technical_name} changed to: ${value}`);

            // Update field value
            this.state.values[field.technical_name] = value;
            this.state.dirty = true;

            // PERFORMANCE: Only invalidate calculated fields cache if necessary
            // Don't clear the entire cache - let calculateDynamicFields handle its own caching
            const oldHash = this.lastFieldValuesHash;
            this.lastFieldValuesHash = null; // Force recalculation check

            // PERFORMANCE: Only clear expression cache for affected fields, not all
            this.clearExpressionCacheForField(field.technical_name);

            // PERFORMANCE: Get dependent fields instead of updating all fields
            const dependentFields = this.getDependentFields(field.technical_name);

            if (dependentFields.length > 0) {
                console.log(`[PERF] Updating ${dependentFields.length} dependent fields:`, dependentFields);
                // Use selective visibility update
                this.updateVisibility(dependentFields);
            } else {
                console.log(`[PERF] No dependent fields found for ${field.technical_name}`);
            }

            // Validate range conditions for changed field
            this.validateRangeConditions(field, value);

            // CRITICAL: Don't debounce these - they're essential for functionality
            // Update dynamic labels and defaults immediately
            this.updateDynamicLabels();
            this.updateDynamicDefaults();

        } catch (error) {
            console.error(`Error handling field change for ${field.name}:`, error);
            this.notification.add(
                `Error updating field: ${error.message}`,
                {
                    type: "warning",
                    title: "Field Update Error",
                }
            );
        }

        this.endTimer(timer);
    }

    // PERFORMANCE: Get fields that depend on the changed field
    getDependentFields(fieldName) {
        const dependents = this.dependencyGraph.get(fieldName);
        return dependents ? Array.from(dependents) : [];
    }

    // PERFORMANCE: Progressive visibility update for large field sets
    async updateVisibilityProgressive(fieldNames) {
        if (!fieldNames || fieldNames.length === 0) return;

        const timer = this.startTimer('updateVisibilityProgressive');
        const batchSize = 10; // Process 10 fields at a time
        const batches = [];

        // Split fields into batches
        for (let i = 0; i < fieldNames.length; i += batchSize) {
            batches.push(fieldNames.slice(i, i + batchSize));
        }

        console.log(`[PERF] Processing ${fieldNames.length} fields in ${batches.length} batches`);

        const changedFields = new Set();
        let processedCount = 0;

        try {
            // Process batches with yield points
            for (const batch of batches) {
                // Process current batch
                for (const fieldName of batch) {
                    const field = this.allFields.get(fieldName);
                    if (!field) continue;

                    const wasVisible = this.state.fieldVisibility[fieldName];
                    const isVisible = this.evaluateVisibility(field);

                    field.visible = isVisible;
                    this.state.fieldVisibility[fieldName] = isVisible;

                    if (wasVisible !== isVisible) {
                        changedFields.add(fieldName);
                        console.log(`[PERF] Field ${fieldName} visibility changed: ${wasVisible} -> ${isVisible}`);
                    }

                    processedCount++;
                }

                // Yield to browser for UI updates after each batch
                await new Promise(resolve => setTimeout(resolve, 0));

                // Update progress
                const progress = Math.round((processedCount / fieldNames.length) * 100);
                console.log(`[PERF] Progress: ${progress}% (${processedCount}/${fieldNames.length})`);
            }

            // Update hidden fields tracking and DOM if any visibility changed
            if (changedFields.size > 0) {
                this.updateHiddenFieldsWithValues();
                this.syncStateToDOM();
            }

            console.log(`[PERF] Progressive visibility update complete. Changed fields:`, Array.from(changedFields));
        } catch (error) {
            this.notification.add(
                `Error during progressive visibility update: ${error.message}`,
                {
                    type: "warning",
                    title: "Progressive Update Error",
                }
            );
            console.error("Progressive visibility update error:", error);
        }

        this.endTimer(timer);
    }

    // PERFORMANCE: Enhanced updateVisibility with progressive fallback
    updateVisibility(fieldNames = null) {
        if (!this.state.sections.length) return;

        // If no specific fields provided, update all fields
        if (!fieldNames) {
            return this.updateVisibilityAll();
        }

        // Use progressive updates for large field sets
        if (fieldNames.length > 20) {
            console.log(`[PERF] Using progressive updates for ${fieldNames.length} fields`);
            return this.updateVisibilityProgressive(fieldNames);
        }

        // Use regular selective update for smaller sets
        return this.updateVisibilitySelective(fieldNames);
    }

    // PERFORMANCE: Regular selective update (renamed from original updateVisibility)
    updateVisibilitySelective(fieldNames) {
        const timer = this.startTimer('updateVisibilitySelective');

        console.log(`[PERF] Updating visibility for ${fieldNames.length} specific fields:`, fieldNames);

        const changedFields = new Set();
        this.performanceMetrics.dependencyUpdates++;

        try {
            // Update only specified fields
            for (const fieldName of fieldNames) {
                const field = this.allFields.get(fieldName);
                if (!field) continue;

                const wasVisible = this.state.fieldVisibility[fieldName];
                const isVisible = this.evaluateVisibility(field);

                field.visible = isVisible;
                this.state.fieldVisibility[fieldName] = isVisible;

                if (wasVisible !== isVisible) {
                    changedFields.add(fieldName);
                    console.log(`[PERF] Field ${fieldName} visibility changed: ${wasVisible} -> ${isVisible}`);
                }
            }

            // Update hidden fields tracking if any visibility changed
            if (changedFields.size > 0) {
                this.updateHiddenFieldsWithValues();
                this.syncStateToDOM();
            }

            console.log(`[PERF] Selective visibility update complete. Changed fields:`, Array.from(changedFields));
        } catch (error) {
            this.notification.add(
                `Error updating field visibility: ${error.message}`,
                {
                    type: "warning",
                    title: "Visibility Calculation Error",
                }
            );
            console.error("Selective visibility update error:", error);
        }

        this.endTimer(timer);
    }

    updateDynamicLabels() {
        try {
            // Update help text in the DOM
            const helpContainers = document.querySelectorAll('[data-use-dynamic-help="true"]');

            helpContainers.forEach((container, index) => {
                const template = container.getAttribute('data-dynamic-help-template');

                if (template) {
                    const dynamicHelp = this.evaluateTemplate(template);
                    const dynamicHelpDiv = container.querySelector('.dynamic-help-text');
                    const staticHelpDiv = container.querySelector('.static-help-text');

                    if (dynamicHelpDiv && staticHelpDiv) {
                        dynamicHelpDiv.innerHTML = dynamicHelp;
                        dynamicHelpDiv.style.display = 'block';
                        staticHelpDiv.style.display = 'none';
                    }
                }
            });
        } catch (error) {
            console.error("Dynamic help update error:", error);
        }
    }

    evaluateTemplate(template) {
        if (!template) return '';

        try {
            const context = { ...this.state.values };

            // Add calculated fields to the context
            try {
                const calculatedFields = this.calculateDynamicFields(this.state.values);
                Object.assign(context, calculatedFields);
            } catch (error) {
                console.warn('Error calculating fields for template evaluation:', error);
            }

            // Add mathematical functions
            context.min = Math.min;
            context.max = Math.max;
            context.abs = Math.abs;
            context.round = Math.round;
            context.Math = Math; // Add Math object for Math.min, Math.max, etc.

            // Process template with {expression} patterns
            let result = template;

            // Replace {expression} patterns
            result = result.replace(/\{([^}]+)\}/g, (match, expression) => {
                try {
                    // Handle fallback values with || operator
                    if (expression.includes('||')) {
                        const parts = expression.split('||').map(p => p.trim());
                        for (const part of parts) {
                            try {
                                // Clean up the expression for JavaScript evaluation
                                const cleanPart = this.cleanExpressionForJS(part);
                                const func = new Function(...Object.keys(context), `return ${cleanPart};`);
                                const value = func(...Object.values(context));
                                if (value !== undefined && value !== null && value !== '') {
                                    return String(value);
                                }
                            } catch (e) {
                                // Continue to next fallback
                            }
                        }
                        return parts[parts.length - 1].replace(/['"]/g, ''); // Return last fallback as literal, remove quotes
                    }

                    // Regular expression evaluation
                    const cleanExpression = this.cleanExpressionForJS(expression);
                    const func = new Function(...Object.keys(context), `return ${cleanExpression};`);
                    const result = func(...Object.values(context));
                    return String(result);
                } catch (e) {
                    console.warn(`Error evaluating expression: ${expression}`, e);
                    return `[${expression}]`;
                }
            });

            return result;
        } catch (error) {
            console.error(`Error evaluating template: ${template}`, error);
            return template;
        }
    }

    cleanExpressionForJS(expression) {
        // Convert Python-style operators to JavaScript
        return expression
            .replace(/\band\b/g, '&&')
            .replace(/\bor\b/g, '||')
            .replace(/\bnot\b/g, '!')
            .replace(/\bTrue\b/g, 'true')
            .replace(/\bFalse\b/g, 'false')
            .replace(/\bNone\b/g, 'null');
    }

    generateDynamicLabel(field) {
        if (!field.dynamic_label_template) {
            return field.name;
        }

        try {
            const context = { ...this.state.values };

            // Add mathematical functions
            context.min = Math.min;
            context.max = Math.max;
            context.abs = Math.abs;
            context.round = Math.round;

            // Add field-specific context
            context.field_name = field.technical_name;
            context.field_label = field.name;

            // Process template with {expression} patterns
            let template = field.dynamic_label_template;

            // Replace {expression} patterns
            template = template.replace(/\{([^}]+)\}/g, (match, expression) => {
                try {
                    // Create a safe evaluation function
                    const func = new Function(...Object.keys(context), `return ${expression};`);
                    const result = func(...Object.values(context));
                    return String(result);
                } catch (e) {
                    console.warn(`Error evaluating dynamic label expression: ${expression}`, e);
                    return `[Error: ${expression}]`;
                }
            });

            return template;
        } catch (error) {
            console.error(`Error generating dynamic label for ${field.name}:`, error);
            return field.name;
        }
    }

    generateDynamicHelp(field) {
        if (!field.dynamic_help_template) {
            return field.help_text || '';
        }

        try {
            const context = { ...this.state.values };

            // Add calculated fields to the context
            try {
                const calculatedFields = this.calculateDynamicFields(this.state.values);
                Object.assign(context, calculatedFields);
            } catch (error) {
                console.warn('Error calculating fields for dynamic help evaluation:', error);
            }

            // Add mathematical functions
            context.min = Math.min;
            context.max = Math.max;
            context.abs = Math.abs;
            context.round = Math.round;

            // Add field-specific context
            context.field_name = field.technical_name;
            context.field_label = field.name;

            // Process template with {expression} patterns
            let template = field.dynamic_help_template;

            // Replace {expression} patterns
            template = template.replace(/\{([^}]+)\}/g, (match, expression) => {
                try {
                    // Create a safe evaluation function
                    const func = new Function(...Object.keys(context), `return ${expression};`);
                    const result = func(...Object.values(context));
                    return String(result);
                } catch (e) {
                    console.warn(`Error evaluating dynamic help expression: ${expression}`, e);
                    return `[Error: ${expression}]`;
                }
            });

            return template;
        } catch (error) {
            console.error(`Error generating dynamic help for ${field.name}:`, error);
            return field.help_text || '';
        }
    }

    updateDynamicDefaults() {
        try {
            // Update default values for fields that have dynamic defaults enabled
            for (const section of this.state.sections) {
                for (const field of section.fields) {
                    if (!field.visible) continue;

                    // Check if field has dynamic default enabled (this would come from server)
                    if (field.use_dynamic_default && field.dynamic_default_template) {
                        try {
                            const currentValue = this.state.values[field.technical_name];
                            const dynamicDefault = this.generateDynamicDefault(field);

                            // Set dynamic default if:
                            // 1. Field is empty, OR
                            // 2. Dynamic default has changed and is different from current value
                            if ((!currentValue || currentValue === '') ||
                                (dynamicDefault && dynamicDefault !== '' && dynamicDefault !== currentValue)) {

                                this.setFieldValue(field.technical_name, dynamicDefault);
                                console.debug(`Applied dynamic default '${dynamicDefault}' to field ${field.technical_name}`);
                            }
                        } catch (fieldError) {
                            console.warn(`Error processing dynamic default for field ${field.technical_name}:`, fieldError);
                            // Continue processing other fields instead of breaking the entire loop
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Critical error in updateDynamicDefaults:', error);
            // Don't re-throw to prevent breaking the entire configurator
        }
    }

    generateDynamicDefault(field) {
        if (!field.dynamic_default_template) {
            return '';
        }

        // Validate the template first
        if (!this.validateDynamicTemplate(field.dynamic_default_template)) {
            console.warn(`Invalid dynamic default template for field ${field.technical_name}: ${field.dynamic_default_template}`);
            return '';
        }

        try {
            const context = { ...this.state.values };

            // Add calculated fields to the context
            try {
                const calculatedFields = this.calculateDynamicFields(this.state.values);
                Object.assign(context, calculatedFields);
            } catch (error) {
                console.warn('Error calculating fields for dynamic default evaluation:', error);
                // Continue without calculated fields
            }

            // Add mathematical functions
            context.min = Math.min;
            context.max = Math.max;
            context.abs = Math.abs;
            context.round = Math.round;

            // Add field-specific context
            context.field_name = field.technical_name;
            context.field_label = field.name;

            // Process template with {expression} patterns
            let template = field.dynamic_default_template;

            // Replace {expression} patterns
            template = template.replace(/\{([^}]+)\}/g, (match, expression) => {
                const trimmedExpression = expression.trim();
                if (!trimmedExpression) {
                    return '';
                }

                try {
                    // Validate the individual expression
                    if (!this.validateSingleExpression(trimmedExpression)) {
                        console.warn(`Invalid expression in dynamic default: ${trimmedExpression}`);
                        return '';
                    }

                    // Handle conditional expressions (ternary operator)
                    const cleanExpression = this.cleanExpressionForJS(trimmedExpression);
                    const func = new Function(...Object.keys(context), `return ${cleanExpression};`);
                    const result = func(...Object.values(context));
                    return result !== null && result !== undefined ? String(result) : '';
                } catch (e) {
                    // Check if it's a reference error (missing variable) - this is expected during initial load
                    if (e instanceof ReferenceError) {
                        console.debug(`Missing variable in dynamic default expression: ${trimmedExpression}`, e.message);
                    } else {
                        console.warn(`Error evaluating dynamic default expression: ${trimmedExpression}`, e);
                    }
                    return '';
                }
            });

            return template;
        } catch (error) {
            console.error(`Error generating dynamic default for ${field.name}:`, error);
            return '';
        }
    }

    validateDynamicTemplate(template) {
        if (!template || typeof template !== 'string') {
            return false;
        }

        // Check for dangerous patterns
        const dangerousPatterns = [
            'eval(', 'Function(', 'setTimeout', 'setInterval',
            'document.', 'window.', 'location.', 'history.',
            'XMLHttpRequest', 'fetch(', 'import(', 'require(',
            '__proto__', 'constructor', 'prototype'
        ];

        const templateLower = template.toLowerCase();
        for (const pattern of dangerousPatterns) {
            if (templateLower.includes(pattern.toLowerCase())) {
                return false;
            }
        }

        // Check for balanced braces
        let braceCount = 0;
        for (const char of template) {
            if (char === '{') {
                braceCount++;
            } else if (char === '}') {
                braceCount--;
                if (braceCount < 0) {
                    return false;
                }
            }
        }

        return braceCount === 0;
    }

    validateSingleExpression(expression) {
        if (!expression || typeof expression !== 'string') {
            return false;
        }

        // Check for dangerous patterns
        const dangerousPatterns = [
            'eval(', 'Function(', 'setTimeout', 'setInterval',
            'document.', 'window.', 'location.', 'history.',
            'XMLHttpRequest', 'fetch(', 'import(', 'require(',
            '__proto__', 'constructor', 'prototype'
        ];

        const expressionLower = expression.toLowerCase();
        for (const pattern of dangerousPatterns) {
            if (expressionLower.includes(pattern.toLowerCase())) {
                return false;
            }
        }

        // Check for balanced parentheses
        let parenCount = 0;
        for (const char of expression) {
            if (char === '(') {
                parenCount++;
            } else if (char === ')') {
                parenCount--;
                if (parenCount < 0) {
                    return false;
                }
            }
        }

        return parenCount === 0;
    }

    validateRangeConditions(changedField, newValue) {
        try {
            for (const section of this.state.sections) {
                for (const field of section.fields) {
                    if (field.visibility_condition && this.isRangeCondition(field.visibility_condition)) {
                        if (field.visibility_condition.includes(changedField.technical_name)) {
                            field.visible = this.evaluateVisibility(field);

                            const currentFieldValue = this.state.values[field.technical_name];
                            if (currentFieldValue !== undefined && currentFieldValue !== '') {
                                const isValid = this.evaluateRangeCondition(field.visibility_condition, this.state.values);

                                if (!isValid) {
                                    this.notification.add(
                                        `${field.name} value (${currentFieldValue}) is outside the valid range based on ${changedField.name}.`,
                                        {
                                            type: "warning",
                                            title: "Validation Warning",
                                        }
                                    );
                                }
                            }
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Error validating range conditions:', error);
        }
    }

    async saveConfiguration() {
        try {
            this.state.saving = true;

            // DISABLED: Auto-clear hidden fields before saving (causes operation cost discrepancy)
            // The automatic clearing of hidden fields was causing field values to change between
            // operation cost preview and save, resulting in different operation costs.
            // This logic is disabled to maintain consistency between UI preview and save.
            /*
            const hiddenFieldsWithValues = this.state.hiddenFieldsWithValues;
            if (hiddenFieldsWithValues.length > 0) {
                console.log(`Auto-clearing ${hiddenFieldsWithValues.length} hidden fields before save`);
                for (const hiddenField of hiddenFieldsWithValues) {
                    this.clearField(hiddenField.technical_name);
                }
                // Update visibility after clearing
                this.updateVisibilityAll(); // Full update after clearing hidden fields
            }
            */

            const productId = this.params.product_id;
            const templateId = this.params.template_id || this.params.matrix_id;
            const orderLineId = this.params.order_line_id;
            const configId = this.params.config_id;

            if (!templateId) {
                throw new Error("Missing required parameter: template_id");
            }

            if (!productId && this.state.template && this.state.template.product_id) {
                this.params.product_id = this.state.template.product_id;
            }

            if (Object.keys(this.state.values).length === 0) {
                throw new Error("Configuration is empty. Please answer at least one question.");
            }

            const validationErrors = this.validateAllRangeConditions();
            if (validationErrors.length > 0) {
                const errorMessage = "Please fix the following validation errors:\n" + validationErrors.join('\n');
                throw new Error(errorMessage);
            }

            const configData = {
                product_id: productId,
                template_id: templateId,
                config_data: JSON.stringify(this.state.values),
            };

            if (orderLineId) {
                configData.sale_order_line_id = orderLineId;
            }

            let configurationId;

            if (configId) {
                await this.orm.write("config.matrix.configuration", [configId], configData);
                configurationId = configId;
            } else {
                configurationId = await this.orm.create("config.matrix.configuration", [configData]);
            }

            // Store configuration ID globally for other components to access
            window.configurationId = configurationId;
            this.params.config_id = configurationId;

            // Dispatch event for other components to update their config ID
            document.dispatchEvent(new CustomEvent('configurator:config-id-changed', {
                detail: { configId: configurationId }
            }));

            await this.orm.call("config.matrix.configuration", "generate_bom", [configurationId]);
            await this.orm.call("config.matrix.configuration", "calculate_price", [configurationId]);

            if (orderLineId) {
                await this.orm.call("config.matrix.configuration", "apply_to_sale_order_line", [configurationId]);
            }

            this.notification.add("Configuration saved successfully", {
                type: "success",
                title: "Configuration Saved",
            });

            this.state.dirty = false;
            this.state.saving = false;

            this.env.services.action.doAction({
                type: "ir.actions.act_window_close",
            });

        } catch (error) {
            this.state.saving = false;

            let errorMessage = error.message || "Failed to save configuration";
            let errorTitle = "Configuration Error";

            if (errorMessage.includes("Missing required")) {
                errorTitle = "Missing Information";
            } else if (errorMessage.includes("Configuration is empty")) {
                errorTitle = "Empty Configuration";
            } else if (errorMessage.includes("validation errors")) {
                errorTitle = "Validation Errors";
            }

            this.notification.add(errorMessage, {
                type: "danger",
                sticky: true,
                title: errorTitle,
            });

            console.error("Configuration save error:", error);
        }
    }

    validateAllRangeConditions() {
        const errors = [];

        try {
            for (const section of this.state.sections) {
                for (const field of section.fields) {
                    if (!field.visible) continue;

                    if (field.visibility_condition && this.isRangeCondition(field.visibility_condition)) {
                        const fieldValue = this.state.values[field.technical_name];

                        if (fieldValue !== undefined && fieldValue !== '') {
                            const isValid = this.evaluateRangeCondition(field.visibility_condition, this.state.values);

                            if (!isValid) {
                                errors.push(`${field.name}: Value ${fieldValue} is outside the valid range.`);
                            }
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Error validating range conditions:', error);
            errors.push('Error occurred during validation. Please check your inputs.');
        }

        return errors;
    }

    cancelConfiguration() {
        if (this.state.saving) {
            this.notification.add(
                "Cannot cancel while saving configuration",
                {
                    type: "warning",
                    title: "Save in Progress",
                }
            );
            return;
        }

        if (this.state.dirty) {
            if (!confirm("You have unsaved changes. Are you sure you want to cancel?")) {
                return;
            }
        }

        this.env.services.action.doAction({
            type: "ir.actions.act_window_close",
        });
    }

    async loadCalculatedFields(templateId) {
        try {
            const result = await this.orm.call(
                "config.matrix.calculated.field",
                "get_calculated_fields_for_template",
                [templateId]
            );

            if (result && Array.isArray(result)) {
                // Sort by sequence to ensure proper dependency order
                this.calculatedFieldsDefinitions = result
                    .map(field => ({
                        name: field.name,
                        formula: field.formula,
                        description: field.description || '',
                        sequence: field.sequence || 10,
                        category: field.category || 'basic'
                    }))
                    .sort((a, b) => a.sequence - b.sequence);

            } else {
                this.calculatedFieldsDefinitions = [];
            }
        } catch (error) {
            console.error(`Error loading calculated fields:`, error);
            this.calculatedFieldsDefinitions = [];
        }
    }

    calculateDynamicFields(fieldValues) {
        // Create a hash of the field values to check if we need to recalculate
        const currentHash = JSON.stringify(fieldValues);

        // Return cached results if field values haven't changed
        if (this.calculatedFieldsCache && this.lastFieldValuesHash === currentHash) {
            return this.calculatedFieldsCache;
        }

        const results = {};

        // Add basic fallback calculations
        this.calculateBasicFields(fieldValues, results);

        if (this.calculatedFieldsDefinitions && this.calculatedFieldsDefinitions.length > 0) {
            // Create evaluation context
            const context = { ...fieldValues, ...results };

            // Add math functions
            context.Math = Math;
            context.min = Math.min;
            context.max = Math.max;
            context.abs = Math.abs;
            context.round = Math.round;
            context.ceil = Math.ceil;
            context.floor = Math.floor;
            context.sqrt = Math.sqrt;

            // Add parseFloat function for JavaScript compatibility
            context.parseFloat = (value) => {
                if (value === null || value === undefined || value === '') return 0;
                try {
                    return parseFloat(String(value));
                } catch {
                    return 0;
                }
            };

            // Calculate server-defined fields in sequence order
            this.calculatedFieldsDefinitions.forEach((calcField, index) => {
                try {
                    // Update context with latest results
                    Object.assign(context, results);

                    // Evaluate the formula
                    const func = new Function(...Object.keys(context), `return ${calcField.formula};`);
                    const result = func(...Object.values(context));
                    results[calcField.name] = result;

                } catch (error) {
                    console.error(`Error calculating ${calcField.name}:`, error);
                    results[calcField.name] = null;
                }
            });
        }

        // Cache the results
        this.calculatedFieldsCache = results;
        this.lastFieldValuesHash = currentHash;

        return results;
    }

    calculateBasicFields(fieldValues, results) {
        // Basic fallback calculations for backward compatibility
        // This ensures basic fields exist even if server-defined fields fail

        // Add any basic calculations here if needed
        // For now, this is just a placeholder
    }

    nextSection() {
        if (this.state.currentSection < this.state.sections.length - 1) {
            this.state.currentSection++;

            const sectionContent = document.querySelector('.o_config_matrix_section_content');
            if (sectionContent) {
                sectionContent.scrollTop = 0;
            }
        }
    }

    prevSection() {
        if (this.state.currentSection > 0) {
            this.state.currentSection--;

            const sectionContent = document.querySelector('.o_config_matrix_section_content');
            if (sectionContent) {
                sectionContent.scrollTop = 0;
            }
        }
    }

    // PERFORMANCE: Comprehensive testing methods
    runComprehensivePerformanceTest() {
        console.log('='.repeat(60));
        console.log('[PERF TEST] Starting comprehensive performance test...');
        console.log('='.repeat(60));

        // Reset metrics
        this.performanceMetrics = {
            fieldChanges: 0,
            evaluations: 0,
            cacheHits: 0,
            cacheMisses: 0,
            totalTime: 0,
            dependencyUpdates: 0
        };

        const testResults = {
            singleFieldChanges: [],
            dependencyUpdates: [],
            cachePerformance: {},
            memoryUsage: {}
        };

        // Test 1: Single field changes
        console.log('[PERF TEST] Test 1: Single field change performance');
        const testFields = Array.from(this.allFields.keys()).slice(0, 10);

        testFields.forEach((fieldName, index) => {
            const field = this.allFields.get(fieldName);
            if (field) {
                const startTime = performance.now();
                this.onFieldChange(field, `test_value_${index}`);
                const duration = performance.now() - startTime;
                testResults.singleFieldChanges.push({
                    field: fieldName,
                    duration: duration
                });
                console.log(`  Field ${fieldName}: ${duration.toFixed(2)}ms`);
            }
        });

        // Test 2: Dependency chain performance
        console.log('[PERF TEST] Test 2: Dependency chain performance');
        const fieldsWithDependents = Array.from(this.dependencyGraph.keys()).slice(0, 5);

        fieldsWithDependents.forEach(fieldName => {
            const dependents = this.getDependentFields(fieldName);
            if (dependents.length > 0) {
                const startTime = performance.now();
                this.updateVisibility(dependents);
                const duration = performance.now() - startTime;
                testResults.dependencyUpdates.push({
                    field: fieldName,
                    dependentCount: dependents.length,
                    duration: duration
                });
                console.log(`  Field ${fieldName} (${dependents.length} dependents): ${duration.toFixed(2)}ms`);
            }
        });

        // Test 3: Cache performance
        console.log('[PERF TEST] Test 3: Cache performance');
        const cacheHitRate = this.performanceMetrics.cacheHits /
            (this.performanceMetrics.cacheHits + this.performanceMetrics.cacheMisses) * 100;

        testResults.cachePerformance = {
            hitRate: cacheHitRate,
            cacheSize: this.expressionCache.size,
            hits: this.performanceMetrics.cacheHits,
            misses: this.performanceMetrics.cacheMisses
        };

        console.log(`  Cache hit rate: ${cacheHitRate.toFixed(1)}%`);
        console.log(`  Cache size: ${this.expressionCache.size} entries`);

        // Test 4: Memory usage
        console.log('[PERF TEST] Test 4: Memory usage');
        testResults.memoryUsage = {
            current: this.getMemoryUsage(),
            fieldAccessTracking: this.fieldAccessTimes.size
        };

        console.log(`  Memory usage: ${this.getMemoryUsage()}`);
        console.log(`  Tracked field accesses: ${this.fieldAccessTimes.size}`);

        // Summary
        console.log('='.repeat(60));
        console.log('[PERF TEST] Test Summary:');
        console.log('='.repeat(60));

        const avgSingleChange = testResults.singleFieldChanges.reduce((sum, test) => sum + test.duration, 0) /
            testResults.singleFieldChanges.length;

        const avgDependencyUpdate = testResults.dependencyUpdates.reduce((sum, test) => sum + test.duration, 0) /
            Math.max(testResults.dependencyUpdates.length, 1);

        console.log(`Average single field change: ${avgSingleChange.toFixed(2)}ms`);
        console.log(`Average dependency update: ${avgDependencyUpdate.toFixed(2)}ms`);
        console.log(`Total evaluations: ${this.performanceMetrics.evaluations}`);
        console.log(`Total dependency updates: ${this.performanceMetrics.dependencyUpdates}`);

        this.logPerformanceMetrics();

        return testResults;
    }

    runStressTest() {
        console.log('[STRESS TEST] Starting stress test with 100 rapid field changes...');

        const startTime = performance.now();
        const testFields = Array.from(this.allFields.keys()).slice(0, 50);

        // Simulate rapid field changes
        for (let i = 0; i < 100; i++) {
            const randomField = testFields[Math.floor(Math.random() * testFields.length)];
            const field = this.allFields.get(randomField);
            if (field) {
                this.onFieldChange(field, `stress_test_${i}`);
            }
        }

        const duration = performance.now() - startTime;
        console.log(`[STRESS TEST] Completed 100 field changes in ${duration.toFixed(2)}ms`);
        console.log(`[STRESS TEST] Average per change: ${(duration / 100).toFixed(2)}ms`);

        this.logPerformanceMetrics();
    }

    validateOptimizations() {
        console.log('[VALIDATION] Validating performance optimizations and core functionality...');

        const validations = {
            dependencyTracking: this.dependencyGraph.size > 0,
            expressionCaching: this.expressionCache instanceof Map,
            memoryManagement: this.memoryCleanupInterval !== null,
            performanceMonitoring: this.performanceMetrics !== null,
            debouncedFunctions: typeof this.debouncedUpdateVisibility === 'function',
            // CRITICAL: Validate core functionality is preserved
            calculatedFieldsMethod: typeof this.calculateDynamicFields === 'function',
            dynamicLabelsMethod: typeof this.updateDynamicLabels === 'function',
            dynamicDefaultsMethod: typeof this.updateDynamicDefaults === 'function',
            visibilityEvaluationMethod: typeof this.evaluateVisibility === 'function',
            fieldChangeHandlerMethod: typeof this.onFieldChange === 'function'
        };

        console.log('[VALIDATION] Optimization status:');
        Object.entries(validations).forEach(([feature, enabled]) => {
            const status = enabled ? '✅ ENABLED' : '❌ DISABLED';
            const critical = ['calculatedFieldsMethod', 'dynamicLabelsMethod', 'dynamicDefaultsMethod', 'visibilityEvaluationMethod', 'fieldChangeHandlerMethod'].includes(feature);
            console.log(`  ${feature}: ${status}${critical ? ' (CRITICAL)' : ''}`);
        });

        // Test critical functionality
        const functionalityTests = this.validateCriticalFunctionality();

        const allOptimizationsEnabled = Object.values(validations).every(v => v);
        const allFunctionalityWorking = Object.values(functionalityTests).every(v => v);

        console.log(`[VALIDATION] Optimizations: ${allOptimizationsEnabled ? '✅ ALL ACTIVE' : '⚠️ SOME MISSING'}`);
        console.log(`[VALIDATION] Core Functionality: ${allFunctionalityWorking ? '✅ ALL WORKING' : '❌ SOME BROKEN'}`);

        return { optimizations: validations, functionality: functionalityTests };
    }

    validateCriticalFunctionality() {
        console.log('[VALIDATION] Testing critical functionality...');

        const tests = {
            calculatedFieldsWorking: false,
            visibilityEvaluationWorking: false,
            dynamicLabelsWorking: false,
            cacheIntegrityWorking: false
        };

        try {
            // Test calculated fields
            const testValues = { test_field: 100 };
            const calculatedResult = this.calculateDynamicFields(testValues);
            tests.calculatedFieldsWorking = typeof calculatedResult === 'object';
            console.log(`  Calculated fields: ${tests.calculatedFieldsWorking ? '✅' : '❌'}`);
        } catch (error) {
            console.error('  Calculated fields test failed:', error);
        }

        try {
            // Test visibility evaluation with a mock field
            const mockField = {
                technical_name: 'test_field',
                visibility_condition: 'true',
                name: 'Test Field'
            };
            const visibilityResult = this.evaluateVisibility(mockField);
            tests.visibilityEvaluationWorking = typeof visibilityResult === 'boolean';
            console.log(`  Visibility evaluation: ${tests.visibilityEvaluationWorking ? '✅' : '❌'}`);
        } catch (error) {
            console.error('  Visibility evaluation test failed:', error);
        }

        try {
            // Test dynamic labels method exists and can be called
            this.updateDynamicLabels();
            tests.dynamicLabelsWorking = true;
            console.log(`  Dynamic labels: ✅`);
        } catch (error) {
            console.error('  Dynamic labels test failed:', error);
        }

        try {
            // Test cache integrity
            const cacheSize = this.expressionCache.size;
            tests.cacheIntegrityWorking = typeof cacheSize === 'number';
            console.log(`  Cache integrity: ${tests.cacheIntegrityWorking ? '✅' : '❌'}`);
        } catch (error) {
            console.error('  Cache integrity test failed:', error);
        }

        return tests;
    }
}

ConfigMatrixConfigurator.template = "canbrax_configmatrix.Configurator";
ConfigMatrixConfigurator.props = {
    ...standardWidgetProps,
    params: { type: Object, optional: true },
};

registry.category("actions").add("config_matrix", ConfigMatrixConfigurator);
registry.category("client_actions").add("config_matrix", ConfigMatrixConfigurator);
registry.category("legacy_client_actions").add("config_matrix", ConfigMatrixConfigurator);

export default ConfigMatrixConfigurator;