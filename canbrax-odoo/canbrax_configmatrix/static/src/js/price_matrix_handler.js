/**
 * Price Matrix Handler - v2.0
 *
 * This script handles price matrix functionality for the configurator.
 * It automatically detects height and width fields and gets prices from price matrices.
 * Now includes display functionality similar to operation costs handler.
 */

class PriceMatrixHandler {
    constructor() {
        this.salePrices = [];
        this.totalPrice = 0.0;
        this.templateId = null;
        this.currentValues = {};
        this.configurationPriceElement = null;
        this.fieldValues = {};

        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            this.init();
        }
    }

    init() {
        // Get the configuration price element
        this.configurationPriceElement = document.getElementById('configuration-price-matrix');
        if (!this.configurationPriceElement) {
            console.error('[PRICE_MATRIX] Configuration price element not found');
            return;
        }

        // Get the template ID
        this.templateId = this.getTemplateId();
        if (!this.templateId) {
            console.error('[PRICE_MATRIX] Template ID not found');
            return;
        }

        // Set up event listeners
        this.setupEventListeners();

        console.log('[PRICE_MATRIX] Price matrix handler initialized');
    }

    getTemplateId() {
        const templateIdInput = document.querySelector('input[name="template_id"]');
        if (templateIdInput) {
            return parseInt(templateIdInput.value) || 0;
        }

        const urlParams = new URLSearchParams(window.location.search);
        const templateId = urlParams.get('template_id') || urlParams.get('matrix_id');
        if (templateId) {
            return parseInt(templateId);
        }

        return null;
    }

    setupEventListeners() {
    // Find all configuration fields and add event listeners
        const configFields = document.querySelectorAll('.config-field');

        configFields.forEach(field => {
            const fieldId = field.getAttribute('data-field-id');
            const technicalName = field.getAttribute('data-technical-name');
            const fieldType = field.getAttribute('data-field-type');

            if (!fieldId || !technicalName) {
                console.warn('[PRICE_MATRIX] Field missing ID or technical name:', field);
                return;
            }

            // Add change event listener
            field.addEventListener('change', async () => {
                // Get the field value
                let value = this.getFieldValue(field, fieldType);

                // Update the field value in the global object
                this.fieldValues[technicalName] = value;

                // Update price from matrix
                await this.refreshSalePrices();
            });
        });

        // Listen for configurator events
        document.addEventListener('configurator:field-changed', (event) => {
            this.onFieldChange(event);
        });

        document.addEventListener('configurator:values-updated', (event) => {
            this.currentValues = event.detail.values || {};
            this.refreshSalePrices();
        });
    }

    onFieldChange(event) {
        // Debounce the refresh to avoid too many calls
        clearTimeout(this.refreshTimeout);
        this.refreshTimeout = setTimeout(() => {
            this.refreshSalePrices();
        }, 300);
    }

    // Get the value of a field based on its type
    getFieldValue(field, fieldType) {
        switch (fieldType) {
            case 'boolean':
                return field.checked;
            case 'number':
                return parseFloat(field.value) || 0;
            case 'selection':
                // Check if this is an unselected option
                if (!field.value || field.value === '-- Select an option --' || field.selectedIndex === 0) {
                    return '';
                }
                return field.value;
            default:
                return field.value;
        }
    }

    // Collect current values of all fields
    collectFieldValues() {
        // Clear existing values
        this.fieldValues = {};

        // Find all configuration fields
        const configFields = document.querySelectorAll('.config-field');

        // Collect values from each field
        configFields.forEach(field => {
            // Get field information
            const technicalName = field.getAttribute('data-technical-name');
            const fieldType = field.getAttribute('data-field-type');

            if (!technicalName) {
                return;
            }

            // Get the field value
            const value = this.getFieldValue(field, fieldType);

            // Store the value
            this.fieldValues[technicalName] = value;
        });
    }

    // Get price from price matrix
    async getPriceFromMatrix() {
        try {
            // Generate configuration data
            const configData = JSON.stringify(this.fieldValues);

            const response = await fetch('/config_matrix/get_price_from_matrix', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    jsonrpc: "2.0",
                    method: "call",
                    params: {
                        template_id: this.templateId,
                        config_data: configData
                    },
                    id: new Date().getTime()
                })
            });

            const data = await response.json();

            if (data.result && data.result.success) {
                // Store sale prices breakdown
                this.salePrices = data.result.sale_prices || data.result.breakdown || [];
                this.totalPrice = data.result.price || 0;
                return data.result.price;
            } else {
                console.warn('[PRICE_MATRIX] No price found from matrix:', data.result?.error || 'Unknown error');
                this.salePrices = [];
                this.totalPrice = 0;
                return 0;
            }
        } catch (error) {
            console.error('[PRICE_MATRIX] Error getting price from matrix:', error);
            this.salePrices = [];
            this.totalPrice = 0;
            return 0;
        }
    }

    // Refresh sale prices (similar to refreshOperationCosts)
    async refreshSalePrices() {
        if (!this.templateId) {
            console.warn('[PRICE_MATRIX] No template ID available');
            return false;
        }

        try {
            // Collect current field values
            this.collectFieldValues();

            // Check if we have any field values
            const filledFields = Object.values(this.fieldValues).filter(v => v && v !== '').length;
            if (filledFields < 2) {
                console.log('[PRICE_MATRIX] Not enough fields filled to get price from matrix');
                this.salePrices = [];
                this.totalPrice = 0;
                this.updateDisplay();
                return false;
            }

            console.log('[PRICE_MATRIX] Getting price from matrix with configuration data');

            const matrixPrice = await this.getPriceFromMatrix();

            if (matrixPrice !== null && matrixPrice > 0) {
            // Update the configuration price with matrix price
                if (this.configurationPriceElement) {
                    this.configurationPriceElement.textContent = this.formatCurrency(matrixPrice);
                    // console.log(`[PRICE_MATRIX] Updated price to: ${this.formatCurrency(matrixPrice)}`);
                }

                // Update hidden input fields
                const fieldPriceMatrix = document.getElementById('field-price-matrix');
                if (fieldPriceMatrix) {
                    fieldPriceMatrix.value = matrixPrice;
                    // console.log(`[PRICE_MATRIX] Updated field-price-matrix input: ${matrixPrice}`);
                }

                // Update the display
                this.updateDisplay();

                return true; // Indicate that we found a matrix price
            } else {
                // If no matrix price found, clear the display
                console.log('[PRICE_MATRIX] No matrix price found');
                this.salePrices = [];
                this.totalPrice = 0;
                this.updateDisplay();
                return false;
            }
        } catch (error) {
            console.error('[PRICE_MATRIX] Error refreshing sale prices:', error);
            this.salePrices = [];
            this.totalPrice = 0;
            this.updateDisplay();
            return false;
        }
    }

    // Update price from matrix (for compatibility)
    async updatePriceFromMatrix() {
        return await this.refreshSalePrices();
    }

    // Update the display (similar to operation costs)
    updateDisplay() {
        const tableBody = document.getElementById('sale-prices-table-body');
        const totalElement = document.getElementById('configuration-sale-prices');

        if (!tableBody || !totalElement) {
            console.warn('[PRICE_MATRIX] Display elements not found');
            return;
        }

        // Clear existing content
        tableBody.innerHTML = '';

        if (this.salePrices.length === 0) {
            // Show empty state
            tableBody.innerHTML = `
                <tr>
                    <td colspan="2" class="text-center text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        No sale prices calculated yet
                    </td>
                </tr>
            `;
            totalElement.textContent = '$0.00';
            return;
        }

        // Add sale price rows
        this.salePrices.forEach(salePrice => {
            const row = document.createElement('tr');

            // Create dimensions info
            let dimensionsInfo = '';
            if (salePrice.dimensions) {
                dimensionsInfo = `<br><small class="text-muted">H: ${salePrice.dimensions.height}" × W: ${salePrice.dimensions.width}"</small>`;
            }

            row.innerHTML = `
                <td>
                    <div class="d-flex align-items-center">
                        <span class="fw-bold">${this.escapeHtml(salePrice.matrix_name || 'Price Matrix')}</span>
                        ${dimensionsInfo}
                    </div>
                </td>
                <td class="text-end">
                    <span class="fw-bold text-success">$${salePrice.price.toFixed(2)}</span>
                </td>
            `;
            tableBody.appendChild(row);
        });

        // Update total
        totalElement.textContent = `$${this.totalPrice.toFixed(2)}`;

        // Update hidden input field for form submission
        const fieldPriceMatrix = document.getElementById('field-price-matrix');
        if (fieldPriceMatrix) {
            fieldPriceMatrix.value = this.totalPrice.toFixed(2);
            // console.log(`[PRICE_MATRIX] Updated field-price-matrix input: ${this.totalPrice}`);
        }
    }

    // Format currency
    formatCurrency(amount) {
        return '$' + parseFloat(amount).toFixed(2);
    }

    // Escape HTML
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Public methods
    getTotalPrice() {
        return this.totalPrice;
    }

    getSalePrices() {
        return this.salePrices;
    }

    // Manual refresh method
    refresh() {
        this.refreshSalePrices();
    }
}

// Create global instance when script loads
window.priceMatrixHandler = new PriceMatrixHandler();

// Global functions for compatibility
window.refreshSalePrices = () => {
    if (window.priceMatrixHandler) {
        window.priceMatrixHandler.refresh();
    } else {
        console.warn('[PRICE_MATRIX] Price matrix handler not available');
    }
};

console.log('[PRICE_MATRIX] Global functions registered: refreshSalePrices()');

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PriceMatrixHandler;
}