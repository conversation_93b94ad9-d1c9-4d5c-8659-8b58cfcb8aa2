# -*- coding: utf-8 -*-

from odoo import http
from odoo.http import request, Response
import json
import logging


_logger = logging.getLogger(__name__)

class ConfigMatrixConfigurationController(http.Controller):
    """Controller for configuration CRUD operations"""

    @http.route('/config_matrix/save_config', type='http', auth='public', website=True, methods=['POST'])
    def save_config(self, **kw):
        """Save configuration and redirect back to sales order or shop cart"""

        _logger.info(f"[SAVE_CONFIG_DEBUG] ===== SAVE_CONFIG METHOD CALLED =====")
        _logger.info(f"[SAVE_CONFIG_DEBUG] ALL REQUEST PARAMS: {dict(kw)}")

        template_id = int(kw.get('template_id') or kw.get('matrix_id') or 0)
        product_id = int(kw.get('product_id') or 0)
        order_line_id = int(kw.get('order_line_id') or 0)
        config_id = int(kw.get('config_id') or 0)

        # Get the calculated total from the frontend
        calculated_total = kw.get('total_price') or kw.get('calculated_total')
        if calculated_total:
            try:
                calculated_total = float(calculated_total)
                _logger.info(f"[SAVE_CONFIG_DEBUG] Frontend calculated total: ${calculated_total}")
            except (ValueError, TypeError):
                calculated_total = None
                _logger.info(f"[SAVE_CONFIG_DEBUG] Could not parse calculated total: {kw.get('total_price')} or {kw.get('calculated_total')}")

        _logger.info(f"[SAVE_CONFIG_DEBUG] Parsed params - template_id: {template_id}, product_id: {product_id}, order_line_id: {order_line_id}, config_id: {config_id}")
        ajax_save = kw.get('ajax_save') == 'true'

        # Get the template to ensure it exists
        template = request.env['config.matrix.template'].sudo().browse(template_id)
        if not template.exists():
            if ajax_save:
                return json.dumps({'success': False, 'error': 'Template not found'})
            return request.redirect('/shop/cart')

        # Get all fields from the template
        all_fields = []
        for section in template.section_ids:
            all_fields.extend(section.field_ids)

        # Collect ALL field values without filtering
        values = {}
        for key, value in kw.items():
            if key.startswith('field_'):
                field_id = key.replace('field_', '')
                # Include ALL fields, regardless of visibility condition
                values[field_id] = value
                _logger.info(f"Including field ID: {field_id} with value: {value}")

        try:
            # Use sudo() for website users to avoid permission issues
            ConfigMatrix = request.env['config.matrix.configuration'].sudo()

            # Get the template to ensure it exists
            template = request.env['config.matrix.template'].sudo().browse(template_id)
            if not template.exists():
                if ajax_save:
                    return json.dumps({'success': False, 'error': 'Template not found'})
                return request.redirect('/shop/cart')

            # Get the product to ensure it exists
            product = request.env['product.product'].sudo().browse(product_id)
            if not product.exists():
                if ajax_save:
                    return json.dumps({'success': False, 'error': 'Product not found'})
                return request.redirect('/shop/cart')

            if config_id:
                # Update existing configuration
                config = ConfigMatrix.browse(config_id)
                if not config.exists():
                    if ajax_save:
                        return json.dumps({'success': False, 'error': 'Configuration not found'})
                    return request.redirect('/shop/cart')

                config.write({
                    'config_data': json.dumps(values),
                })

                # Save calculated fields for mesh hunting
                config.save_calculated_fields_to_config_data()
            else:
                # Create new configuration
                create_vals = {
                    'template_id': template_id,
                    'product_id': product_id,
                    'config_data': json.dumps(values),
                }

                # Add order line reference if provided
                if order_line_id:
                    create_vals['sale_order_line_id'] = order_line_id

                config = ConfigMatrix.create(create_vals)
                config_id = config.id

                # Save calculated fields for mesh hunting
                config.save_calculated_fields_to_config_data()

            # Update order line if provided
            if order_line_id:
                order_line = request.env['sale.order.line'].sudo().browse(order_line_id)
                if order_line.exists():
                    config = request.env['config.matrix.configuration'].sudo().browse(config_id)
                    if config.exists():
                        # Get quantity multiplier from configuration values
                        quantity_multiplier = self._get_quantity_multiplier(values)
                        _logger.info(f"[SAVE_CONFIG_DEBUG] Found quantity multiplier: {quantity_multiplier}")
                        
                        # SIMPLE FIX: Always calculate price to ensure it's up to date
                        config.calculate_price()

                        # Now use the total aggregated price from configuration
                        final_price = config.price_total
                        _logger.info(f"[SAVE_CONFIG_DEBUG] Using total: ${final_price}")
                        _logger.info(f"[SAVE_CONFIG_DEBUG] Breakdown - Matrix: ${config.price_matrix}, Components: ${config.price_component}, Operations: ${config.price_operations}")

                        # Fallback to calculated total if price_total is still not set
                        if final_price <= 0:
                            final_price = calculated_total if calculated_total else order_line.price_unit
                            _logger.info(f"[SAVE_CONFIG_DEBUG] Fallback price: ${final_price}")
                    else:
                        final_price = calculated_total if calculated_total else order_line.price_unit
                        quantity_multiplier = 1.0
                        _logger.info(f"[SAVE_CONFIG_DEBUG] No config found, using fallback: ${final_price}")

                    order_line.write({
                        'config_id': config_id,
                        'is_configured': True,
                        'price_unit': final_price,
                        'product_uom_qty': quantity_multiplier,  # Set quantity to multiplier value
                    })
                    _logger.info(f"[SAVE_CONFIG_DEBUG] FINAL ORDER LINE PRICE SET TO: ${final_price}, QUANTITY SET TO: {quantity_multiplier}")

                    # Force recompute of configuration_summary
                    order_line._compute_configuration_summary()

            # If this is an AJAX save, return JSON response
            if ajax_save:
                response = json.dumps({
                    'success': True,
                    'config_id': config_id,
                    'message': 'Configuration saved successfully'
                })
                return Response(
                    response,
                    content_type='application/json',
                    headers=[('Content-Type', 'application/json')]
                )

            # Handle different redirect scenarios
            return self._handle_save_redirect(kw, order_line_id)

        except Exception as e:
            _logger.error(f"Error saving configuration: {str(e)}")
            # Rollback the transaction to prevent database issues
            try:
                request.env.cr.rollback()
            except Exception:
                pass  # Ignore rollback errors
            return self._handle_save_error(e, kw, ajax_save, template_id, config_id, values)

    def _handle_save_redirect(self, kw, order_line_id):
        """Handle redirect after successful save"""
        # Check contexts from the form
        website_context = kw.get('website_context')
        portal_context = kw.get('portal_context')

        # Handle different redirect scenarios
        if portal_context:
            # For portal context, redirect back to My Configurations
            return request.redirect('/my/configurations')
        elif website_context:
            # For ecommerce context, redirect to cart
            return request.redirect('/shop/cart')
        else:
            # For backend users, redirect to sales order
            if order_line_id:
                order_line = request.env['sale.order.line'].sudo().browse(order_line_id)
                if order_line.exists() and order_line.order_id:
                    return request.redirect(f'/web#id={order_line.order_id.id}&view_type=form&model=sale.order')

            # Default redirect for backend
            return request.redirect('/web')

    def _handle_save_error(self, error, kw, ajax_save, template_id, config_id, values):
        """Handle errors during save operation"""
        # If this is an AJAX save, return JSON error response
        if ajax_save:
            return json.dumps({
                'success': False,
                'error': str(error),
                'message': "Failed to save configuration"
            })

        # Check if this is explicitly a website context from the form
        website_context = kw.get('website_context')

        # For website users, provide a more user-friendly error page
        if website_context:
            return self._create_error_response(f"We couldn't save your configuration. Please try again or contact support. Error details: {str(error)}")
        else:
            # For backend users, show the detailed error
            return request.render('canbrax_configmatrix.configurator_template', {
                'error': f"Failed to save configuration: {str(error)}",
                'template': request.env['config.matrix.template'].sudo().browse(template_id),
                'config_values': values,
                'params': kw,
                'config_id': config_id
            })

    @http.route('/config_matrix/save_configuration', type='json', auth='user')
    def save_configuration(self, **kw):
        """Save configuration (JSON API)"""
        _logger.info(f"[OPERATION_COSTS_BUG] ===== SAVE CONFIGURATION CALLED =====")
        try:
            template_id = kw.get('template_id')
            product_id = kw.get('product_id')
            order_line_id = kw.get('order_line_id')
            _logger.info(f"[OPERATION_COSTS_BUG] Save config - Template ID: {template_id}, Product ID: {product_id}")
            config_id = kw.get('config_id')
            values = kw.get('values', {})

            if not template_id:
                return {'error': 'Template ID is required'}

            # Do NOT filter out any fields - include ALL fields
            filtered_values = values.copy()

            # Get the template
            template = request.env['config.matrix.template'].sudo().browse(int(template_id))
            if not template.exists():
                # If we can't find the template, use the original values to be safe
                filtered_values = values

            # Replace the original values with the filtered ones
            values = filtered_values

            # Get template - use sudo()
            template = request.env['config.matrix.template'].sudo().browse(int(template_id))
            if not template.exists():
                return {'error': 'Template not found'}

            # Create or update configuration - use sudo()
            ConfigMatrix = request.env['config.matrix.configuration'].sudo()

            price_matrix = 0
            price_component = 0
            if 'configuration_price_matrix' in kw:
                price_matrix = float(kw['configuration_price_matrix'])
            if 'configuration_price' in kw:
                price_component = float(kw['configuration_price'])

            if config_id:
                # Update existing configuration
                config = ConfigMatrix.browse(int(config_id))
                if not config.exists():
                    return {'error': 'Configuration not found'}

                # Calculate operation costs using the same method as UI (field/option mapping)
                operation_cost = 0.0
                _logger.info(f"[OPERATION_COSTS_BUG] ===== SAVE CONFIG OPERATION COST CALCULATION =====")
                try:
                    # Use the same calculation method as the UI for consistency
                    operation_result = self._calculate_operation_costs_field_option_mapping(
                        config.template_id, values, 1.0  # quantity_multiplier = 1.0 for save config
                    )

                    if operation_result.get('success'):
                        operation_cost = operation_result.get('total_cost', 0.0)
                        operation_count = len(operation_result.get('operations', []))
                        _logger.info(f"[OPERATION_COSTS_BUG] ✓ Save config operation cost: ${operation_cost} from {operation_count} operations")
                    else:
                        _logger.warning(f"[OPERATION_COSTS_BUG] ✗ Save config operation calculation failed: {operation_result.get('error')}")
                        operation_cost = 0.0

                except Exception as e:
                    _logger.warning(f"[OPERATION_COSTS_BUG] Could not calculate operation costs: {str(e)}")
                    operation_cost = 0.0

                config.write({
                    'config_data': json.dumps(values),
                    'price_matrix': price_matrix,
                    'price_component': price_component,
                    'price_operations': operation_cost,
                    'price': price_matrix + price_component,
                })

                _logger.info(f"[OPERATION_COSTS_BUG] ===== SAVE CONFIG FINAL PRICES =====")
                _logger.info(f"[OPERATION_COSTS_BUG] Price Matrix: ${price_matrix}")
                _logger.info(f"[OPERATION_COSTS_BUG] Price Component: ${price_component}")
                _logger.info(f"[OPERATION_COSTS_BUG] Price Operations: ${operation_cost}")
                _logger.info(f"[OPERATION_COSTS_BUG] Price (Matrix + Component): ${price_matrix + price_component}")

                # Force computation of price_total
                config._compute_price_total()
                _logger.info(f"[OPERATION_COSTS_BUG] Final price_total after compute: ${config.price_total}")
            else:
                # Create new configuration
                create_vals = {
                    'template_id': template_id,
                    'product_id': product_id,
                    'config_data': json.dumps(values),
                    'price_matrix': price_matrix,
                    'price_component': price_component,
                    'price': price_matrix + price_component,
                }

                # Add order line reference if provided
                if order_line_id:
                    create_vals['sale_order_line_id'] = int(order_line_id)

                config = ConfigMatrix.create(create_vals)

            # Update order line if provided - use sudo()
            if order_line_id:
                order_line = request.env['sale.order.line'].sudo().browse(int(order_line_id))
                if order_line.exists():
                    # Get quantity multiplier from configuration values
                    quantity_multiplier = self._get_quantity_multiplier(values)
                    _logger.info(f"[INITIAL_PRICING] Found quantity multiplier: {quantity_multiplier}")
                    
                    # SIMPLE FIX: Always calculate price to ensure it's up to date
                    config.calculate_price()

                    # Now use the configuration's total aggregated price
                    total_price = config.price_total
                    _logger.info(f"[INITIAL_PRICING] Using total: ${total_price}")
                    _logger.info(f"[INITIAL_PRICING] Breakdown - Matrix: ${config.price_matrix}, Components: ${config.price_component}, Operations: ${config.price_operations}")

                    # When saving configurations, always use backend calculation (base price)
                    # The multiplier will be applied to product_uom_qty in the sale order line
                    # This ensures consistency between components, operations, and sales prices
                    if total_price <= 0:
                        # Manual calculation as fallback
                        total_price = config.price_matrix + config.price_component + config.price_operations
                        _logger.warning(f"[INITIAL_PRICING] FALLBACK - Manual calculation: ${total_price}")
                    
                    # Log the price breakdown for debugging
                    _logger.info(f"[INITIAL_PRICING] Using backend calculated base price (multiplier will be applied to product_uom_qty)")

                    _logger.info(f"[INITIAL_PRICING] FINAL PRICE TO SET: ${total_price}")

                    order_line.write({
                        'config_id': config.id,
                        'is_configured': True,
                        'price_unit': total_price,
                        'product_uom_qty': quantity_multiplier,  # Set quantity to multiplier value
                    })

                    # Force recompute of configuration_summary
                    order_line._compute_configuration_summary()

                    # Update sales order to review state if it's in draft or sent state
                    sale_order = order_line.order_id
                    if sale_order.state in ['draft', 'sent']:
                        sale_order.action_set_review()
                        _logger.info(f"[CONFIG_CONTROLLER] Updated sales order {sale_order.name} to Review state")

            # Return success
            return {
                'result': {
                    'config_id': config.id,
                    'redirect_url': f'/web#id={order_line.order_id.id}&view_type=form&model=sale.order' if order_line_id else '/web',
                }
            }
        except Exception as e:
            _logger.error(f"Error saving configuration: {str(e)}")
            return {'error': str(e)}

    @http.route('/config_matrix/get_configuration/<int:config_id>', type='json', auth='user')
    def get_configuration(self, config_id, **kw):
        """Get configuration data"""
        try:
            config = request.env['config.matrix.configuration'].sudo().browse(config_id)
            if not config.exists():
                return {'error': 'Configuration not found'}

            return {
                'id': config.id,
                'product_id': config.product_id.id,
                'template_id': config.template_id.id,
                'values': json.loads(config.config_data or '{}'),
                'price': config.price,
                'state': config.state,
            }

        except Exception as e:
            _logger.error(f"Error getting configuration: {str(e)}")
            return {'error': str(e)}

    def _create_error_response(self, error_message):
        """Create a user-friendly error response"""
        return request.render('website.404', {
            'error_message': error_message
        })

    @http.route('/config_matrix/calculate_operation_costs', type='json', auth='public', website=True)
    def calculate_operation_costs(self, template_id, field_values=None, config_id=None, **kw):
        """Calculate operation costs based on current field values with quantity multiplier"""
        _logger.info(f"[OPERATION_COSTS_BUG] ===== CALCULATE OPERATION COSTS CALLED =====")
        _logger.info(f"[OPERATION_COSTS_BUG] Template ID: {template_id}")
        _logger.info(f"[OPERATION_COSTS_BUG] Config ID: {config_id}")
        _logger.info(f"[OPERATION_COSTS_BUG] Has field_values: {bool(field_values)}")

        try:
            template = request.env['config.matrix.template'].sudo().browse(int(template_id))
            if not template.exists():
                _logger.warning(f"[OPERATION_COSTS_BUG] Template {template_id} not found")
                return {'success': False, 'error': 'Template not found'}

            if not field_values:
                field_values = {}

            _logger.info(f"[OPERATION_COSTS_BUG] Field values count: {len(field_values)}")

            if template.id:
                calc_field_model = request.env['config.matrix.calculated.field']
                calculated_results = calc_field_model.calculate_fields(field_values, template.id)
                field_values.update(calculated_results)
                _logger.info(f"[OPERATION_COSTS_BUG] Added {len(calculated_results)} calculated fields")

            # Get the global quantity multiplier
            quantity_multiplier = self._get_quantity_multiplier(field_values)
            _logger.info(f"[OPERATION_COSTS_BUG] Found quantity multiplier: {quantity_multiplier}")

            # UNIFIED CALCULATION: Use BOM-based approach if config_id is provided
            if config_id:
                _logger.info(f"[OPERATION_COSTS_BUG] ✓ Using BOM-based calculation for config {config_id}")
                return self._calculate_operation_costs_from_bom(config_id, field_values, quantity_multiplier)
            else:
                _logger.warning(f"[OPERATION_COSTS_BUG] ✗ Using field/option mapping calculation (no config_id)")
                _logger.warning(f"[OPERATION_COSTS_BUG] ✗ WARNING: This WILL result in different operation costs than save config")

                # TRY TO USE TEMPLATE-BASED CALCULATION (more consistent with save config)
                try:
                    _logger.info(f"[OPERATION_COSTS_BUG] Attempting template-based calculation as fallback")
                    template_result = self._calculate_operation_costs_from_templates(template, field_values, quantity_multiplier)
                    if template_result.get('success'):
                        _logger.info(f"[OPERATION_COSTS_BUG] ✓ Template-based calculation successful: ${template_result.get('total_cost', 0)}")
                        return template_result
                    else:
                        _logger.warning(f"[OPERATION_COSTS_BUG] Template-based calculation failed: {template_result.get('error')}")
                except Exception as e:
                    _logger.warning(f"[OPERATION_COSTS_BUG] Template-based calculation error: {e}")

                # TRY TEMPORARY CONFIG APPROACH (most accurate)
                try:
                    _logger.info(f"[OPERATION_COSTS_BUG] Attempting temporary config approach")
                    temp_config_result = self._calculate_operation_costs_with_temp_config(template, field_values, quantity_multiplier)
                    if temp_config_result.get('success'):
                        _logger.info(f"[OPERATION_COSTS_BUG] ✓ Temporary config calculation successful: ${temp_config_result.get('total_cost', 0)}")
                        return temp_config_result
                    else:
                        _logger.warning(f"[OPERATION_COSTS_BUG] Temporary config calculation failed: {temp_config_result.get('error')}")
                except Exception as e:
                    _logger.warning(f"[OPERATION_COSTS_BUG] Temporary config calculation error: {e}")

                _logger.warning(f"[OPERATION_COSTS_BUG] Falling back to field/option mapping")

            # Use the extracted field/option mapping method
            return self._calculate_operation_costs_field_option_mapping(template, field_values, quantity_multiplier)

        except Exception as e:
            _logger.error(f"[OPERATION_COSTS_BUG] Error in calculate_operation_costs: {e}")
            import traceback
            _logger.error(f"[OPERATION_COSTS_BUG] Traceback: {traceback.format_exc()}")
            return {'success': False, 'error': str(e)}

    def _calculate_operation_costs_field_option_mapping(self, template, field_values, quantity_multiplier):
        """
        Calculate operation costs using field/option mapping approach (extracted for reuse)

        Args:
            template: Configuration template
            field_values (dict): Field values for calculation
            quantity_multiplier (float): Quantity multiplier

        Returns:
            dict: Operation costs response
        """
        try:
            _logger.info(f"[OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING CALCULATION =====")

            operations = []
            total_cost = 0.0

            # Get field operation mappings through sections and fields
            for section in template.section_ids:
                for field in section.field_ids:
                    if not self._evaluate_visibility_condition(field.visibility_condition, field_values):
                        continue

                    # Check for legacy single operation mapping first
                    if field.workcenter_id and field.operation_name:
                        try:
                            # Calculate duration using legacy formula
                            duration_value = 0.0
                            if field.duration_formula:
                                duration_value = self._evaluate_formula(field.duration_formula, field_values, template)
                            else:
                                duration_value = 60.0  # Default

                            # For legacy fields, assume cost equals duration (backward compatibility)
                            # In the future, we should have separate cost formulas
                            cost_value = duration_value

                            # Apply quantity multiplier to operation cost
                            if quantity_multiplier != 1.0:
                                cost_value = cost_value * quantity_multiplier
                                _logger.info(f"[OPERATION_COSTS] Applied quantity multiplier {quantity_multiplier} to operation {field.operation_name}: {duration_value} -> {cost_value}")

                            if duration_value > 0:
                                operation_data = {
                                    'name': field.operation_name,
                                    'workcenter': field.workcenter_id.name if field.workcenter_id else '',
                                    'cost': cost_value,
                                    'duration': duration_value,
                                    'formula': field.duration_formula or '60.0',
                                    'question_number': field.question_number if field.question_number else None,
                                    'field_name': field.name,
                                    'source_type': 'field_legacy'
                                }

                                operations.append(operation_data)
                                total_cost += cost_value
                                _logger.info(f"[OPERATION_COSTS_BUG] CONTROLLER: Added field legacy operation '{field.operation_name}' from field '{field.name}' - Cost: ${cost_value}")
                        except Exception as e:
                            _logger.warning(f"Error calculating cost for legacy field operation {field.operation_name}: {e}")

                    # Get new field operation mappings
                    for mapping in field.operation_mapping_ids:
                        try:
                            # DEBUG: Log field operation mapping found
                            _logger.info(f"[OPERATION_COSTS_DEBUG] Found field operation mapping: '{mapping.operation_name}' for field '{field.name}'")
                            
                            # Check if this operation should be included based on conditions
                            if mapping.condition:
                                # Evaluate condition with current field values
                                condition_result = self._evaluate_condition(mapping.condition, field_values)
                                if not condition_result:
                                    _logger.info(f"[OPERATION_COSTS_DEBUG] Field operation '{mapping.operation_name}' condition failed, skipping")
                                    continue
                                else:
                                    _logger.info(f"[OPERATION_COSTS_DEBUG] Field operation '{mapping.operation_name}' condition passed")

                            # Calculate duration and cost using the operation template
                            duration_value = 0.0
                            cost_value = 0.0

                            if mapping.operation_template_id:
                                # Use the operation template's calculation methods
                                duration_value = mapping.operation_template_id.get_calculated_duration(field_values)
                                cost_value = mapping.operation_template_id.get_calculated_cost(field_values)
                            elif hasattr(mapping, 'default_duration') and mapping.default_duration:
                                duration_value = mapping.default_duration
                                cost_value = mapping.default_duration  # Fallback: assume cost equals duration

                            # Apply quantity multiplier to operation cost
                            if quantity_multiplier != 1.0:
                                cost_value = cost_value * quantity_multiplier
                                _logger.info(f"[OPERATION_COSTS] Applied quantity multiplier {quantity_multiplier} to operation {mapping.operation_name}: {cost_value / quantity_multiplier} -> {cost_value}")

                            if duration_value > 0:
                                operation_data = {
                                    'name': mapping.operation_name,
                                    'workcenter': mapping.workcenter_id.name if mapping.workcenter_id else '',
                                    'cost': cost_value,
                                    'duration': duration_value,
                                    'formula': mapping.duration_formula or 'Default value',
                                    'question_number': field.question_number if field.question_number else None,
                                    'field_name': field.name,
                                    'source_type': 'field'
                                }

                                operations.append(operation_data)
                                total_cost += cost_value
                                _logger.info(f"[OPERATION_COSTS_BUG] CONTROLLER: Added field mapping operation '{mapping.operation_name}' from field '{field.name}' - Cost: ${cost_value}")

                        except Exception as e:
                            _logger.warning(f"Error calculating cost for field operation {mapping.operation_name}: {e}")
                            continue

            # Get option operation mappings through sections, fields, and options
            for section in template.section_ids:
                for field in section.field_ids:
                    if not self._evaluate_visibility_condition(field.visibility_condition, field_values):
                        continue

                    field_value = field_values.get(field.technical_name)
                    for option in field.option_ids.filtered(lambda o: o.value == field_value):

                        # Check for legacy single operation mapping first
                        if option.workcenter_id and option.operation_name:
                            try:
                                # Calculate duration using legacy formula
                                duration_value = 0.0
                                if option.duration_formula:
                                    duration_value = self._evaluate_formula(option.duration_formula, field_values, template)
                                else:
                                    duration_value = 60.0  # Default

                                # For legacy options, assume cost equals duration (backward compatibility)
                                cost_value = duration_value

                                # Apply quantity multiplier to operation cost
                                if quantity_multiplier != 1.0:
                                    cost_value = cost_value * quantity_multiplier
                                    _logger.info(f"[OPERATION_COSTS] Applied quantity multiplier {quantity_multiplier} to option operation {option.operation_name}: {duration_value} -> {cost_value}")

                                if duration_value > 0:
                                    operation_data = {
                                        'name': option.operation_name,
                                        'workcenter': option.workcenter_id.name if option.workcenter_id else '',
                                        'cost': cost_value,
                                        'duration': duration_value,
                                        'formula': option.duration_formula or '60.0',
                                        'question_number': field.question_number if field.question_number else None,
                                        'field_name': field.name,
                                        'option_name': option.name,
                                        'source_type': 'option_legacy'
                                    }

                                    operations.append(operation_data)
                                    total_cost += cost_value
                                    _logger.info(f"[OPERATION_COSTS_BUG] CONTROLLER: Added option legacy operation '{option.operation_name}' from option '{option.name}' in field '{field.name}' - Cost: ${cost_value}")
                            except Exception as e:
                                _logger.warning(f"Error calculating cost for legacy option operation {option.operation_name}: {e}")

                        # Get new option operation mappings
                        for mapping in option.operation_mapping_ids:
                            try:
                                # DEBUG: Log option operation mapping found
                                _logger.info(f"[OPERATION_COSTS_DEBUG] Found option operation mapping: '{mapping.operation_name}' for option '{option.name}' in field '{field.name}'")
                                
                                # Check if this operation should be included based on conditions
                                if mapping.condition:
                                    condition_result = self._evaluate_condition(mapping.condition, field_values)
                                    if not condition_result:
                                        _logger.info(f"[OPERATION_COSTS_DEBUG] Option operation '{mapping.operation_name}' condition failed, skipping")
                                        continue
                                    else:
                                        _logger.info(f"[OPERATION_COSTS_DEBUG] Option operation '{mapping.operation_name}' condition passed")

                                # Calculate duration and cost using the operation template
                                duration_value = 0.0
                                cost_value = 0.0

                                if mapping.operation_template_id:
                                    # Use the operation template's calculation methods
                                    duration_value = mapping.operation_template_id.get_calculated_duration(field_values)
                                    cost_value = mapping.operation_template_id.get_calculated_cost(field_values)
                                elif hasattr(mapping, 'default_duration') and mapping.default_duration:
                                    duration_value = mapping.default_duration
                                    cost_value = mapping.default_duration  # Fallback: assume cost equals duration

                                # Apply quantity multiplier to operation cost
                                if quantity_multiplier != 1.0:
                                    cost_value = cost_value * quantity_multiplier
                                    _logger.info(f"[OPERATION_COSTS] Applied quantity multiplier {quantity_multiplier} to option operation {mapping.operation_name}: {cost_value / quantity_multiplier} -> {cost_value}")

                                if duration_value > 0:
                                    operation_data = {
                                        'name': mapping.operation_name,
                                        'workcenter': mapping.workcenter_id.name if mapping.workcenter_id else '',
                                        'cost': cost_value,
                                        'duration': duration_value,
                                        'formula': mapping.duration_formula or 'Default value',
                                        'question_number': field.question_number if field.question_number else None,
                                        'field_name': field.name,
                                        'option_name': option.name,
                                        'source_type': 'option'
                                    }

                                    operations.append(operation_data)
                                    total_cost += cost_value
                                    _logger.info(f"[OPERATION_COSTS_BUG] CONTROLLER: Added option mapping operation '{mapping.operation_name}' from option '{option.name}' in field '{field.name}' - Cost: ${cost_value}")

                            except Exception as e:
                                _logger.warning(f"Error calculating cost for option operation {mapping.operation_name}: {e}")
                                continue

            _logger.warning(f"[OPERATION_COSTS_BUG] ===== FIELD/OPTION MAPPING RESULT =====")
            _logger.warning(f"[OPERATION_COSTS_BUG] Operations found: {len(operations)}")
            _logger.warning(f"[OPERATION_COSTS_BUG] Total cost: ${total_cost}")
            _logger.warning(f"[OPERATION_COSTS_BUG] Quantity multiplier: {quantity_multiplier}")
            _logger.warning(f"[OPERATION_COSTS_BUG] ===== THIS DIFFERS FROM SAVE CONFIG =====")

            # Log key metrics for future troubleshooting
            self._log_operation_cost_metrics(None, len(operations), total_cost, 'field_option_mapping')

            return {
                'success': True,
                'operations': operations,
                'total_cost': total_cost,
                'quantity_multiplier': quantity_multiplier
            }

        except Exception as e:
            _logger.error(f"[OPERATION_COSTS_BUG] Error in field/option mapping calculation: {e}")
            import traceback
            _logger.error(f"[OPERATION_COSTS_BUG] Traceback: {traceback.format_exc()}")
            return {'success': False, 'error': str(e)}

    def _evaluate_condition(self, condition, field_values):
        """Evaluate a condition string with field values"""
        if not condition or condition.strip() == 'true':
            return True

        try:
            # Use the formula helper model for evaluation
            formula_helper = request.env['config.matrix.formula.helper'].sudo()
            return formula_helper.evaluate_condition(condition, field_values, default_result=False)
        except Exception as e:
            _logger.warning(f"Error evaluating condition '{condition}': {e}")
            return False

    def _evaluate_visibility_condition(self, condition, field_values):
        """Evaluate visibility condition with proper JSON marker handling and JS to Python conversion"""
        if not condition:
            return True
        
        try:
            # Use the formula helper model for evaluation
            formula_helper = request.env['config.matrix.formula.helper'].sudo()
            return formula_helper.evaluate_visibility_condition(condition, field_values, default_result=True)
        except Exception as e:
            _logger.error(f"Error evaluating visibility condition '{condition}': {e}")
            # Default to visible if evaluation fails
            return True

    def _evaluate_formula(self, formula, field_values, template):
        """Evaluate a formula string with field values and template context"""
        if not formula:
            return 0.0

        try:
            # Use the formula helper for evaluation with custom context
            formula_helper = request.env['config.matrix.formula.helper'].sudo()
            
            # Define custom helper functions specific to this controller
            custom_functions = {
                'get_fixed_price': lambda code: self._get_fixed_price(code, template),
                'get_labor_time': lambda: self._get_labor_time(field_values, template),
                'get_question': lambda question_number: self._get_question(question_number, field_values, template),
            }
            
            # Use the helper's method for evaluation with custom context
            result = formula_helper.evaluate_formula_with_custom_context(
                formula, 
                field_values, 
                custom_functions, 
                default_value=0.0
            )
            
            return float(result) if result is not None else 0.0
        except Exception as e:
            _logger.warning(f"Error evaluating formula '{formula}': {e}")
            return 0.0

    def _get_fixed_price(self, code, template):
        """Get fixed price from operation price table"""
        try:
            price_record = request.env['config.matrix.operation.price'].sudo().search([
                ('code', '=', code),
                ('template_id', '=', template.id)
            ], limit=1)

            if price_record:
                return price_record.value_cost or 0.0
            return 0.0
        except Exception as e:
            _logger.warning(f"Error getting fixed price for code '{code}': {e}")
            return 0.0

    def _get_labor_time(self, field_values, template):
        """Get labor time based on field values"""
        try:
            # This is a placeholder - implement based on your specific logic
            # You might want to look up labor matrices or calculate based on dimensions
            height = float(field_values.get('height', 0))
            width = float(field_values.get('width', 0))

            if height > 0 and width > 0:
                # Simple calculation - you can make this more sophisticated
                area = height * width / 1000000  # Convert mm² to m²
                return area * 10  # 10 minutes per square meter

            return 0.0
        except Exception as e:
            _logger.warning(f"Error calculating labor time: {e}")
            return 0.0

    def _get_question(self, question_number, field_values, template):
        """Get answer by question number"""
        try:
            # Find the field with this question number in the template
            field = request.env['config.matrix.field'].sudo().search([
                ('question_number', '=', question_number),
                ('matrix_id', 'in', template.ids)  # Search within the template
            ], limit=1)

            if field:
                # Try both field ID and technical name
                value = field_values.get(str(field.id)) or field_values.get(field.technical_name)
                return self._convert_answer_value(value, field.field_type)

            # Fallback: try direct lookup by question number
            return self._convert_answer_value(field_values.get(str(question_number), 0), 'integer')

        except Exception as e:
            _logger.warning(f"Error getting question {question_number}: {e}")
            return 0

    def _convert_answer_value(self, value, field_type):
        """Convert answer value to appropriate type for calculations"""
        if value is None:
            return 0

        try:
            if field_type in ['integer', 'float']:
                return float(value) if value else 0.0
            elif field_type == 'boolean':
                return 1.0 if value in [True, 'true', 'True', '1', 1] else 0.0
            else:
                # For text fields, try to convert to number, otherwise return 0
                try:
                    return float(value)
                except (ValueError, TypeError):
                    return 0.0
        except Exception:
            return 0.0

    def _get_quantity_multiplier(self, config_values):
        """Get the global quantity multiplier from configuration values"""
        for key, value in config_values.items():
            if key.endswith('_quantity'):
                try:
                    # Handle both string and numeric values
                    if isinstance(value, str):
                        # Convert string to float
                        numeric_value = float(value)
                    elif isinstance(value, (int, float)):
                        numeric_value = float(value)
                    else:
                        continue
                    
                    if numeric_value > 0:
                        return numeric_value
                except (ValueError, TypeError):
                    # Skip invalid values
                    continue
        return 1.0

    def _log_operation_cost_metrics(self, config_id, operation_count, total_cost, calculation_method):
        """
        Log key metrics for operation cost troubleshooting
        
        Args:
            config_id (int): Configuration ID (None for field/option mapping)
            operation_count (int): Number of operations found
            total_cost (float): Total operation cost
            calculation_method (str): Method used ('bom_based' or 'field_option_mapping')
        """
        try:
            # Create a structured log entry for easy searching
            log_data = {
                'config_id': config_id,
                'operation_count': operation_count,
                'total_cost': round(total_cost, 2),
                'calculation_method': calculation_method,
                'timestamp': request.httprequest.environ.get('REQUEST_TIME', 'unknown')
            }
            
            _logger.info(f"[OPERATION_COST_METRICS] {log_data}")
            
            # Log warning if using field/option mapping (potential inconsistency)
            if calculation_method == 'field_option_mapping':
                _logger.warning(f"[OPERATION_COST_METRICS] WARNING: Using field/option mapping - may differ from save config")
                
        except Exception as e:
            _logger.error(f"[OPERATION_COST_METRICS] Error logging metrics: {e}")

    def _calculate_operation_costs_from_bom(self, config_id, field_values, quantity_multiplier):
        """
        Calculate operation costs using BOM-based approach for consistency with save config
        
        Args:
            config_id (int): Configuration ID
            field_values (dict): Field values for calculation
            quantity_multiplier (float): Quantity multiplier
            
        Returns:
            dict: Operation costs response
        """
        try:
            _logger.info(f"[OPERATION_COSTS_BOM] Calculating operation costs for config {config_id}")
            
            # Get the configuration
            config = request.env['config.matrix.configuration'].sudo().browse(config_id)
            if not config.exists():
                _logger.warning(f"[OPERATION_COSTS_BOM] Configuration {config_id} not found")
                return {'success': False, 'error': 'Configuration not found'}

            if not config.bom_id:
                _logger.warning(f"[OPERATION_COSTS_BOM] No BOM found for configuration {config_id}")
                return {'success': False, 'error': 'No BOM found for configuration'}

            # Use the same calculation method as save config
            total_cost = config._calculate_operation_prices()
            _logger.info(f"[OPERATION_COSTS_BUG] ===== BOM-BASED CALCULATION RESULT =====")
            _logger.info(f"[OPERATION_COSTS_BUG] Total operation cost: ${total_cost}")
            _logger.info(f"[OPERATION_COSTS_BUG] BOM operations count: {len(config.bom_id.operation_ids)}")
            _logger.info(f"[OPERATION_COSTS_BUG] ===== THIS SHOULD MATCH SAVE CONFIG =====")
            _logger.info(f"[OPERATION_COSTS_BOM] Total operation cost: {total_cost} (operations: {len(config.bom_id.operation_ids)})")
            
            # Build operations list from BOM for UI display
            operations = []
            
            for i, operation in enumerate(config.bom_id.operation_ids):
                try:
                    # Try to find the operation template
                    operation_template = config._find_operation_template_for_operation(operation)
                    
                    if operation_template and hasattr(operation_template, 'get_calculated_cost'):
                        # Use the operation template's cost calculation
                        base_operation_price = operation_template.get_calculated_cost(field_values)
                        operation_price = base_operation_price * quantity_multiplier
                        
                        # Get duration if available
                        duration_value = 0.0
                        if hasattr(operation_template, 'get_calculated_duration'):
                            duration_value = operation_template.get_calculated_duration(field_values)
                        else:
                            duration_value = operation.time_cycle / 60.0 if operation.time_cycle else 0.0
                        
                        operation_data = {
                            'name': operation.name,
                            'workcenter': operation.workcenter_id.name if operation.workcenter_id else '',
                            'cost': operation_price,
                            'duration': duration_value,
                            'formula': 'BOM-based calculation',
                            'question_number': None,
                            'field_name': 'BOM Operation',
                            'source_type': 'bom'
                        }
                        
                        operations.append(operation_data)
                    else:
                        # Fallback: use default rate based on time_cycle
                        default_rate = 50.0  # $50/hour default
                        base_operation_price = (operation.time_cycle / 60.0) * default_rate if operation.time_cycle else 0.0
                        operation_price = base_operation_price * quantity_multiplier
                        
                        operation_data = {
                            'name': operation.name,
                            'workcenter': operation.workcenter_id.name if operation.workcenter_id else '',
                            'cost': operation_price,
                            'duration': operation.time_cycle / 60.0 if operation.time_cycle else 0.0,
                            'formula': 'Fallback calculation',
                            'question_number': None,
                            'field_name': 'BOM Operation',
                            'source_type': 'bom_fallback'
                        }
                        
                        operations.append(operation_data)
                        
                except Exception as e:
                    _logger.warning(f"[OPERATION_COSTS_BOM] Error processing BOM operation {operation.name}: {e}")
                    continue

            _logger.info(f"[OPERATION_COSTS_BOM] Processed {len(operations)} operations, total cost: {total_cost}")
            
            # Log key metrics for future troubleshooting
            # self._log_operation_cost_metrics(config_id, len(operations), total_cost, 'bom_based')
            
            return {
                'success': True,
                'operations': operations,
                'total_cost': total_cost,
                'quantity_multiplier': quantity_multiplier
            }

        except Exception as e:
            _logger.error(f"[OPERATION_COSTS_BOM] Error in BOM-based calculation: {e}")
            import traceback
            _logger.error(f"[OPERATION_COSTS_BOM] Traceback: {traceback.format_exc()}")
            return {'success': False, 'error': str(e)}

    def _calculate_operation_costs_from_templates(self, template, field_values, quantity_multiplier):
        """
        Calculate operation costs using field/option mapping approach (same as UI and save config)

        Args:
            template: Configuration template
            field_values (dict): Field values for calculation
            quantity_multiplier (float): Quantity multiplier

        Returns:
            dict: Operation costs response
        """
        try:
            _logger.info(f"[OPERATION_COSTS_BUG] ===== TEMPLATE-BASED CALCULATION (using field/option mapping) =====")

            # Use the same field/option mapping calculation as the UI
            result = self._calculate_operation_costs_field_option_mapping(template, field_values, quantity_multiplier)

            if result.get('success'):
                _logger.info(f"[OPERATION_COSTS_BUG] ✓ Template-based calculation successful: ${result.get('total_cost', 0)} from {len(result.get('operations', []))} operations")
            else:
                _logger.warning(f"[OPERATION_COSTS_BUG] ✗ Template-based calculation failed: {result.get('error')}")

            return result

        except Exception as e:
            _logger.error(f"[OPERATION_COSTS_BUG] Error in template-based calculation: {e}")
            return {'success': False, 'error': str(e)}

    def _calculate_operation_costs_with_temp_config(self, template, field_values, quantity_multiplier):
        """
        Calculate operation costs by creating a temporary configuration and using the same logic as save config

        Args:
            template: Configuration template
            field_values (dict): Field values for calculation
            quantity_multiplier (float): Quantity multiplier

        Returns:
            dict: Operation costs response
        """
        try:
            _logger.info(f"[OPERATION_COSTS_BUG] ===== TEMPORARY CONFIG CALCULATION =====")

            # Create a temporary configuration (not saved to database)
            config_model = request.env['config.matrix.configuration'].sudo()

            # Find a product for this template
            product = request.env['product.product'].sudo().search([
                ('product_tmpl_id.matrix_id', '=', template.id)
            ], limit=1)

            if not product:
                _logger.warning(f"[OPERATION_COSTS_BUG] No product found for template {template.id}")
                return {'success': False, 'error': 'No product found for template'}

            # Create temporary config values
            temp_config = config_model.new({
                'product_id': product.id,
                'template_id': template.id,
                'config_data': json.dumps(field_values),
            })

            _logger.info(f"[OPERATION_COSTS_BUG] Created temporary config for product {product.name}")

            # Calculate operation costs using the same method as save config
            operation_templates = request.env['config.matrix.operation.template'].sudo().search([
                ('config_template_id', '=', template.id),
                ('active', '=', True)
            ])

            _logger.info(f"[OPERATION_COSTS_BUG] Found {len(operation_templates)} operation templates")

            operations = []
            total_cost = 0.0

            for operation_template in operation_templates:
                try:
                    # Use the same method as save config
                    template_cost = operation_template.get_calculated_cost(field_values)

                    # Apply quantity multiplier
                    final_cost = template_cost * quantity_multiplier
                    total_cost += final_cost

                    operation_data = {
                        'name': operation_template.name,
                        'workcenter': operation_template.workcenter_id.name if operation_template.workcenter_id else '',
                        'cost': final_cost,
                        'duration': 0.0,  # Could be calculated if needed
                        'formula': 'Temporary config calculation',
                        'question_number': None,
                        'field_name': 'Template Operation',
                        'source_type': 'temp_config'
                    }

                    operations.append(operation_data)
                    _logger.info(f"[OPERATION_COSTS_BUG] Template '{operation_template.name}': ${template_cost} * {quantity_multiplier} = ${final_cost}")

                except Exception as e:
                    _logger.warning(f"[OPERATION_COSTS_BUG] Error processing template {operation_template.name}: {e}")
                    continue

            _logger.info(f"[OPERATION_COSTS_BUG] Temporary config total: ${total_cost} from {len(operations)} operations")

            return {
                'success': True,
                'operations': operations,
                'total_cost': total_cost,
                'quantity_multiplier': quantity_multiplier
            }

        except Exception as e:
            _logger.error(f"[OPERATION_COSTS_BUG] Error in temporary config calculation: {e}")
            return {'success': False, 'error': str(e)}
