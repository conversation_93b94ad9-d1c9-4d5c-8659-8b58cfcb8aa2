# ConfigMatrix Data Models Reference

## Overview

This document provides a comprehensive reference for all data models in the ConfigMatrix system. The models are organized into logical groups based on their functionality and relationships.

## Core Configuration Models

### config.matrix.template
**Purpose**: Central configuration template for products with lifecycle management

**Key Fields**:
- `name`: Template name
- `code`: Unique template code
- `product_template_id`: Associated product template
- `state`: Lifecycle state (draft, testing, active, archived)
- `use_case`: Primary use case (check_measure, sales, online)
- `description`: Template description
- `active`: Active status

**Relationships**:
- `section_ids`: One-to-many with config.matrix.section
- `price_matrix_ids`: One-to-many with config.matrix.price.matrix
- `labor_time_matrix_ids`: One-to-many with config.matrix.labor.time.matrix
- `svg_component_ids`: One-to-many with config.matrix.svg.component

**Key Methods**:
- `action_activate()`: Activate template for use
- `action_test()`: Set template to testing state
- `action_archive()`: Archive template
- `generate_configuration()`: Create new configuration instance

### config.matrix.section
**Purpose**: Logical grouping of configuration fields

**Key Fields**:
- `name`: Section name
- `matrix_id`: Parent template
- `sequence`: Display order
- `description`: Section description
- `active`: Active status

**Relationships**:
- `field_ids`: One-to-many with config.matrix.field
- `matrix_id`: Many-to-one with config.matrix.template

### config.matrix.field
**Purpose**: Individual configuration questions with advanced features

**Key Fields**:
- `name`: Field name
- `technical_name`: Technical identifier
- `field_type`: Field type (text, number, selection, boolean, date)
- `section_id`: Parent section
- `sequence`: Display order
- `required`: Required field flag
- `default_value`: Default value
- `help_text`: Help text
- `active`: Active status

**Advanced Features**:
- `visibility_condition`: Expression-based visibility rule
- `min_value`/`max_value`: Range validation with expressions
- `check_measure_dynamic_help_template`: Dynamic help for check measure
- `sales_dynamic_help_template`: Dynamic help for sales
- `online_dynamic_help_template`: Dynamic help for online
- `check_measure_dynamic_error_template`: Dynamic error messages
- `sales_dynamic_error_template`: Dynamic error messages
- `online_dynamic_error_template`: Dynamic error messages
- `check_measure_dynamic_default_template`: Dynamic default values
- `sales_dynamic_default_template`: Dynamic default values
- `online_dynamic_default_template`: Dynamic default values

**Relationships**:
- `option_ids`: One-to-many with config.matrix.option
- `calculated_field_ids`: One-to-many with config.matrix.calculated.field
- `component_mapping_ids`: One-to-many with config.matrix.component.mapping
- `field_component_mapping_ids`: One-to-many with config.matrix.field.component.mapping

**Key Methods**:
- `evaluate_visibility()`: Evaluate visibility condition
- `generate_dynamic_help()`: Generate dynamic help text
- `generate_dynamic_error()`: Generate dynamic error messages
- `generate_dynamic_default()`: Generate dynamic default values
- `validate_value()`: Validate field value against rules

### config.matrix.option
**Purpose**: Selection choices for fields with individual component mappings

**Key Fields**:
- `name`: Option name
- `field_id`: Parent field
- `sequence`: Display order
- `value`: Option value
- `active`: Active status

**Relationships**:
- `component_mapping_ids`: One-to-many with config.matrix.component.mapping
- `field_id`: Many-to-one with config.matrix.field

### config.matrix.configuration
**Purpose**: Saved configuration instances with field values and generated BOMs

**Key Fields**:
- `name`: Configuration name
- `template_id`: Associated template
- `state`: Configuration state
- `field_values`: JSON field values
- `calculated_values`: JSON calculated values
- `bom_id`: Generated Bill of Materials
- `total_price`: Calculated total price
- `labor_time`: Calculated labor time
- `active`: Active status

**Relationships**:
- `template_id`: Many-to-one with config.matrix.template
- `bom_id`: Many-to-one with mrp.bom
- `sale_order_line_id`: Many-to-one with sale.order.line

**Key Methods**:
- `generate_bom()`: Generate Bill of Materials
- `calculate_price()`: Calculate total price
- `calculate_labor_time()`: Calculate labor time
- `get_field_value()`: Get field value
- `set_field_value()`: Set field value

## Advanced Feature Models

### config.matrix.visibility.condition
**Purpose**: Complex visibility rules for fields and options

**Key Fields**:
- `name`: Condition name
- `field_id`: Associated field
- `condition_expression`: Expression for visibility
- `use_case`: Use case (check_measure, sales, online)
- `active`: Active status

**Key Methods**:
- `evaluate()`: Evaluate visibility condition

### config.matrix.calculated.field
**Purpose**: Formula-based computed fields with dependency tracking

**Key Fields**:
- `name`: Calculated field name
- `field_id`: Parent field
- `formula`: Calculation formula
- `result_type`: Result data type
- `dependencies`: JSON dependency list
- `active`: Active status

**Key Methods**:
- `calculate()`: Calculate field value
- `get_dependencies()`: Get field dependencies
- `update_dependencies()`: Update dependency tracking

### config.matrix.component.mapping
**Purpose**: Component relationships and BOM generation

**Key Fields**:
- `name`: Mapping name
- `field_id`: Associated field
- `option_id`: Associated option
- `component_product_id`: Component product
- `quantity`: Component quantity
- `quantity_formula`: Quantity calculation formula
- `active`: Active status

**Key Methods**:
- `calculate_quantity()`: Calculate component quantity
- `get_component()`: Get component product

### config.matrix.field.component.mapping
**Purpose**: 1:many component mappings for individual fields

**Key Fields**:
- `name`: Mapping name
- `field_id`: Associated field
- `component_product_id`: Component product
- `quantity_formula`: Quantity calculation formula
- `is_dynamic`: Dynamic component flag
- `reference_value`: Reference value for filtering
- `filter_domain`: Product filter domain
- `active`: Active status

**Key Methods**:
- `get_filtered_products()`: Get filtered products
- `calculate_quantity()`: Calculate component quantity

## Pricing & Manufacturing Models

### config.matrix.price.matrix
**Purpose**: Multi-dimensional pricing calculations based on height/width

**Key Fields**:
- `name`: Matrix name
- `template_id`: Associated template
- `category_id`: Matrix category
- `height_ranges`: JSON height ranges
- `width_ranges`: JSON width ranges
- `matrix_data`: JSON pricing matrix
- `active`: Active status

**Key Methods**:
- `get_price_for_dimensions()`: Get price for dimensions
- `update_matrix()`: Update matrix data
- `validate_ranges()`: Validate range definitions

### config.matrix.labor.time.matrix
**Purpose**: Labor time and cost calculations for manufacturing

**Key Fields**:
- `name`: Matrix name
- `template_id`: Associated template
- `category_id`: Matrix category
- `height_ranges`: JSON height ranges
- `width_ranges`: JSON width ranges
- `matrix_data`: JSON labor time matrix
- `cost_per_hour`: Cost per hour
- `active`: Active status

**Key Methods**:
- `get_labor_time()`: Get labor time for dimensions
- `calculate_cost()`: Calculate labor cost
- `update_matrix()`: Update matrix data

### config.matrix.category
**Purpose**: Organizes pricing and labor matrices by type and purpose

**Key Fields**:
- `name`: Category name
- `type`: Category type (price, labor)
- `description`: Category description
- `active`: Active status

**Relationships**:
- `price_matrix_ids`: One-to-many with config.matrix.price.matrix
- `labor_time_matrix_ids`: One-to-many with config.matrix.labor.time.matrix

## Visual & Integration Models

### config.matrix.svg.component
**Purpose**: Vector-based visual representations for product previews

**Key Fields**:
- `name`: Component name
- `template_id`: Associated template
- `svg_content`: SVG content
- `layer_name`: Layer name
- `visibility_condition`: Layer visibility condition
- `sequence`: Display order
- `active`: Active status

**Key Methods**:
- `evaluate_visibility()`: Evaluate layer visibility
- `render_svg()`: Render SVG content

### Sale Order Line Extension
**Purpose**: Configuration support in sales workflow

**Extended Fields**:
- `is_configurable`: Configurable product flag
- `configuration_id`: Associated configuration
- `config_matrix_template_id`: Configuration template

**Key Methods**:
- `action_configure()`: Open configuration form
- `get_configuration_summary()`: Get configuration summary

### Product Template Extension
**Purpose**: Configuration flags and matrix associations

**Extended Fields**:
- `is_configurable`: Configurable product flag
- `config_matrix_template_id`: Configuration template
- `config_matrix_category_ids`: Configuration categories

## Administrative Models

### config.matrix.wizard
**Purpose**: Administrative wizards and tools

**Key Fields**:
- `name`: Wizard name
- `wizard_type`: Wizard type
- `data`: Wizard data
- `active`: Active status

**Key Methods**:
- `execute()`: Execute wizard action
- `validate()`: Validate wizard data

### config.matrix.import.export
**Purpose**: Import/export functionality for configurations

**Key Fields**:
- `name`: Import/export name
- `type`: Type (import, export)
- `format`: Format (excel, json)
- `data`: Import/export data
- `active`: Active status

**Key Methods**:
- `import_data()`: Import configuration data
- `export_data()`: Export configuration data
- `validate_format()`: Validate data format

## Model Relationships Diagram

```
config.matrix.template
├── config.matrix.section
│   └── config.matrix.field
│       ├── config.matrix.option
│       ├── config.matrix.calculated.field
│       ├── config.matrix.component.mapping
│       ├── config.matrix.field.component.mapping
│       └── config.matrix.visibility.condition
├── config.matrix.price.matrix
├── config.matrix.labor.time.matrix
├── config.matrix.svg.component
└── config.matrix.configuration

config.matrix.category
├── config.matrix.price.matrix
└── config.matrix.labor.time.matrix

sale.order.line (extended)
└── config.matrix.configuration

product.template (extended)
└── config.matrix.template
```

## Key Design Patterns

### 1. Template Pattern
- Templates define the structure for configurations
- Templates can be in different states (draft, testing, active, archived)
- Templates support multiple use cases

### 2. Section-Field Pattern
- Sections organize fields into logical groups
- Fields contain the actual configuration questions
- Fields support multiple types and advanced features

### 3. Component Mapping Pattern
- Components are mapped to fields and options
- Support for both 1:1 and 1:many relationships
- Dynamic component selection based on conditions

### 4. Matrix Pattern
- Pricing and labor calculations use matrix structures
- Matrices support multi-dimensional lookups
- Matrices are organized by categories

### 5. Configuration Instance Pattern
- Configurations are instances of templates
- Configurations store field values and calculated values
- Configurations generate BOMs and pricing

## Performance Considerations

### 1. Caching
- Use computed fields with proper dependencies
- Cache expensive calculations using `@tools.ormcache`
- Cache visibility evaluations for performance

### 2. Database Optimization
- Proper indexing on frequently queried fields
- Use prefetching for related record access
- Optimize queries for large configurations

### 3. Memory Management
- Efficient state management in frontend components
- Proper cleanup of event listeners and timers
- Debouncing for expensive operations

### 4. Query Optimization
- Use `with_context(prefetch_fields=True)` for related records
- Minimize database queries in loops
- Use bulk operations where possible

## Security Considerations

### 1. Access Control
- Role-based access control with user groups
- Record rules for data access
- Field-level security where appropriate

### 2. Input Validation
- Validate all user inputs
- Sanitize dynamic expressions
- Prevent injection attacks

### 3. Data Integrity
- Use constraints for data validation
- Implement proper error handling
- Maintain referential integrity

## Migration Considerations

### 1. Data Migration
- Preserve existing configurations during upgrades
- Migrate field values and relationships
- Update computed fields and dependencies

### 2. Template Migration
- Migrate templates to new structure
- Update visibility conditions and formulas
- Preserve component mappings

### 3. Performance Migration
- Optimize queries for new structure
- Update caching strategies
- Monitor performance after migration
