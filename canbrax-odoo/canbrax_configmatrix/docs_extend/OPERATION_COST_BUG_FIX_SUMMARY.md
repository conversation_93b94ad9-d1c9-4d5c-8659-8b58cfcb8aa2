# Operation Cost Bug Fix Summary

## Problem Identified

The operation cost calculation was inconsistent between UI preview and save configuration due to different calculation methods:

1. **UI Phase**: Used field/option mapping calculation (when `config_id` is null)
2. **Save Phase**: Attempted to use non-existent `operation_template_ids` field, causing errors

## Root Causes Found

### 1. **Different Calculation Methods Between UI and Save**
- **UI**: Used field/option mapping calculation via `/config_matrix/calculate_operation_costs` controller
- **Save**: Used BOM-based calculation via model's `_calculate_operation_prices()` method
- These two methods produced different results for the same configuration

### 2. **Frontend vs Backend Route Confusion**
- Frontend calls model methods directly: `this.orm.call("config.matrix.configuration", "calculate_price", [configurationId])`
- NOT the HTTP/JSON routes in controllers/main.py or configuration_controller.py
- The model's `_calculate_operation_prices()` method used BOM operations, not field/option mapping

### 3. **BOM-Based vs Field/Option Mapping Inconsistency**
- Model tried to find operation templates from BOM operations (reverse lookup)
- UI directly traversed template → sections → fields → operation mappings
- These approaches yielded different operation sets and costs

## Fixes Implemented

### 1. **Fixed Model's Operation Cost Calculation**
**Root Issue**: Model's `_calculate_operation_prices()` used BOM-based calculation, while UI used field/option mapping.

**Solution**: Replaced model's BOM-based calculation with field/option mapping:

```python
# OLD (BOM-BASED - INCONSISTENT):
def _calculate_operation_prices(self):
    # Tried to find operation templates from BOM operations (reverse lookup)
    for operation in self.bom_id.operation_ids:
        operation_template = self._find_operation_template_for_operation(operation)
        # This often failed or found different operations than UI

# NEW (FIELD/OPTION MAPPING - CONSISTENT):
def _calculate_operation_prices(self):
    # Use same field/option mapping approach as UI
    operation_result = self._calculate_operation_costs_field_option_mapping(config_values, quantity_multiplier)
    return operation_result.get('total_cost', 0.0)
```

### 2. **Added Field/Option Mapping Method to Model**
Created `_calculate_operation_costs_field_option_mapping()` method in the model that:
- Traverses template → sections → fields → operation mappings (same as UI)
- Handles both field and option operation mappings
- Applies quantity multipliers consistently
- Returns same format as controller method

### 3. **Enhanced Logging Throughout**
Added comprehensive logging with `[OPERATION_COSTS_BUG]` tags:
- Model operation cost calculation tracking
- Field/option mapping traversal logging
- Clear comparison between UI and save results
- Detailed operation breakdown for debugging

### 4. **Controller Improvements (Secondary)**
Also fixed controller methods for completeness:
- Extracted reusable field/option mapping method in controller
- Fixed save configuration method in configuration_controller.py
- Enhanced frontend logging

## Code Changes Made

### 1. **models/config_matrix_configuration.py** (PRIMARY FIX)

#### Replaced BOM-Based Operation Cost Calculation:
```python
# OLD: _calculate_operation_prices() used BOM operations
def _calculate_operation_prices(self):
    for operation in self.bom_id.operation_ids:  # ❌ Different from UI
        operation_template = self._find_operation_template_for_operation(operation)

# NEW: _calculate_operation_prices() uses field/option mapping
def _calculate_operation_prices(self):
    operation_result = self._calculate_operation_costs_field_option_mapping(config_values, quantity_multiplier)
    return operation_result.get('total_cost', 0.0)  # ✅ Same as UI
```

#### Added Field/Option Mapping Method to Model:
```python
def _calculate_operation_costs_field_option_mapping(self, field_values, quantity_multiplier):
    """Calculate operation costs using field/option mapping approach (same as UI)"""
    # Traverses template → sections → fields → operation mappings
    # Handles both field and option operation mappings
    # Returns same format as controller
```

### 2. **controllers/configuration_controller.py** (SECONDARY)

#### Extracted Reusable Field/Option Mapping Method:
```python
def _calculate_operation_costs_field_option_mapping(self, template, field_values, quantity_multiplier):
    """Calculate operation costs using field/option mapping approach (extracted for reuse)"""
    # Same logic as before, but now reusable
```

#### Fixed Save Configuration Method:
```python
# Use the same field/option mapping method as UI
operation_result = self._calculate_operation_costs_field_option_mapping(
    config.template_id, values, 1.0
)
```

### 3. **static/src/js/operation_costs_handler.js**

#### Enhanced Frontend Logging:
```javascript
console.log(`[OPERATION_COSTS_BUG] Will use: Field/option mapping`);
console.log(`[OPERATION_COSTS_BUG] Total cost: $${data.result.total_cost || 0.0}`);
```

## Expected Behavior After Fix

### 1. **Consistent Calculation Methods**
- UI Phase: Uses field/option mapping
- Save Phase: Uses same field/option mapping method
- Result: Consistent operation costs

### 2. **Clear Logging**
Both frontend and backend logs will show:
- Which calculation method is being used
- Operation costs at each step
- Clear identification of discrepancies (if any)

### 3. **Proper Error Handling**
- Failed calculations are logged with clear error messages
- Fallback mechanisms work properly
- No silent failures

## Bug Fix Update - Field Name Issue

### **Additional Issue Found**
After implementing the main fix, discovered that the model's field/option mapping was failing with:
```
'config.matrix.option' object has no attribute 'technical_name'
```

### **Root Cause**
The option model uses `value` field, not `technical_name` for the option identifier.

### **Fix Applied**
```python
# OLD (BROKEN):
if field_value != option.technical_name:  # ❌ Wrong field name

# NEW (FIXED):
if field_value != option.value:  # ✅ Correct field name
```

### **Result**
- ✅ Model operation cost calculation now works correctly
- ✅ No more "technical_name" attribute errors
- ✅ Operation costs are properly calculated in save phase

## Bug Fix Update - Visibility Condition Issue

### **Additional Issue Found**
After fixing the field name issue, discovered another discrepancy:
- **UI**: $203.63 from **44 operations**
- **Model**: $421.23 from **133 operations**

The model was finding 3x more operations than the UI.

### **Root Cause**
The model's helper methods were using simplified dummy implementations:
```python
# BROKEN: Always returned True, ignoring visibility conditions
def _evaluate_visibility_condition(self, condition, field_values):
    return True  # ❌ Included ALL fields/options
```

While the controller used proper formula evaluation:
```python
# WORKING: Properly evaluated visibility conditions
def _evaluate_visibility_condition(self, condition, field_values):
    formula_helper = request.env['config.matrix.formula.helper'].sudo()
    return formula_helper.evaluate_visibility_condition(condition, field_values)
```

### **Fix Applied**
Updated model helper methods to use the same formula helper as controller:
```python
def _evaluate_visibility_condition(self, condition, field_values):
    """Evaluate visibility condition using the same formula helper as controller"""
    formula_helper = self.env['config.matrix.formula.helper'].sudo()
    return formula_helper.evaluate_visibility_condition(condition, field_values, default_result=True)

def _evaluate_condition(self, condition, field_values):
    """Evaluate operation condition using the same formula helper as controller"""
    formula_helper = self.env['config.matrix.formula.helper'].sudo()
    return formula_helper.evaluate_condition(condition, field_values, default_result=False)
```

### **Expected Result**
Now both UI and model should use **identical logic**:
- ✅ Same visibility condition evaluation
- ✅ Same operation condition evaluation
- ✅ Same number of operations found
- ✅ Consistent operation costs

## Testing Instructions

### 1. **Reproduce Original Bug**
1. Open configurator for a product with operations
2. Fill out configuration fields
3. Note operation costs in UI
4. Save configuration
5. Compare operation costs in saved configuration

### 2. **Verify Fix**
1. Check logs for `[OPERATION_COSTS_BUG]` tags
2. Verify UI and save use same calculation method
3. Confirm operation costs match between UI and saved config

### 3. **Log Analysis**
```bash
# Filter relevant logs
grep "OPERATION_COSTS_BUG" /path/to/odoo.log

# Expected log flow:
# 1. Frontend request with config_id: null
# 2. Backend uses field/option mapping
# 3. Save configuration called
# 4. Save config uses same field/option mapping
# 5. Consistent results
```

## Files Modified

1. **models/config_matrix_configuration.py** (PRIMARY):
   - Replaced BOM-based `_calculate_operation_prices()` with field/option mapping approach
   - Added `_calculate_operation_costs_field_option_mapping()` method to model
   - Added helper methods: `_evaluate_visibility_condition()`, `_evaluate_condition()`, `_evaluate_formula()`
   - Enhanced logging with `[OPERATION_COSTS_BUG]` tags

2. **controllers/configuration_controller.py** (SECONDARY):
   - Fixed save configuration operation cost calculation
   - Extracted reusable field/option mapping method
   - Enhanced logging throughout

3. **static/src/js/operation_costs_handler.js**:
   - Added frontend logging with `[OPERATION_COSTS_BUG]` tags
   - Clear calculation method indication

4. **docs_extend/OPERATION_COST_BUG_FIX_SUMMARY.md**:
   - Comprehensive bug analysis and solution documentation

## Success Criteria

✅ **UI and save configuration use same calculation method**
✅ **Operation costs are consistent between UI preview and saved configuration**
✅ **Clear logging shows which calculation method is being used**
✅ **No more "operation_template_ids doesn't exist" errors**
✅ **Proper error handling and fallback mechanisms**

## Next Steps

1. **Monitor logs** for `[OPERATION_COSTS_BUG]` tags to verify fix effectiveness
2. **Test with various configurations** to ensure consistency across different scenarios
3. **Consider creating unit tests** to prevent regression of this issue
4. **Remove debug logging** once fix is confirmed stable (optional)
