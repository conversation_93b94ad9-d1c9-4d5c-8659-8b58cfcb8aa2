# Sales Fixed Pricing System Guide

## Overview

The **Sales Fixed Pricing** system allows you to create conditional pricing rules that automatically apply fixed amounts (discounts or surcharges) to configurations based on specific conditions. This system works alongside the existing price matrices to provide flexible and dynamic pricing.

## Key Features

- **Conditional Pricing**: Apply fixed prices based on configuration values
- **Priority System**: Control the order of rule evaluation
- **Flexible Conditions**: Use Python expressions for complex pricing logic
- **Template Integration**: Link pricing rules to specific product templates
- **Automatic Application**: Rules are automatically evaluated during configuration

## How It Works

### 1. Rule Evaluation Process

When a configuration is processed, the system:

1. **Fetches all active fixed pricing rules** for the template
2. **Evaluates conditions** in priority order (lowest number = highest priority)
3. **Applies the first matching rule** (stops at first match)
4. **Adds the fixed price** to the total configuration price

### 2. Priority System

Rules are evaluated in priority order:
- **Lower priority numbers = Higher priority**
- **Example**: Priority 10 is evaluated before Priority 50
- **First match wins** - subsequent rules are ignored

### 3. Condition Evaluation

Conditions use Python expressions with access to:
- **Configuration field values** (e.g., `door_height`, `door_type`)
- **Helper functions** (`len`, `str`, `int`, `float`, `bool`, `min`, `max`, `sum`, `abs`, `round`)
- **Logical operators** (`and`, `or`, `not`)
- **Comparison operators** (`==`, `!=`, `<`, `<=`, `>`, `>=`)

## Usage Examples

### Example 1: Large Door Discount

**Rule**: Apply $50 discount for doors with height >= 2000mm

```python
# Condition
door_height >= 2000

# Fixed Price
-50.00

# Priority
50
```

**Result**: When `door_height` is 2000mm or more, a $50 discount is applied.

### Example 2: Commercial Customer Pricing

**Rule**: Apply $100 discount for commercial customers

```python
# Condition
customer_type == 'commercial'

# Fixed Price
-100.00

# Priority
40
```

**Result**: When `customer_type` equals 'commercial', a $100 discount is applied.

### Example 3: Sliding Door Premium

**Rule**: Apply $75 surcharge for sliding doors with midrails

```python
# Condition
door_type == 'sliding' and has_midrail == True

# Fixed Price
75.00

# Priority
60
```

**Result**: When both conditions are true, a $75 surcharge is applied.

### Example 4: Bulk Order Discount

**Rule**: Apply $200 discount for orders with quantity >= 5

```python
# Condition
quantity >= 5

# Fixed Price
-200.00

# Priority
30
```

**Result**: When quantity is 5 or more, a $200 discount is applied.

### Example 5: Express Delivery Surcharge

**Rule**: Apply $150 surcharge for express delivery

```python
# Condition
delivery_type == 'express'

# Fixed Price
150.00

# Priority
70
```

**Result**: When delivery type is 'express', a $150 surcharge is applied.

## Creating Fixed Pricing Rules

### Step 1: Access the Sales Fixed Pricing Menu

1. Navigate to **Matrix → Pricing → Sales Fixed Pricing**
2. Click **Create** to add a new rule

### Step 2: Configure Basic Information

- **Name**: Descriptive name for the rule (e.g., "Large Door Discount")
- **Description**: Detailed explanation of when the rule applies
- **Product Template**: Select the product this rule applies to
- **Sequence**: Display order in the list view
- **Priority**: Evaluation priority (lower numbers = higher priority)

### Step 3: Set Pricing and Conditions

- **Fixed Price**: The amount to add/subtract (negative = discount, positive = surcharge)
- **Conditions**: Python expression that determines when the rule applies

### Step 4: Save and Activate

- **Active**: Check to enable the rule
- **Save** the record

## Best Practices

### 1. Priority Management

- **Use priority 10-50** for core business rules
- **Use priority 51-100** for standard rules
- **Use priority 101+** for fallback or special cases

### 2. Condition Design

- **Keep conditions simple** and readable
- **Use clear field names** that match your configuration
- **Test conditions** with various configuration values
- **Avoid complex nested logic** when possible

### 3. Naming Conventions

- **Use descriptive names** that explain the rule's purpose
- **Include the condition** in the name (e.g., "Height >= 2000mm Discount")
- **Use consistent formatting** across all rules

### 4. Testing and Validation

- **Test with various configurations** to ensure rules work correctly
- **Verify priority order** works as expected
- **Check edge cases** and boundary conditions
- **Validate business logic** with stakeholders

## Integration with Existing Systems

### 1. Price Matrix Integration

Sales Fixed Pricing works alongside existing price matrices:
- **Price matrices** provide base pricing based on dimensions
- **Fixed pricing rules** add conditional adjustments
- **Total price** = Base price + Fixed pricing adjustments

### 2. Configuration System Integration

Rules automatically evaluate during configuration:
- **Field values** are available for condition evaluation
- **Calculated fields** can be referenced in conditions
- **Real-time updates** as configuration changes

### 3. Sales Order Integration

Fixed pricing is applied when:
- **Creating sales orders** from configurations
- **Calculating line item prices**
- **Generating quotes** and proposals

## Advanced Features

### 1. Complex Conditions

You can create sophisticated pricing logic:

```python
# Multiple field conditions
door_height >= 2000 and door_width >= 1200 and door_type == 'sliding'

# Range conditions
800 <= width <= 1200 and height >= 1500

# Enumeration conditions
door_type in ['sliding', 'bi-fold'] and has_midrail == True

# Mathematical conditions
(door_height * door_width) >= 2400000  # Area >= 2.4m²
```

### 2. Helper Functions

Use built-in helper functions for calculations:

```python
# String operations
len(customer_name) > 10

# Mathematical operations
abs(door_height - 2000) <= 100  # Within 100mm of 2000mm

# Type conversions
float(width) >= 800.0
```

### 3. Dynamic Pricing

Create rules that adapt to configuration values:

```python
# Percentage-based adjustments
door_height >= 2500 and door_width >= 1500  # Large door premium

# Quantity-based pricing
quantity >= 10  # Bulk order discount

# Customer-specific pricing
customer_type == 'wholesale' and order_value >= 5000
```

## Troubleshooting

### Common Issues

1. **Rule Not Applying**
   - Check if rule is active
   - Verify condition syntax
   - Ensure field names match configuration
   - Check priority order

2. **Incorrect Pricing**
   - Verify fixed price value (negative = discount)
   - Check condition logic
   - Test with sample data

3. **Performance Issues**
   - Limit number of rules per template
   - Use simple conditions when possible
   - Consider rule priority carefully

### Debugging Tips

1. **Test conditions individually** with sample data
2. **Use simple conditions first**, then add complexity
3. **Check field names** in your configuration template
4. **Verify data types** (string vs integer vs boolean)
5. **Test edge cases** and boundary conditions

## Security Considerations

### 1. Input Validation

- **Conditions are validated** for security
- **Dangerous patterns** are blocked (e.g., `import`, `exec`, `eval`)
- **Field access is restricted** to configuration values only

### 2. Access Control

- **Admin users** can create and modify rules
- **Regular users** can view rules but not modify
- **Portal users** cannot access pricing rules

### 3. Data Protection

- **Company isolation** ensures rules only apply to your company
- **Template restrictions** limit rule scope
- **Audit trail** tracks rule changes

## Future Enhancements

### Planned Features

1. **Rule Templates**: Pre-built rule patterns for common scenarios
2. **Bulk Operations**: Import/export rules from Excel
3. **Advanced Conditions**: More sophisticated logic operators
4. **Performance Optimization**: Caching and rule compilation
5. **Analytics**: Rule usage and effectiveness reporting

### Customization Options

1. **Custom Helper Functions**: Add business-specific logic
2. **Rule Inheritance**: Create rule hierarchies
3. **Conditional Fields**: Dynamic field visibility based on rules
4. **Integration APIs**: Connect with external pricing systems

## Conclusion

The Sales Fixed Pricing system provides a powerful and flexible way to implement complex pricing rules in your ConfigMatrix system. By following the best practices outlined in this guide, you can create effective pricing strategies that automatically adapt to your customers' needs and business requirements.

For additional support or questions, please refer to the main ConfigMatrix documentation or contact your system administrator.
