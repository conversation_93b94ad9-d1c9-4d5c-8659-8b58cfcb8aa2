# Sales Fixed Pricing - Technical Implementation Guide

## Code Architecture

### 1. Model Structure

#### Core Model: `ConfigMatrixSalesFixedPrice`
```python
class ConfigMatrixSalesFixedPrice(models.Model):
    _name = 'config.matrix.sales.fixed.price'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Sales Fixed Pricing for Configurable Products'
    _order = 'sequence, description'
```

#### Key Fields and Relationships
```python
# Template Association
template_id = fields.Many2one(
    'config.matrix.template',
    "Template",
    help="The configuration template this fixed pricing applies to"
)

# Condition Fields (Dual System)
condition = fields.Char(
    "Additional Condition",
    help="Extra condition for this operation (beyond target selection)"
)
code = fields.Char(
    "Conditions",
    help="Python expression to evaluate when determining if this fixed price should apply"
)

# Pricing
value_cost = fields.Float(
    "Fixed Price", 
    digits='Product Price',
    help="The fixed price to apply when conditions are met"
)

# Organization
sequence = fields.Integer('Sequence', default=10, help='Order of display')
active = fields.Boolean('Active', default=True)
```

### 2. Condition Evaluation Engine

#### JavaScript to Python Conversion
```python
def evaluate_conditions(self, configuration_values):
    """Evaluate if the conditions are met for the given configuration values"""
    self.ensure_one()
    
    # Use the new condition field if available, otherwise fall back to code field
    condition_to_evaluate = self.condition or self.code
    
    if not condition_to_evaluate:
        return False  # No conditions means always apply
    
    try:
        # Convert JavaScript formula to Python using the existing calculated field method
        python_formula = self.env['config.matrix.calculated.field']._convert_js_to_python(condition_to_evaluate)
        
        # Create safe evaluation context with configuration values
        ctx = dict(configuration_values)
        
        # Add math functions
        math_context = {
            'round': round,
            'ceil': math.ceil,
            'floor': math.floor,
            'abs': abs,
            'max': max,
            'min': min,
            'sum': sum,
            'parseFloat': float,
            'parseInt': int,
            'Number': float,
            'Math': type('Math', (), {
                'max': max, 'min': min, 'abs': abs, 'ceil': math.ceil,
                'floor': math.floor, 'sqrt': math.sqrt
            })()
        }
        
        # Add boolean context
        bool_context = {
            'true': True,
            'false': False,
            'True': True,
            'False': False,
        }
        
        ctx.update(bool_context)
        ctx.update(math_context)
        
        # Evaluate the condition using safe_eval
        result = safe_eval(python_formula, ctx)
        return bool(result)
        
    except Exception as e:
        _logger.error(f"Error evaluating conditions for fixed pricing {self.id}: {e}")
        return False
```

#### Safe Evaluation Context
```python
# Configuration Values Context
ctx = dict(configuration_values)

# Math Functions Context
math_context = {
    'round': round,
    'ceil': math.ceil,
    'floor': math.floor,
    'abs': abs,
    'max': max,
    'min': min,
    'sum': sum,
    'parseFloat': float,
    'parseInt': int,
    'Number': float,
    'Math': type('Math', (), {
        'max': max, 'min': min, 'abs': abs, 'ceil': math.ceil,
        'floor': math.floor, 'sqrt': math.sqrt
    })()
}

# Boolean Context
bool_context = {
    'true': True,
    'false': False,
    'True': True,
    'False': False,
}

# Update context
ctx.update(bool_context)
ctx.update(math_context)
```

### 3. Integration with ConfigMatrix Template

#### Pricing Flow Integration
```python
# In config_matrix_template.py - get_configuration_price method

# NEW: SPECIAL CONDITION EVALUATION FOR PLUGH COMPONENTS
if self.plugh_price_grid_id:
    _logger.info(f"[PRICE_MATRIX] Evaluating special conditions for plugh components")
    
    # Get all price matrices with sales_price_application='plugh'
    plugh_sale_price_matrices = self.env['config.matrix.price.matrix'].search([
        ('sales_price_application', '=', 'plugh'),
        ('active', '=', True)
    ])
    
    # Find the first matrix that meets special conditions
    selected_plugh_matrix = None
    for matrix in plugh_sale_price_matrices:
        if matrix.evaluate_special_conditions(configuration_values, ...):
            selected_plugh_matrix = matrix
            break
    
    if selected_plugh_matrix:
        # Add the selected special condition matrix as a new entry in price_matrix_configs
        # Keep the original plugh_price_grid_id, add this as an additional plugh matrix
        new_plugh_config = {
            'matrix': selected_plugh_matrix,
            'height_field': self.door_height_field_id.name if self.door_height_field_id else None,
            'width_field': self.door_width_field_id.name if self.door_width_field_id else None,
            'type': 'plugh_special'  # New type to distinguish from original plugh
        }
        price_matrix_configs.append(new_plugh_config)
        _logger.info(f"[PRICE_MATRIX] Added special condition plugh matrix: {selected_plugh_matrix.name} as new entry")
        _logger.info(f"[PRICE_MATRIX] Now processing both original plugh matrix and special condition matrix")
```

#### Fixed Pricing Fallback Logic
```python
# SPECIAL HANDLING FOR PLUGH SPECIAL CONDITION MATRIX: Check for fixed pricing fallback
# Only apply fixed pricing fallback to the special condition matrix, not the original plugh matrix
if matrix_type == 'plugh_special' and not price:
    _logger.info(f"[PRICE_MATRIX] Grid lookup failed for plugh special condition matrix, checking fixed pricing fallback")
    
    # Get fixed pricing for this template
    fixed_pricing_result = self._get_plugh_fixed_pricing_fallback(configuration_values)
    
    if fixed_pricing_result['fixed_pricing_applied']:
        # Use fixed pricing instead of grid pricing
        price = fixed_pricing_result['total_price'] * panel_quantity
        _logger.info(f"[PRICE_MATRIX] Using fixed pricing for plugh special condition matrix: {price}")
        
        # Add fixed pricing breakdown entries
        for fixed_entry in fixed_pricing_result['breakdown']:
            breakdown.append({
                'matrix_name': f"Fixed Pricing - {fixed_entry['description']}",
                'price': fixed_entry['price'] * panel_quantity,
                'dimensions': {'height': height, 'width': width},
                'matrix_type': 'plugh_special_fixed',
                'height_field': height_field,
                'width_field': width_field,
                'fixed_pricing_entry': fixed_entry['id'],
                'pricing_source': 'fixed_pricing'
            })
        
        # Use fixed pricing currency if available
        if fixed_pricing_result['currency']:
            currency = fixed_pricing_result['currency']
        
        # Continue to next iteration since we've handled pricing
        continue
```

### 4. Fixed Pricing Fallback Method

#### Core Method Implementation
```python
def _get_plugh_fixed_pricing_fallback(self, configuration_values):
    """
    Get fixed pricing for plugh components when grid lookup fails
    
    Args:
        configuration_values: Dict of field technical names to values
        
    Returns:
        dict: {
            'fixed_pricing_applied': bool,
            'total_price': float,
            'breakdown': list,
            'currency': record or None
        }
    """
    try:
        # Find all fixed pricing entries for this template
        fixed_pricing_entries = self.env['config.matrix.sales.fixed.price'].search([
            ('template_id', '=', self.id),
            ('active', '=', True)
        ], order='sequence ASC')
        
        if not fixed_pricing_entries:
            return {
                'fixed_pricing_applied': False,
                'total_price': 0.0,
                'breakdown': [],
                'currency': None
            }
        
        _logger.info(f"[FIXED_PRICING] Found {len(fixed_pricing_entries)} fixed pricing entries for template {self.id}")
        
        # Check each entry in sequence order (first match takes precedence)
        total_price = 0.0
        breakdown = []
        currency = None
        domain_helper = self.env['config.matrix.calculated.field'].sudo()

        for entry in fixed_pricing_entries:
            try:
                if entry.evaluate_conditions(configuration_values):
                    _logger.info(f"[FIXED_PRICING] Fixed pricing condition met for entry: {entry.description}")
                    
                    # Add to breakdown
                    breakdown.append({
                        'id': entry.id,
                        'description': entry.description,
                        'price': entry.value_cost,
                        'condition': entry.condition or entry.code
                    })
                    
                    # Add to total price
                    total_price += entry.value_cost
                    
                    # Use entry currency if available
                    if entry.currency_id:
                        currency = entry.currency_id
                    
                    _logger.info(f"[FIXED_PRICING] Added fixed pricing: {entry.description} = {entry.value_cost}")
                    
            except Exception as e:
                _logger.error(f"[FIXED_PRICING] Error evaluating conditions for entry {entry.id}: {str(e)}")
                continue
        
        if breakdown:
            _logger.info(f"[FIXED_PRICING] Total fixed pricing applied: {total_price}")
            return {
                'fixed_pricing_applied': True,
                'total_price': total_price,
                'breakdown': breakdown,
                'currency': currency
            }
        else:
            _logger.info(f"[FIXED_PRICING] No fixed pricing conditions met for template {self.id}")
            return {
                'fixed_pricing_applied': False,
                'total_price': 0.0,
                'breakdown': [],
                'currency': None
            }
            
    except Exception as e:
        _logger.error(f"[FIXED_PRICING] Error getting fixed price for template {self.id}: {str(e)}")
        return {
            'fixed_pricing_applied': False,
            'total_price': 0.0,
            'breakdown': [],
            'currency': None
        }
```

## Security Implementation

### 1. Input Validation

#### Security Constraints
```python
@api.constrains('condition')
def _check_condition_security(self):
    """Validate additional condition for security"""
    for record in self:
        if record.condition:
            # Check for dangerous patterns
            dangerous_patterns = [
                'import', 'exec', 'eval', '__', 'open', 'file',
                'system', 'subprocess', 'os.', 'sys.', 'globals'
            ]
            condition_lower = record.condition.lower()
            for pattern in dangerous_patterns:
                if pattern in condition_lower:
                    raise ValidationError(
                        _("Additional condition contains invalid pattern: %s") % pattern
                    )

@api.constrains('code')
def _check_code_security(self):
    """Validate conditions for security"""
    for record in self:
        if record.code:
            # Check for dangerous patterns
            dangerous_patterns = [
                'import', 'exec', 'eval', '__', 'open', 'file',
                'system', 'subprocess', 'os.', 'sys.', 'globals'
            ]
            code_lower = record.code.lower()
            for pattern in dangerous_patterns:
                if pattern in code_lower:
                    raise ValidationError(
                        _("Conditions code contains invalid pattern: %s") % pattern
                    )
```

#### Safe Evaluation Environment
```python
# Restricted execution context
result = safe_eval(python_formula, ctx)

# Context isolation
ctx = dict(configuration_values)  # Only configuration values
ctx.update(math_context)          # Only safe math functions
ctx.update(bool_context)          # Only boolean values

# No builtins access
# No file system access
# No network access
# No system commands
```

### 2. Access Control

#### Model Access Rights
```csv
id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_config_matrix_sales_fixed_pricing_admin,config.matrix.sales.fixed.price.admin,model_config_matrix_sales_fixed_price,group_config_matrix_admin,1,1,1,1
access_config_matrix_sales_fixed_pricing_user,config.matrix.sales.fixed.price.user,model_config_matrix_sales_fixed_price,group_config_matrix_user,1,0,0,0
```

#### Record Rules
```xml
<!-- Template-scoped access -->
<record id="config_matrix_sales_fixed_pricing_rule_user" model="ir.rule">
    <field name="name">Sales Fixed Pricing: users can only access rules for their accessible templates</field>
    <field name="model_id" ref="model_config_matrix_sales_fixed_price"/>
    <field name="domain_force">[('template_id.active', '=', True)]</field>
    <field name="groups" eval="[(4, ref('canbrax_configmatrix.group_config_matrix_user'))]"/>
</record>

<!-- Admin access -->
<record id="config_matrix_sales_fixed_pricing_rule_admin" model="ir.rule">
    <field name="name">Sales Fixed Pricing: admins can access all rules</field>
    <field name="model_id" ref="model_config_matrix_sales_fixed_price"/>
    <field name="domain_force">[(1, '=', 1)]</field>
    <field name="groups" eval="[(4, ref('canbrax_configmatrix.group_config_matrix_admin'))]"/>
</record>
```

## View Implementation

### 1. List View
```xml
<record id="view_config_matrix_sales_fixed_pricing_list" model="ir.ui.view">
    <field name="name">config.matrix.sales.fixed.pricing.list</field>
    <field name="model">config.matrix.sales.fixed.price</field>
    <field name="arch" type="xml">
        <list string="Sales Fixed Pricing">
            <field name="sequence" widget="handle"/>
            <field name="template_id"/>
            <field name="description"/>
            <field name="value_cost" widget="monetary" options="{'currency_field': 'currency_id'}"/>
            <field name="condition"/>
        </list>
    </field>
</record>
```

### 2. Form View
```xml
<record id="view_config_matrix_sales_fixed_pricing_form" model="ir.ui.view">
    <field name="name">config.matrix.sales.fixed.pricing.form</field>
    <field name="model">config.matrix.sales.fixed.price</field>
    <field name="arch" type="xml">
        <form string="Sales Fixed Pricing">
            <sheet>
                <div class="oe_title">
                    <h1>
                        <field name="description" placeholder="Fixed Pricing Description"/>
                    </h1>
                </div>
                <group>
                    <group>
                        <field name="template_id" options="{'no_create': True}"/>
                        <field name="sequence"/>
                    </group>
                    <group>
                        <field name="value_cost" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                        <field name="currency_id" invisible="1"/>
                        <field name="active" invisible="1"/>
                    </group>
                </group>
                <group string="Conditions">
                    <field name="condition" placeholder="Primary condition expression (e.g., door_height >= 2000 and door_type == 'sliding')"/>
                </group>
            </sheet>
            <chatter/>
        </form>
    </field>
</record>
```

### 3. Search View
```xml
<record id="view_config_matrix_sales_fixed_pricing_search" model="ir.ui.view">
    <field name="name">config.matrix.sales.fixed.pricing.search</field>
    <field name="model">config.matrix.sales.fixed.price</field>
    <field name="arch" type="xml">
        <search string="Sales Fixed Pricing">
            <field name="description"/>
            <field name="template_id"/>
            <field name="condition"/>
            <filter string="Active" name="active" domain="[('active', '=', True)]"/>
            <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
            <group expand="0" string="Group By">
                <filter string="Template" name="group_template" context="{'group_by': 'template_id'}"/>
            </group>
        </search>
    </field>
</record>
```

## Menu Integration

### Menu Structure Update
```xml
<!-- Rename existing menu -->
<menuitem id="menu_config_matrix_operation_price" 
          name="Operations Fixed Pricing" 
          parent="menu_config_matrix_pricing" 
          sequence="10"/>

<!-- Add new menu -->
<menuitem id="menu_config_matrix_sales_fixed_pricing" 
          name="Sales Fixed Pricing" 
          parent="menu_config_matrix_pricing" 
          action="action_config_matrix_sales_fixed_pricing" 
          sequence="20"/>
```

### Action Definition
```xml
<record id="action_config_matrix_sales_fixed_pricing" model="ir.actions.act_window">
    <field name="name">Sales Fixed Pricing</field>
    <field name="res_model">config.matrix.sales.fixed.price</field>
    <field name="view_mode">list,form</field>
    <field name="help" type="html">
        <p class="o_view_nocontent_smiling_face">
            Create your first sales fixed pricing rule!
        </p>
    </field>
</record>
```

## Data Flow and Integration Points

### 1. Pricing Calculation Flow
```
1. Configuration Template Pricing Request
   ↓
2. Regular Grid Pricing Lookup
   ↓
3. Special Condition Matrix Evaluation
   ↓
4. Fixed Pricing Fallback (if needed)
   ↓
5. Combined Pricing Result
```

### 2. Integration Points

#### Template Integration
- **Model**: `config.matrix.template`
- **Method**: `get_configuration_price()`
- **Hook**: Plugh component special handling
- **Fallback**: Fixed pricing when grid lookup fails

#### Calculated Field Integration
- **Model**: `config.matrix.calculated.field`
- **Method**: `_convert_js_to_python()`
- **Purpose**: JavaScript to Python syntax conversion
- **Reuse**: Leverages existing conversion logic

#### Price Matrix Integration
- **Model**: `config.matrix.price.matrix`
- **Relationship**: Special condition matrices with `sales_price_application = 'plugh'`
- **Flow**: Evaluated before fixed pricing fallback

### 3. Data Relationships
```
config.matrix.template (1) ←→ (N) config.matrix.sales.fixed.price
config.matrix.template (1) ←→ (N) config.matrix.price.matrix
config.matrix.sales.fixed.price (N) ←→ (1) res.company
```

## Performance Considerations

### 1. Database Optimization

#### Efficient Queries
```python
# Optimized search with proper ordering
fixed_pricing_entries = self.env['config.matrix.sales.fixed.price'].search([
    ('template_id', '=', self.id),
    ('active', '=', True)
], order='sequence ASC')

# Use limit for performance
if len(fixed_pricing_entries) > 50:
    _logger.warning(f"[FIXED_PRICING] Too many active rules ({len(fixed_pricing_entries)}), limiting evaluation")
    fixed_pricing_entries = fixed_pricing_entries[:50]
```

#### Indexing Strategy
```sql
-- Recommended database indexes
CREATE INDEX idx_sales_fixed_pricing_template_active 
ON config_matrix_sales_fixed_price(template_id, active);

CREATE INDEX idx_sales_fixed_pricing_sequence 
ON config_matrix_sales_fixed_price(sequence);

CREATE INDEX idx_sales_fixed_pricing_company 
ON config_matrix_sales_fixed_pricing(company_id);
```

### 2. Caching Strategy

#### Condition Evaluation Caching
```python
# Consider implementing caching for expensive condition evaluations
@tools.ormcache('self.id', 'hash(tuple(sorted(configuration_values.items())))')
def evaluate_conditions_cached(self, configuration_values):
    """Cached version of condition evaluation"""
    return self.evaluate_conditions(configuration_values)
```

#### Template-Level Caching
```python
# Cache fixed pricing rules per template
@tools.ormcache('template_id')
def get_cached_fixed_pricing_rules(self, template_id):
    """Get cached fixed pricing rules for template"""
    return self.search([
        ('template_id', '=', template_id),
        ('active', '=', True)
    ], order='sequence ASC')
```

## Error Handling and Logging

### 1. Comprehensive Logging

#### Logging Levels
```python
# Info level for normal operations
_logger.info(f"[FIXED_PRICING] Found {len(fixed_pricing_entries)} fixed pricing entries for template {self.id}")

# Warning level for potential issues
_logger.warning(f"[FIXED_PRICING] Too many active rules ({len(fixed_pricing_entries)}), limiting evaluation")

# Error level for failures
_logger.error(f"[FIXED_PRICING] Error evaluating conditions for entry {entry.id}: {str(e)}")
```

#### Structured Logging
```python
# Consistent log format
_logger.info(f"[FIXED_PRICING] Entry {entry.id}: condition='{entry.condition}', result={result}")

# Performance logging
import time
start_time = time.time()
result = entry.evaluate_conditions(configuration_values)
duration = time.time() - start_time
_logger.info(f"[FIXED_PRICING] Condition evaluation took {duration:.3f}s for entry {entry.id}")
```

### 2. Error Recovery

#### Graceful Degradation
```python
try:
    if entry.evaluate_conditions(configuration_values):
        # Process successful evaluation
        pass
except Exception as e:
    _logger.error(f"[FIXED_PRICING] Error evaluating conditions for entry {entry.id}: {str(e)}")
    # Continue with next entry instead of failing completely
    continue
```

#### Fallback Mechanisms
```python
# Return safe defaults on errors
except Exception as e:
    _logger.error(f"[FIXED_PRICING] Error getting fixed price for template {self.id}: {str(e)}")
    return {
        'fixed_pricing_applied': False,
        'total_price': 0.0,
        'breakdown': [],
        'currency': None
    }
```

## Testing and Validation

### 1. Unit Testing

#### Model Testing
```python
def test_condition_evaluation(self):
    """Test condition evaluation with various inputs"""
    rule = self.env['config.matrix.sales.fixed.price'].create({
        'template_id': self.template.id,
        'description': 'Test rule',
        'value_cost': 100.0,
        'condition': 'door_height >= 2000'
    })
    
    # Test successful evaluation
    result = rule.evaluate_conditions({'door_height': 2500})
    self.assertTrue(result)
    
    # Test failed evaluation
    result = rule.evaluate_conditions({'door_height': 1500})
    self.assertFalse(result)
    
    # Test error handling
    result = rule.evaluate_conditions({'door_height': 'invalid'})
    self.assertFalse(result)
```

#### Integration Testing
```python
def test_fixed_pricing_fallback(self):
    """Test fixed pricing fallback integration"""
    # Create fixed pricing rule
    rule = self.env['config.matrix.sales.fixed.price'].create({
        'template_id': self.template.id,
        'description': 'Test fallback',
        'value_cost': 50.0,
        'condition': 'door_height > 2000'
    })
    
    # Test pricing calculation
    result = self.template.get_configuration_price({
        'door_height': 2500,
        'door_width': 1000
    })
    
    # Verify fixed pricing was applied
    self.assertIn('fixed_pricing', [item['pricing_source'] for item in result['breakdown']])
```

### 2. Performance Testing

#### Load Testing
```python
def test_performance_with_many_rules(self):
    """Test performance with large number of rules"""
    # Create many rules
    for i in range(100):
        self.env['config.matrix.sales.fixed.price'].create({
            'template_id': self.template.id,
            'description': f'Rule {i}',
            'value_cost': 10.0,
            'condition': f'field_{i} > {i}'
        })
    
    # Measure performance
    import time
    start_time = time.time()
    
    result = self.template.get_configuration_price({
        'field_50': 100
    })
    
    duration = time.time() - start_time
    self.assertLess(duration, 1.0, "Pricing calculation should complete within 1 second")
```

## Deployment and Migration

### 1. Database Migration

#### Schema Changes
```python
# Migration script for existing installations
def migrate_sales_fixed_pricing(cr, version):
    """Migrate to sales fixed pricing system"""
    
    # Create new table
    cr.execute("""
        CREATE TABLE IF NOT EXISTS config_matrix_sales_fixed_price (
            id SERIAL PRIMARY KEY,
            sequence INTEGER DEFAULT 10,
            active BOOLEAN DEFAULT TRUE,
            template_id INTEGER REFERENCES config_matrix_template(id),
            description TEXT,
            value_cost NUMERIC(10,2),
            condition VARCHAR(255),
            code VARCHAR(255),
            company_id INTEGER REFERENCES res_company(id),
            create_date TIMESTAMP DEFAULT NOW(),
            write_date TIMESTAMP DEFAULT NOW()
        )
    """)
    
    # Create indexes
    cr.execute("""
        CREATE INDEX IF NOT EXISTS idx_sales_fixed_pricing_template_active 
        ON config_matrix_sales_fixed_pricing(template_id, active)
    """)
```

#### Data Migration
```python
# Migrate existing fixed pricing data if any
def migrate_existing_data(cr):
    """Migrate existing fixed pricing data"""
    
    # Check if old table exists
    cr.execute("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_name = 'old_fixed_pricing_table'
        )
    """)
    
    if cr.fetchone()[0]:
        # Migrate data
        cr.execute("""
            INSERT INTO config_matrix_sales_fixed_price 
            (template_id, description, value_cost, code, company_id)
            SELECT template_id, description, price, condition, company_id
            FROM old_fixed_pricing_table
        """)
```

### 2. Configuration Updates

#### Module Dependencies
```python
# __manifest__.py updates
{
    'depends': [
        'base',
        'mail',
        'product',
        'canbrax_configmatrix',  # Core module
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/pricing/config_matrix_sales_fixed_price_views.xml',
        'views/menu_views.xml',
    ],
}
```

#### Security Updates
```python
# Update security files
def update_security_files():
    """Update security configuration"""
    
    # Add access rights
    access_rights = [
        ('access_config_matrix_sales_fixed_pricing_admin', 'config.matrix.sales.fixed.price.admin'),
        ('access_config_matrix_sales_fixed_pricing_user', 'config.matrix.sales.fixed.price.user'),
    ]
    
    # Add record rules
    record_rules = [
        ('config_matrix_sales_fixed_pricing_rule_user', 'Sales Fixed Pricing: user access'),
        ('config_matrix_sales_fixed_pricing_rule_admin', 'Sales Fixed Pricing: admin access'),
    ]
```

## Monitoring and Maintenance

### 1. Performance Monitoring

#### Key Metrics
```python
# Monitor pricing calculation performance
def log_performance_metrics(self, start_time, rule_count, result):
    """Log performance metrics for monitoring"""
    duration = time.time() - start_time
    success = result['fixed_pricing_applied']
    
    _logger.info(f"[PERFORMANCE] Fixed pricing calculation: "
                f"duration={duration:.3f}s, rules={rule_count}, success={success}")
    
    # Log to monitoring system if available
    if hasattr(self.env, 'monitoring'):
        self.env.monitoring.record_metric(
            'fixed_pricing_duration', duration,
            tags={'template_id': self.id, 'rule_count': rule_count}
        )
```

#### Health Checks
```python
def health_check_fixed_pricing(self):
    """Perform health check on fixed pricing system"""
    issues = []
    
    # Check for rules with invalid conditions
    invalid_rules = self.env['config.matrix.sales.fixed.price'].search([
        ('active', '=', True)
    ]).filtered(lambda r: not r._validate_condition_syntax())
    
    if invalid_rules:
        issues.append(f"Found {len(invalid_rules)} rules with invalid condition syntax")
    
    # Check for performance issues
    slow_rules = self.env['config_matrix_sales_fixed_pricing'].search([
        ('active', '=', True),
        ('evaluation_time', '>', 1.0)  # If tracking evaluation time
    ])
    
    if slow_rules:
        issues.append(f"Found {len(slow_rules)} rules with slow evaluation")
    
    return issues
```

### 2. Maintenance Tasks

#### Regular Cleanup
```python
def cleanup_inactive_rules(self):
    """Clean up inactive and outdated rules"""
    
    # Archive rules not used in 90 days
    cutoff_date = fields.Date.today() - timedelta(days=90)
    
    inactive_rules = self.env['config_matrix.sales.fixed_pricing'].search([
        ('active', '=', True),
        ('write_date', '<', cutoff_date),
        ('usage_count', '=', 0)  # If tracking usage
    ])
    
    if inactive_rules:
        inactive_rules.write({'active': False})
        _logger.info(f"Archived {len(inactive_rules)} inactive fixed pricing rules")
    
    return len(inactive_rules)
```

#### Condition Validation
```python
def validate_all_conditions(self):
    """Validate all active condition expressions"""
    
    invalid_rules = []
    
    for rule in self.env['config.matrix.sales.fixed_pricing'].search([('active', '=', True)]):
        try:
            # Test condition syntax
            python_condition = rule.env['config.matrix.calculated.field']._convert_js_to_python(rule.condition)
            
            # Test compilation
            compile(python_condition, '<string>', 'eval')
            
        except Exception as e:
            invalid_rules.append({
                'rule': rule,
                'error': str(e),
                'condition': rule.condition
            })
    
    return invalid_rules
```

## Conclusion

The Sales Fixed Pricing system provides a robust, secure, and performant solution for implementing conditional pricing rules in the ConfigMatrix system. The technical implementation ensures:

- **Security**: Safe expression evaluation with comprehensive input validation
- **Performance**: Efficient condition evaluation and caching strategies
- **Reliability**: Robust error handling and graceful degradation
- **Maintainability**: Clear code structure and comprehensive logging
- **Scalability**: Optimized database queries and performance monitoring

The system integrates seamlessly with existing ConfigMatrix infrastructure while providing powerful new capabilities for dynamic pricing based on product configuration choices.
