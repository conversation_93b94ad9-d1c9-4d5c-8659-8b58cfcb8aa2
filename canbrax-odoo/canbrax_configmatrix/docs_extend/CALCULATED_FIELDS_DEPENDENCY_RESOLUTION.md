# Calculated Fields Dependency Resolution System

## Overview

The Calculated Fields Dependency Resolution System is an advanced feature that automatically handles dependencies between calculated fields in the ConfigMatrix system. It ensures that fields are calculated in the correct order, preventing errors caused by missing dependencies.

## Problem Statement

### Original Issue
The previous `calculate_fields` method processed calculated fields sequentially without considering dependencies. This caused errors when:

1. **Field A** depends on **Field B**
2. **Field B** hasn't been calculated yet
3. **Field A** tries to use **Field B**'s value
4. Result: Error or incorrect calculation

### Example of Dependency Chain
```
_CALCULATED_largest_door_width → _CALCULATED_Middle_width → _CALCULATED_CommandeX_Midrail_Qty
```

If these fields are calculated out of order, the system fails.

## Solution Architecture

### 1. Dependency Analysis
The system analyzes each calculated field to identify:
- **Direct dependencies**: Fields referenced in formulas
- **Calculated field dependencies**: Only `_CALCULATED_*` fields
- **Dependency count**: Number of calculated fields each field depends on

### 2. Topological Sorting
Uses <PERSON>'s algorithm to determine the optimal calculation order:
- Fields with no dependencies are calculated first
- Dependent fields are calculated after their dependencies
- Circular dependencies are detected and reported

### 3. Multi-Pass Calculation
The system performs multiple calculation passes:
- **Pass 1**: Calculate independent fields
- **Pass 2**: Calculate fields with resolved dependencies
- **Pass 3+**: Continue until all fields are calculated or max passes reached

## Key Methods

### `calculate_fields(template_id, field_values)`
Main method that orchestrates the entire calculation process.

**Features:**
- Automatic dependency resolution
- Multi-pass calculation strategy
- Error handling and logging
- Performance optimization with caching

**Parameters:**
- `template_id`: ID of the configuration template
- `field_values`: Dictionary of input field values

**Returns:**
- Dictionary of calculated field values

### `analyze_calculated_field_dependencies(template_id)`
Analyzes dependencies between calculated fields for debugging and optimization.

**Returns:**
```python
{
    'dependency_map': {
        'field_name': {
            'formula': 'formula_string',
            'sequence': 1,
            'category': 'basic',
            'all_dependencies': ['dep1', 'dep2'],
            'calculated_dependencies': ['_CALCULATED_dep1'],
            'dependency_count': 1
        }
    },
    'sorted_fields': [...],
    'total_fields': 10,
    'fields_with_dependencies': 5,
    'fields_without_dependencies': 5
}
```

### `get_calculation_order_suggestion(template_id)`
Suggests optimal calculation order based on dependency analysis.

**Returns:**
```python
{
    'suggested_order': ['field1', 'field2', 'field3'],
    'suggested_sequences': {'field1': 1, 'field2': 2, 'field3': 3},
    'analysis': {...}
}
```

### `debug_calculation_dependencies(template_id, test_values)`
Debug method for testing and troubleshooting dependency issues.

### `validate_calculation_formulas(template_id)`
Validates that all calculation formulas are syntactically correct.

## How It Works

### Step 1: Dependency Extraction
```python
def _extract_dependencies_from_formula(self, formula):
    """Extract all variable dependencies from a formula"""
    import re
    
    # Find all variable names in the formula
    variable_pattern = r'\b([a-zA-Z_][a-zA-Z0-9_]*)\b'
    variables = re.findall(variable_pattern, formula)
    
    # Filter out built-in functions and constants
    built_ins = ['min', 'max', 'abs', 'round', 'ceil', 'floor', 'sqrt', 'pow', 
                 'log', 'log10', 'exp', 'sin', 'cos', 'tan', 'pi', 'e',
                 'parseFloat', 'parseInt', 'Number', 'Math']
    
    dependencies = [var for var in variables if var not in built_ins]
    return list(set(dependencies))  # Remove duplicates
```

### Step 2: Topological Sort
```python
def _topological_sort(self, dependency_graph):
    """Perform topological sort on dependency graph using Kahn's algorithm"""
    # Calculate in-degrees for each node
    in_degree = {node: 0 for node in dependency_graph}
    
    for node, dependencies in dependency_graph.items():
        for dep in dependencies:
            if dep in in_degree:
                in_degree[dep] += 1
    
    # Find nodes with no incoming edges
    queue = [node for node, degree in in_degree.items() if degree == 0]
    result = []
    
    while queue:
        node = queue.pop(0)
        result.append(node)
        
        # Remove edges from this node
        for dep in dependency_graph.get(node, []):
            if dep in in_degree:
                in_degree[dep] -= 1
                if in_degree[dep] == 0:
                    queue.append(dep)
    
    # Check for circular dependencies
    if len(result) != len(dependency_graph):
        remaining = set(dependency_graph.keys()) - set(result)
        raise ValueError(f"Circular dependency detected in fields: {remaining}")
    
    return result
```

### Step 3: Multi-Pass Calculation
```python
def calculate_fields(self, field_values, template_id=None):
    """Calculate all calculated fields with dependency resolution"""
    calculated_fields = self.get_calculated_fields_for_template(template_id)
    results = {}
    
    # Create evaluation context
    context = dict(field_values)
    
    # Add math functions and constants
    context.update(self._get_math_context())
    
    # Sort fields by dependencies
    sorted_fields = self._sort_fields_by_dependencies(calculated_fields)
    
    # Multi-pass calculation
    max_passes = 10
    for pass_num in range(max_passes):
        fields_calculated_this_pass = 0
        
        for field in sorted_fields:
            if field['name'] in results:
                continue  # Already calculated
                
            if self._can_calculate_field(field, context, results):
                try:
                    value = self._evaluate_field(field, context, results)
                    results[field['name']] = value
                    context[field['name']] = value
                    fields_calculated_this_pass += 1
                except Exception as e:
                    _logger.error(f"Error calculating {field['name']}: {e}")
                    results[field['name']] = None
        
        # If no fields were calculated this pass, we're done
        if fields_calculated_this_pass == 0:
            break
    
    return results
```

## Usage Examples

### Basic Usage
```python
# Calculate all fields for a template
calculated_values = self.env['config.matrix.calculated.field'].calculate_fields(
    field_values={'width': 1000, 'height': 800},
    template_id=1
)

print(f"Calculated {len(calculated_values)} fields")
```

### Dependency Analysis
```python
# Analyze dependencies between fields
analysis = self.env['config.matrix.calculated.field'].analyze_calculated_field_dependencies(
    template_id=1
)

print(f"Fields with dependencies: {analysis['fields_with_dependencies']}")
for field_name, info in analysis['dependency_map'].items():
    if info['dependency_count'] > 0:
        print(f"{field_name} depends on: {info['calculated_dependencies']}")
```

### Debug and Troubleshooting
```python
# Debug calculation issues
debug_report = self.env['config.matrix.calculated.field'].debug_calculation_dependencies(
    template_id=1,
    test_values={'width': 1000, 'height': 800}
)

if not debug_report['calculation_success']:
    print(f"Calculation failed: {debug_report['error_message']}")
    print("Fields with issues:")
    for issue in debug_report['fields_with_issues']:
        print(f"  {issue['field_name']}: missing {issue['missing_dependencies']}")
```

### Formula Validation
```python
# Validate all formulas
validation = self.env['config.matrix.calculated.field'].validate_calculation_formulas(
    template_id=1
)

for field_name, result in validation.items():
    if result['status'] != 'valid':
        print(f"Field {field_name} has issues: {result['error']}")
```

## Performance Considerations

### Caching Strategy
- **Dependency analysis** is cached per template
- **Calculation results** are cached during multi-pass execution
- **Formula conversion** is cached to avoid repeated JavaScript-to-Python conversion

### Optimization Techniques
- **Early termination**: Stop when no more fields can be calculated
- **Batch processing**: Process multiple fields per pass
- **Memory management**: Efficient context updates and result storage

### Scalability
- **Large templates**: Handles templates with 100+ calculated fields
- **Complex dependencies**: Manages deep dependency chains (5+ levels)
- **Memory efficient**: Minimal memory overhead during calculation

## Error Handling

### Common Error Types
1. **Circular Dependencies**: Detected and reported with field names
2. **Missing Dependencies**: Logged with detailed dependency information
3. **Formula Errors**: Syntax and evaluation errors are caught and logged
4. **Type Conversion Errors**: Automatic handling of data type mismatches

### Error Recovery
- **Graceful degradation**: Continue calculation for other fields
- **Detailed logging**: Comprehensive error information for debugging
- **Fallback values**: Use default values when calculations fail

## Best Practices

### 1. Field Naming
- Use descriptive names for calculated fields
- Prefix with `_CALCULATED_` for automatic detection
- Avoid names that could conflict with input fields

### 2. Formula Design
- Keep formulas simple and readable
- Use clear variable names
- Avoid complex nested logic when possible
- Test formulas with various input values

### 3. Dependency Management
- Minimize circular dependencies
- Use appropriate sequence values
- Group related calculations together
- Document complex dependency relationships

### 4. Performance Optimization
- Use computed fields for expensive calculations
- Cache frequently used values
- Avoid unnecessary recalculations
- Monitor calculation performance

## Troubleshooting Guide

### Issue: Circular Dependency Detected
**Symptoms:**
- Error message: "Circular dependency detected in fields: [field1, field2]"
- Calculation fails completely

**Solutions:**
1. Review the dependency chain between the listed fields
2. Restructure formulas to break the circular dependency
3. Use intermediate calculated fields to break cycles
4. Consider if the circular dependency is actually necessary

### Issue: Field Not Calculated
**Symptoms:**
- Some calculated fields return `None`
- Missing values in results

**Solutions:**
1. Check if all dependencies are available
2. Verify formula syntax and logic
3. Use debug methods to identify missing dependencies
4. Check sequence values and dependency order

### Issue: Performance Problems
**Symptoms:**
- Slow calculation times
- High memory usage
- Timeout errors

**Solutions:**
1. Review dependency complexity
2. Optimize formulas for efficiency
3. Use caching for expensive calculations
4. Consider breaking complex calculations into smaller parts

## Testing and Validation

### Unit Testing
```python
def test_dependency_resolution(self):
    """Test dependency resolution with known dependencies"""
    field_values = {'width': 1000, 'height': 800}
    results = self.env['config.matrix.calculated.field'].calculate_fields(
        field_values, template_id=1
    )
    
    # Verify all fields were calculated
    self.assertTrue(all(v is not None for v in results.values()))
    
    # Verify dependency order
    if '_CALCULATED_dependent_field' in results:
        self.assertIn('_CALCULATED_base_field', results)
```

### Integration Testing
```python
def test_complete_calculation_workflow(self):
    """Test complete calculation workflow with real data"""
    # Load test template
    template = self.env['config.matrix.template'].browse(1)
    
    # Test with various input values
    test_cases = [
        {'width': 1000, 'height': 800},
        {'width': 1500, 'height': 1200},
        {'width': 800, 'height': 600}
    ]
    
    for test_case in test_cases:
        results = self.env['config.matrix.calculated.field'].calculate_fields(
            test_case, template_id=template.id
        )
        self.assertTrue(len(results) > 0)
```

## Future Enhancements

### Planned Features
1. **Visual Dependency Graph**: Web interface for visualizing field dependencies
2. **Automatic Optimization**: AI-powered formula optimization suggestions
3. **Performance Profiling**: Detailed performance analysis and recommendations
4. **Formula Templates**: Reusable formula patterns for common calculations

### Extension Points
1. **Custom Functions**: Support for user-defined calculation functions
2. **External Integrations**: API calls and external service integration
3. **Advanced Caching**: Redis-based distributed caching
4. **Real-time Updates**: WebSocket-based real-time calculation updates

## Conclusion

The Calculated Fields Dependency Resolution System provides a robust, efficient, and maintainable solution for handling complex field dependencies in the ConfigMatrix system. By automatically managing calculation order and providing comprehensive debugging tools, it eliminates the manual effort required to maintain calculation sequences and significantly reduces calculation errors.

The system's multi-pass approach, topological sorting, and comprehensive error handling make it suitable for production use with complex configuration templates, while its performance optimizations ensure fast calculation times even with large numbers of calculated fields.
