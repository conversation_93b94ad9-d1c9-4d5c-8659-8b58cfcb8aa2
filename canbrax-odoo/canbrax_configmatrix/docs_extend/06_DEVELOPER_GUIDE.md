# ConfigMatrix Developer Guide

## Overview

This developer guide provides comprehensive technical implementation details, patterns, and best practices for the ConfigMatrix system. It covers backend development, frontend components, integration patterns, and performance optimization.

## Backend Development Standards

### Model Development Patterns

#### 1. Template Pattern Implementation
```python
class ConfigMatrixTemplate(models.Model):
    _name = 'config.matrix.template'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Configuration Matrix Template'
    
    # Use descriptive field names with proper types
    name = fields.Char(string='Template Name', required=True, tracking=True)
    code = fields.Char(string='Template Code', required=True, tracking=True)
    product_template_id = fields.Many2one('product.template', string='Product Template', required=True)
    
    # Use selection_add for extending existing selections
    state = fields.Selection([
        ('draft', 'Draft'),
        ('testing', 'Testing'),
        ('active', 'Active'),
        ('archived', 'Archived')
    ], string='State', default='draft', tracking=True)
    
    # Use computed fields with proper dependencies
    @api.depends('section_ids', 'section_ids.field_ids')
    def _compute_field_count(self):
        for template in self:
            template.field_count = sum(len(section.field_ids) for section in template.section_ids)
    
    # Use constraints for data integrity
    _sql_constraints = [
        ('unique_code', 'unique(code)', 'Template code must be unique!')
    ]
```

#### 2. Advanced Field Implementation
```python
class ConfigMatrixField(models.Model):
    _name = 'config.matrix.field'
    _description = 'Configuration Matrix Field'
    
    # Core fields
    name = fields.Char(string='Field Name', required=True)
    technical_name = fields.Char(string='Technical Name', required=True)
    field_type = fields.Selection([
        ('text', 'Text'),
        ('number', 'Number'),
        ('selection', 'Selection'),
        ('boolean', 'Boolean'),
        ('date', 'Date')
    ], string='Field Type', required=True, default='text')
    
    # Advanced features
    visibility_condition = fields.Text(string='Visibility Condition')
    min_value = fields.Text(string='Minimum Value Expression')
    max_value = fields.Text(string='Maximum Value Expression')
    
    # Dynamic content templates
    check_measure_dynamic_help_template = fields.Text(string='Check Measure Help Template')
    sales_dynamic_help_template = fields.Text(string='Sales Help Template')
    online_dynamic_help_template = fields.Text(string='Online Help Template')
    
    # Performance optimization with caching
    @tools.ormcache('self.id', 'values_hash')
    def evaluate_visibility_cached(self, values_hash, field_values):
        """Cached visibility evaluation for performance"""
        return self._evaluate_visibility_uncached(field_values)
    
    def _evaluate_visibility_uncached(self, field_values):
        """Evaluate field visibility based on conditions"""
        if not self.visibility_condition:
            return True
        
        try:
            # Create safe context for evaluation
            safe_context = {
                'values': field_values,
                'field_values': field_values,
                'config_values': field_values,
            }
            
            # Add field values as individual variables
            for key, value in field_values.items():
                safe_context[key] = value
            
            # Evaluate the condition
            result = safe_eval(self.visibility_condition, safe_context)
            return bool(result)
        except Exception as e:
            _logger.error(f"Error evaluating visibility condition: {e}")
            return True
```

#### 3. Dynamic Content Generation
```python
    def generate_dynamic_help(self, values=None, use_case='check_measure'):
        """Generate dynamic help text using field placeholders"""
        if not values:
            values = {}
        
        template_field = f'{use_case}_dynamic_help_template'
        template = getattr(self, template_field, '')
        
        if not template:
            return ''
        
        # Replace placeholders with actual values
        def replace_expression(match):
            field_name = match.group(1)
            if field_name in values:
                return str(values[field_name])
            return f'{{{field_name}}}'
        
        import re
        return re.sub(r'\{([^}]+)\}', replace_expression, template)
```

### Component Mapping Patterns

#### 1. Unified Component Mapping
```python
class ConfigMatrixComponentMapping(models.Model):
    _name = 'config.matrix.component.mapping'
    _description = 'Configuration Matrix Component Mapping'
    
    field_id = fields.Many2one('config.matrix.field', string='Field', required=True)
    option_id = fields.Many2one('config.matrix.option', string='Option')
    component_product_id = fields.Many2one('product.product', string='Component Product', required=True)
    quantity = fields.Float(string='Quantity', default=1.0)
    quantity_formula = fields.Text(string='Quantity Formula')
    
    def calculate_quantity(self, field_values=None):
        """Calculate component quantity using formula or fixed value"""
        if self.quantity_formula and field_values:
            try:
                # Create safe context for formula evaluation
            safe_context = {
                'values': field_values,
                'field_values': field_values,
            }
            
            # Add field values as individual variables
            for key, value in field_values.items():
                safe_context[key] = value
            
                result = safe_eval(self.quantity_formula, safe_context)
                return float(result)
        except Exception as e:
                _logger.error(f"Error calculating quantity: {e}")
                return self.quantity
        
        return self.quantity
```

#### 2. Dynamic Component Selection
```python
class ConfigMatrixFieldComponentMapping(models.Model):
    _name = 'config.matrix.field.component.mapping'
    _description = 'Configuration Matrix Field Component Mapping'
    
    field_id = fields.Many2one('config.matrix.field', string='Field', required=True)
    component_product_id = fields.Many2one('product.product', string='Component Product')
    quantity_formula = fields.Text(string='Quantity Formula')
    is_dynamic = fields.Boolean(string='Dynamic Component')
    reference_value = fields.Char(string='Reference Value')
    filter_domain = fields.Text(string='Filter Domain')
    
    def get_filtered_products(self, reference_value=None, field_values=None):
        """Get filtered products based on dynamic conditions"""
        if not self.is_dynamic:
            return self.component_product_id
        
        try:
            # Build domain from filter_domain
            domain = []
            if self.filter_domain:
                domain = safe_eval(self.filter_domain, {
                    'reference_value': reference_value,
                    'field_values': field_values or {},
                })
            
            # Add reference value condition if specified
            if self.reference_value and reference_value:
                domain.append(('name', 'ilike', reference_value))
            
            # Search for products
            products = self.env['product.product'].search(domain)
            return products
        except Exception as e:
            _logger.error(f"Error filtering products: {e}")
            return self.component_product_id
```

### Pricing Matrix Implementation

#### 1. Multi-dimensional Pricing
```python
class ConfigMatrixPriceMatrix(models.Model):
    _name = 'config.matrix.price.matrix'
    _description = 'Configuration Matrix Price Matrix'
    
    name = fields.Char(string='Matrix Name', required=True)
    template_id = fields.Many2one('config.matrix.template', string='Template', required=True)
    category_id = fields.Many2one('config.matrix.category', string='Category')
    height_ranges = fields.Text(string='Height Ranges (JSON)')
    width_ranges = fields.Text(string='Width Ranges (JSON)')
    matrix_data = fields.Text(string='Matrix Data (JSON)')
    
    def get_price_for_dimensions(self, height, width):
        """Get price for specific dimensions"""
        try:
            height_ranges = json.loads(self.height_ranges) if self.height_ranges else []
            width_ranges = json.loads(self.width_ranges) if self.width_ranges else []
            matrix_data = json.loads(self.matrix_data) if self.matrix_data else {}
            
            # Find matching height range
            height_range = None
            for hr in height_ranges:
                if hr['min'] <= height <= hr['max']:
                    height_range = hr
                    break
            
            # Find matching width range
            width_range = None
            for wr in width_ranges:
                if wr['min'] <= width <= wr['max']:
                    width_range = wr
                    break
            
            if not height_range or not width_range:
                return 0.0
            
            # Get price from matrix
            key = f"height_{height_range['label']}_width_{width_range['label']}"
            price = matrix_data.get(key, 0.0)
            
            return float(price)
        except Exception as e:
            _logger.error(f"Error getting price for dimensions: {e}")
            return 0.0
```

## Frontend Development (OWL 2.0)

### Component Architecture

#### 1. Main Configurator Component
```javascript
/** @odoo-module **/
import { Component, useState, onMounted, useRef } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";

export class ConfigMatrixConfigurator extends Component {
    static template = "canbrax_configmatrix.Configurator";
    static props = {
        templateId: { type: Number, optional: true },
        configurationId: { type: Number, optional: true },
        useCase: { type: String, optional: true },
        onSave: { type: Function, optional: true },
        onCancel: { type: Function, optional: true },
    };
    
    setup() {
        this.state = useState({
            loading: false,
            error: false,
            fieldValues: {},
            calculatedValues: {},
            visibleFields: new Set(),
            validationErrors: {},
        });
        
        this.rpc = useService("rpc");
        this.notification = useService("notification");
        this.rootRef = useRef("root");
        
        // Use debouncing for performance
        this.debouncedUpdate = this.debounce(this.updateCalculatedFields, 300);
        this.debouncedVisibility = this.debounce(this.updateVisibility, 200);
        
        onMounted(() => {
            this.initializeConfiguration();
        });
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    async initializeConfiguration() {
        try {
            this.state.loading = true;
            
            if (this.props.configurationId) {
                await this.loadExistingConfiguration();
            } else if (this.props.templateId) {
                await this.loadTemplate();
            }
            
            await this.updateVisibility();
            await this.updateCalculatedFields();
        } catch (error) {
            this.state.error = true;
            this.notification.add(error.message, { type: "danger" });
        } finally {
            this.state.loading = false;
        }
    }
    
    async loadTemplate() {
        const template = await this.rpc("/config_matrix/get_template", {
            template_id: this.props.templateId,
            use_case: this.props.useCase || "sales",
        });
        
        this.template = template;
        this.initializeFieldValues();
    }
    
    async loadExistingConfiguration() {
        const configuration = await this.rpc("/config_matrix/get_configuration", {
            configuration_id: this.props.configurationId,
        });
        
        this.configuration = configuration;
        this.template = configuration.template;
        this.state.fieldValues = configuration.field_values || {};
        this.state.calculatedValues = configuration.calculated_values || {};
    }
    
    initializeFieldValues() {
        const fieldValues = {};
        
        for (const section of this.template.sections) {
            for (const field of section.fields) {
                if (field.default_value) {
                    fieldValues[field.technical_name] = field.default_value;
                }
            }
        }
        
        this.state.fieldValues = fieldValues;
    }
    
    async updateVisibility() {
        const visibleFields = new Set();
        
        for (const section of this.template.sections) {
            for (const field of section.fields) {
                const isVisible = await this.evaluateFieldVisibility(field);
                if (isVisible) {
                    visibleFields.add(field.id);
                }
            }
        }
        
        this.state.visibleFields = visibleFields;
    }
    
    async evaluateFieldVisibility(field) {
        if (!field.visibility_condition) {
            return true;
        }
        
        try {
            const result = await this.rpc("/config_matrix/evaluate_visibility", {
                field_id: field.id,
                field_values: this.state.fieldValues,
                use_case: this.props.useCase || "sales",
            });
            
            return result.visible;
        } catch (error) {
            console.error("Error evaluating visibility:", error);
            return true;
        }
    }
    
    async updateCalculatedFields() {
        try {
            const result = await this.rpc("/config_matrix/calculate_fields", {
                template_id: this.template.id,
                field_values: this.state.fieldValues,
                use_case: this.props.useCase || "sales",
            });
            
            this.state.calculatedValues = result.calculated_values;
        } catch (error) {
            console.error("Error updating calculated fields:", error);
        }
    }
    
    async onFieldChange(fieldId, value) {
        this.state.fieldValues[fieldId] = value;
        
        // Update visibility and calculated fields
        this.debouncedVisibility();
        this.debouncedUpdate();
        
        // Validate field
        await this.validateField(fieldId, value);
    }
    
    async validateField(fieldId, value) {
        try {
            const result = await this.rpc("/config_matrix/validate_field", {
                field_id: fieldId,
                value: value,
                field_values: this.state.fieldValues,
                use_case: this.props.useCase || "sales",
            });
            
            if (result.valid) {
                delete this.state.validationErrors[fieldId];
            } else {
                this.state.validationErrors[fieldId] = result.error_message;
            }
        } catch (error) {
            console.error("Error validating field:", error);
        }
    }
    
    async saveConfiguration() {
        try {
            this.state.loading = true;
            
            const result = await this.rpc("/config_matrix/save_configuration", {
                template_id: this.template.id,
                field_values: this.state.fieldValues,
                calculated_values: this.state.calculatedValues,
                use_case: this.props.useCase || "sales",
            });
            
            if (this.props.onSave) {
                this.props.onSave(result.configuration_id);
            }
            
            this.notification.add("Configuration saved successfully", { type: "success" });
        } catch (error) {
            this.notification.add(error.message, { type: "danger" });
        } finally {
            this.state.loading = false;
        }
    }
    
    cancelConfiguration() {
        if (this.props.onCancel) {
            this.props.onCancel();
        }
    }
}
```

#### 2. Field Component Implementation
```javascript
/** @odoo-module **/
import { Component } from "@odoo/owl";

export class ConfigMatrixField extends Component {
    static template = "canbrax_configmatrix.Field";
    static props = {
        field: { type: Object },
        value: { type: [String, Number, Boolean], optional: true },
        error: { type: String, optional: true },
        onChange: { type: Function },
    };
    
    setup() {
        this.fieldTypes = {
            text: this.renderTextField.bind(this),
            number: this.renderNumberField.bind(this),
            selection: this.renderSelectionField.bind(this),
            boolean: this.renderBooleanField.bind(this),
            date: this.renderDateField.bind(this),
        };
    }
    
    renderField() {
        const renderer = this.fieldTypes[this.props.field.field_type];
        return renderer ? renderer() : this.renderTextField();
    }
    
    renderTextField() {
        return {
            type: "input",
            props: {
                type: "text",
                value: this.props.value || "",
                class: "form-control",
                placeholder: this.props.field.help_text,
            },
            on: {
                input: (ev) => this.props.onChange(this.props.field.technical_name, ev.target.value),
            },
        };
    }
    
    renderNumberField() {
        return {
            type: "input",
            props: {
                type: "number",
                value: this.props.value || "",
                class: "form-control",
                step: "0.01",
                placeholder: this.props.field.help_text,
            },
            on: {
                input: (ev) => this.props.onChange(this.props.field.technical_name, parseFloat(ev.target.value) || 0),
            },
        };
    }
    
    renderSelectionField() {
        return {
            type: "select",
            props: {
                class: "form-control",
            },
            children: [
                {
                    type: "option",
                    props: { value: "" },
                    children: ["Select an option..."],
                },
                ...this.props.field.options.map(option => ({
                    type: "option",
                    props: { 
                        value: option.value,
                        selected: this.props.value === option.value,
                    },
                    children: [option.name],
                })),
            ],
            on: {
                change: (ev) => this.props.onChange(this.props.field.technical_name, ev.target.value),
            },
        };
    }
    
    renderBooleanField() {
        return {
            type: "input",
            props: {
                type: "checkbox",
                checked: this.props.value || false,
                class: "form-check-input",
            },
            on: {
                change: (ev) => this.props.onChange(this.props.field.technical_name, ev.target.checked),
            },
        };
    }
    
    renderDateField() {
        return {
            type: "input",
            props: {
                type: "date",
                value: this.props.value || "",
                class: "form-control",
            },
            on: {
                change: (ev) => this.props.onChange(this.props.field.technical_name, ev.target.value),
            },
        };
    }
}
```

### Widget Registration
```javascript
// ✅ CORRECT - Use object with component property
registry.category("fields").add("config_matrix_widget", {
    component: ConfigMatrixConfigurator,
});

// ❌ INCORRECT - Direct class registration causes OWL errors
registry.category("fields").add("config_matrix_widget", ConfigMatrixConfigurator);
```

## Controller Development

### API Endpoints
```python
class ConfigMatrixController(http.Controller):
    
    @http.route('/config_matrix/get_template', type='json', auth='user')
    def get_template(self, template_id, use_case='sales'):
        """Get configuration template with fields and options"""
        try:
            template = request.env['config.matrix.template'].browse(template_id)
            
            if not template.exists():
                return {'error': 'Template not found'}
            
            # Get template data with sections and fields
            template_data = template.get_template_data(use_case=use_case)
            return template_data
        except Exception as e:
            _logger.error(f"Error getting template: {e}")
            return {'error': str(e)}
    
    @http.route('/config_matrix/evaluate_visibility', type='json', auth='user')
    def evaluate_visibility(self, field_id, field_values, use_case='sales'):
        """Evaluate field visibility based on conditions"""
        try:
            field = request.env['config.matrix.field'].browse(field_id)
            
            if not field.exists():
                return {'visible': True}
            
            visible = field.evaluate_visibility(field_values, use_case=use_case)
            return {'visible': visible}
        except Exception as e:
            _logger.error(f"Error evaluating visibility: {e}")
            return {'visible': True}
    
    @http.route('/config_matrix/calculate_fields', type='json', auth='user')
    def calculate_fields(self, template_id, field_values, use_case='sales'):
        """Calculate computed fields based on current values"""
        try:
            template = request.env['config.matrix.template'].browse(template_id)
            
            if not template.exists():
                return {'calculated_values': {}}
            
            calculated_values = template.calculate_fields(field_values, use_case=use_case)
            return {'calculated_values': calculated_values}
        except Exception as e:
            _logger.error(f"Error calculating fields: {e}")
            return {'calculated_values': {}}
    
    @http.route('/config_matrix/validate_field', type='json', auth='user')
    def validate_field(self, field_id, value, field_values, use_case='sales'):
        """Validate field value against rules"""
        try:
            field = request.env['config.matrix.field'].browse(field_id)
            
            if not field.exists():
                return {'valid': True}
            
            valid, error_message = field.validate_value(value, field_values, use_case=use_case)
            return {
                'valid': valid,
                'error_message': error_message
            }
        except Exception as e:
            _logger.error(f"Error validating field: {e}")
            return {'valid': True}
    
    @http.route('/config_matrix/save_configuration', type='json', auth='user')
    def save_configuration(self, template_id, field_values, calculated_values, use_case='sales'):
        """Save configuration and generate BOM"""
        try:
            template = request.env['config.matrix.template'].browse(template_id)
            
            if not template.exists():
                return {'error': 'Template not found'}
            
            # Create or update configuration
            configuration = template.create_or_update_configuration(
                field_values=field_values,
                calculated_values=calculated_values,
                use_case=use_case
            )
            
            return {
                'configuration_id': configuration.id,
                'bom_id': configuration.bom_id.id if configuration.bom_id else None,
                'total_price': configuration.total_price,
                'labor_time': configuration.labor_time,
            }
        except Exception as e:
            _logger.error(f"Error saving configuration: {e}")
            return {'error': str(e)}
```

## Performance Optimization

### 1. Caching Strategies
```python
# Use computed fields with proper dependencies
@api.depends('field_values', 'calculated_values')
def _compute_total_price(self):
    for config in self:
        config.total_price = config.calculate_total_price()

# Use ormcache for expensive operations
@tools.ormcache('self.id', 'values_hash')
def evaluate_visibility_cached(self, values_hash, field_values):
    return self._evaluate_visibility_uncached(field_values)

# Cache template data
@tools.ormcache('self.id', 'use_case')
def get_template_data(self, use_case='sales'):
    return self._get_template_data_uncached(use_case)
```

### 2. Database Optimization
```python
# Use prefetching for related records
def get_template_with_sections(self, template_id):
    template = self.env['config.matrix.template'].with_context(
        prefetch_fields=True
    ).browse(template_id)
    
    # Prefetch related records
    template.section_ids.mapped('field_ids')
    template.section_ids.mapped('field_ids.option_ids')
    
    return template

# Use bulk operations
def update_field_values_bulk(self, configurations, field_values):
    for config in configurations:
        config.field_values = field_values
    
    # Bulk write for performance
    self.env['config.matrix.configuration'].browse(
        [c.id for c in configurations]
    ).write({
        'field_values': field_values
    })
```

### 3. Frontend Performance
```javascript
// Debounce expensive operations
this.debouncedUpdate = this.debounce(this.updateCalculatedFields, 300);
this.debouncedVisibility = this.debounce(this.updateVisibility, 200);

// Use virtual scrolling for large lists
export class VirtualList extends Component {
    setup() {
        this.visibleItems = this.computeVisibleItems();
        this.scrollHandler = this.debounce(this.handleScroll, 100);
    }
    
    computeVisibleItems() {
        const start = Math.floor(this.state.scrollTop / this.itemHeight);
        const end = start + this.visibleCount;
        return this.props.items.slice(start, end);
    }
}

// Memory management
export class Configurator extends Component {
    setup() {
        this.cleanup = [];
    }
    
    willUnmount() {
        this.cleanup.forEach(cleanup => cleanup());
    }
    
    addCleanup(cleanup) {
        this.cleanup.push(cleanup);
    }
}
```

### 4. Operation Costs Handler
The Operation Costs Handler is a specialized JavaScript class that manages the display and calculation of operation costs in the configurator's right panel.

#### Implementation
```javascript
class OperationCostsHandler {
    constructor() {
        this.operationCosts = [];
        this.totalCost = 0.0;
        this.templateId = null;
        this.currentValues = {};
    }

    /**
     * Enhanced getCurrentFieldValues method that includes calculated fields
     * This method collects both regular field values and calculated field values
     */
    getCurrentFieldValues() {
        const values = {};

        // Collect regular field values from DOM
        const fields = document.querySelectorAll('.config-field');
        fields.forEach(field => {
            const technicalName = field.getAttribute('data-technical-name');
            if (technicalName) {
                if (field.type === 'checkbox') {
                    values[technicalName] = field.checked;
                } else if (field.type === 'radio') {
                    if (field.checked) {
                        values[technicalName] = field.value;
                    }
                } else {
                    values[technicalName] = field.value;
                }
            }
        });

        // Include calculated fields for accurate operation cost calculations
        try {
            // Primary method: Use global calculateDynamicFields function
            if (typeof window.calculateDynamicFields === 'function') {
                const calculatedFields = window.calculateDynamicFields(values);
                Object.assign(values, calculatedFields);
            } else {
                // Fallback method: Use internal calculated fields logic
                const calculatedFields = this.calculateDynamicFieldsFallback(values);
                if (calculatedFields && Object.keys(calculatedFields).length > 0) {
                    Object.assign(values, calculatedFields);
                }
            }
        } catch (error) {
            console.warn('[OperationCosts] Error calculating dynamic fields:', error);
            // Continue without calculated fields - operation costs can still work
        }

        return values;
    }

    /**
     * Fallback method for calculating dynamic fields when global function unavailable
     * Implements simplified version of calculateDynamicFields logic
     */
    calculateDynamicFieldsFallback(fieldValues) {
        try {
            if (typeof window.calculatedFieldsDefinitions !== 'undefined' &&
                window.calculatedFieldsDefinitions &&
                window.calculatedFieldsDefinitions.length > 0) {

                const results = {};
                const context = { ...fieldValues };

                // Add math functions to context
                context.Math = Math;
                context.min = Math.min;
                context.max = Math.max;
                // ... other math functions

                // Multi-pass calculation to handle dependencies
                const maxPasses = 5;
                let hasChanges = true;
                let pass = 0;

                while (hasChanges && pass < maxPasses) {
                    hasChanges = false;
                    pass++;

                    window.calculatedFieldsDefinitions.forEach((calcField) => {
                        try {
                            // Update context with latest results
                            Object.assign(context, results);

                            // Create safe context with proxy for undefined variables
                            const safeContext = new Proxy(context, {
                                get: (target, prop) => {
                                    if (prop in target) return target[prop];
                                    if (prop.startsWith('_CALCULATED_')) return null;
                                    return null;
                                }
                            });

                            // Evaluate formula with error handling
                            const func = new Function(...Object.keys(safeContext),
                                `try { return (${calcField.formula}); } catch(e) { return null; }`);
                            const result = func(...Object.values(safeContext));

                            if (results[calcField.name] !== result) {
                                results[calcField.name] = result;
                                hasChanges = true;
                            }
                        } catch (error) {
                            console.error(`Error calculating ${calcField.name}:`, error);
                            results[calcField.name] = null;
                        }
                    });
                }

                return results;
            }
        } catch (error) {
            console.warn('Error in calculateDynamicFieldsFallback:', error);
        }
        return {};
    }
}
```

#### Key Features
- **Calculated Fields Integration**: Automatically includes calculated field values in operation cost calculations
- **Dual Method Approach**: Uses global function when available, falls back to internal logic
- **Error Resilience**: Continues to work even if calculated fields fail
- **Performance Optimized**: Multi-pass calculation with dependency handling
- **Backward Compatible**: Works with existing operation costs functionality

#### Smart Timing Implementation
The operation costs handler now intelligently waits for the configurator to be fully ready before attempting to collect field values:

```javascript
/**
 * Wait for the configurator to be fully ready before attempting to collect field values
 * This ensures calculated fields are available when we try to get field values
 */
waitForConfiguratorReady() {
    const maxAttempts = 10;
    let attempts = 0;
    
    const checkReady = () => {
        attempts++;
        
        // Check if configurator is ready
        const isReady = this.isConfiguratorReady();
        
        if (isReady) {
            this.refreshOperationCosts();
        } else if (attempts < maxAttempts) {
            // Retry after delay
            setTimeout(checkReady, 1000);
        } else {
            console.warn('[OperationCosts] Configurator not ready after maximum attempts, proceeding anyway');
            this.refreshOperationCosts();
        }
    };
    
    // Start checking after a short delay
    setTimeout(checkReady, 500);
}

/**
 * Check if the configurator is ready by looking for key components
 */
isConfiguratorReady() {
    // Check if calculated fields definitions are loaded
    const hasCalculatedFields = window.calculatedFieldsDefinitions &&
        window.calculatedFieldsDefinitions.length > 0;
    
    // Check if calculateDynamicFields function is working
    const hasCalculateFunction = typeof window.calculateDynamicFields === 'function';
    
    // Check if we have some field values in the DOM
    const hasFieldValues = document.querySelectorAll('.config-field').length > 0;
    
    // Consider ready if we have the essential components
    return hasCalculateFunction && hasFieldValues;
}
```

#### Event-Driven Architecture
Multiple event listeners ensure the operation costs handler responds to configurator state changes:

```javascript
setupEventListeners() {
    // Listen for configurator ready events
    document.addEventListener('configurator:ready', (event) => {
        this.onConfiguratorReady();
    });

    document.addEventListener('configurator:calculated-fields-ready', (event) => {
        this.onConfiguratorReady();
    });

    // Listen for DOM changes that might indicate configurator is ready
    const observer = new MutationObserver((mutations) => {
        // Check if calculated fields definitions have been added
        if (window.calculatedFieldsDefinitions && window.calculatedFieldsDefinitions.length > 0) {
            this.onConfiguratorReady();
            observer.disconnect(); // Stop observing once we detect it
        }
    });

    // Start observing
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}
```

#### Global Accessibility
The `calculateDynamicFields` function is made globally accessible in `visibility_conditions.js`:

```javascript
// CALCULATED FIELDS FUNCTIONS: Expose to global scope for use by other modules
window.calculateDynamicFields = calculateDynamicFields;
window.calculatedFieldsDefinitions = calculatedFieldsDefinitions;
```

#### Usage
```javascript
// Initialize operation costs handler
window.operationCostsHandler = new OperationCostsHandler();

// The handler automatically integrates with the configurator
// and includes calculated fields in cost calculations

// You can also use the global function directly
const fieldValues = { door_width: 1000, door_height: 2100 };
const calculatedFields = window.calculateDynamicFields(fieldValues);
console.log('Calculated fields:', calculatedFields);
```

#### Benefits
1. **Accurate Cost Calculations**: Operation costs now include calculated field values
2. **Smart Timing**: Automatically waits for configurator readiness
3. **Error Resilience**: Continues working even if calculated fields fail
4. **Performance Optimized**: Efficient multi-pass calculation with dependency handling
5. **Event-Driven**: Responsive to configurator state changes
6. **Production Ready**: Clean, maintainable code without debug noise

## Security Implementation

### 1. Expression Validation
```python
def validate_expression(self, expression, allowed_vars=None):
    """Validate expression for security"""
    if not expression:
        return True, None
    
        # Check for dangerous patterns
        dangerous_patterns = [
        'import', 'exec', 'eval', '__', 'globals', 'locals',
        'open', 'file', 'system', 'subprocess'
        ]
        
        for pattern in dangerous_patterns:
            if pattern in expression.lower():
            return False, f"Dangerous pattern '{pattern}' not allowed"
    
    # Validate syntax
    try:
        ast.parse(expression)
    except SyntaxError as e:
        return False, f"Invalid syntax: {e}"
    
    return True, None

def safe_eval(self, expression, context):
    """Safely evaluate expression with restricted context"""
    # Validate expression first
    valid, error = self.validate_expression(expression)
    if not valid:
        raise ValueError(error)
    
    # Create safe context
    safe_context = {
        'abs': abs,
        'min': min,
        'max': max,
        'round': round,
        'len': len,
        'str': str,
        'int': int,
        'float': float,
        'bool': bool,
    }
    
    # Add allowed variables
    if context:
        safe_context.update(context)
    
    # Evaluate with restricted globals
    return eval(expression, {'__builtins__': {}}, safe_context)
```

### 2. Access Control
```python
# Record rules for data access
class ConfigMatrixConfiguration(models.Model):
    _name = 'config.matrix.configuration'
    
    def _check_access_rights(self, operation):
        """Check access rights for configuration"""
        if operation == 'read':
            # Users can read their own configurations
            if self.create_uid == self.env.user:
                return True
            
            # Admins can read all configurations
            if self.env.user.has_group('canbrax_configmatrix.group_config_matrix_admin'):
                return True
            
            return False
        
        return super()._check_access_rights(operation)

# Field-level security
class ConfigMatrixField(models.Model):
    _name = 'config.matrix.field'
    
    def _check_field_access(self, field_name):
        """Check field-level access"""
        if field_name in ['visibility_condition', 'formula']:
            # Only admins can access advanced fields
            if not self.env.user.has_group('canbrax_configmatrix.group_config_matrix_admin'):
                return False
        
        return True
```

## Testing Patterns

### 1. Unit Tests
```python
class TestConfigMatrixTemplate(TransactionCase):
    def setUp(self):
        super().setUp()
        self.template = self.env['config.matrix.template'].create({
            'name': 'Test Template',
            'code': 'TEST001',
            'product_template_id': self.env['product.template'].create({
                'name': 'Test Product'
            }).id,
        })
    
    def test_field_visibility(self):
        """Test field visibility conditions"""
        field = self.env['config.matrix.field'].create({
            'name': 'Test Field',
            'technical_name': 'test_field',
            'field_type': 'text',
            'section_id': self.env['config.matrix.section'].create({
                'name': 'Test Section',
                'matrix_id': self.template.id,
            }).id,
            'visibility_condition': 'test_value == "visible"',
        })
        
        self.assertTrue(field.evaluate_visibility({'test_value': 'visible'}))
        self.assertFalse(field.evaluate_visibility({'test_value': 'hidden'}))
    
    def test_component_mapping(self):
        """Test component mapping and BOM generation"""
        # Create test data
        field = self.env['config.matrix.field'].create({
            'name': 'Door Type',
            'technical_name': 'door_type',
            'field_type': 'selection',
            'section_id': self.env['config.matrix.section'].create({
                'name': 'Door',
                'matrix_id': self.template.id,
            }).id,
        })
        
        option = self.env['config.matrix.option'].create({
            'name': 'Sliding Door',
            'value': 'sliding',
            'field_id': field.id,
        })
        
        component = self.env['product.product'].create({
            'name': 'Sliding Door Component',
            'type': 'product',
        })
        
        mapping = self.env['config.matrix.component.mapping'].create({
            'field_id': field.id,
            'option_id': option.id,
            'component_product_id': component.id,
            'quantity': 1.0,
        })
        
        # Test BOM generation
        configuration = self.template.generate_configuration({
            'door_type': 'sliding'
        })
        
        bom = configuration.generate_bom()
        self.assertIsNotNone(bom)
        self.assertEqual(len(bom.bom_line_ids), 1)
        self.assertEqual(bom.bom_line_ids[0].product_id, component)
```

### 2. Integration Tests
```python
class TestConfigMatrixIntegration(TransactionCase):
    def test_sales_integration(self):
        """Test complete sales workflow"""
        # Create template and configuration
        template = self.create_test_template()
        configuration = template.generate_configuration({
            'door_type': 'sliding',
            'width': 1000,
            'height': 2000,
        })
        
        # Create sales order
        sale_order = self.env['sale.order'].create({
            'partner_id': self.env['res.partner'].create({
                'name': 'Test Customer'
            }).id,
        })
        
        sale_line = self.env['sale.order.line'].create({
            'order_id': sale_order.id,
            'product_id': template.product_template_id.product_variant_id.id,
            'configuration_id': configuration.id,
        })
        
        # Test configuration integration
        self.assertEqual(sale_line.configuration_id, configuration)
        self.assertIsNotNone(configuration.bom_id)
        self.assertGreater(configuration.total_price, 0)
```

## Deployment Guidelines

### 1. Production Setup
```python
# Configuration for production
class ConfigMatrixSettings(models.TransientModel):
    _inherit = 'res.config.settings'
    
    config_matrix_cache_enabled = fields.Boolean(
        string='Enable Caching',
        default=True,
        help='Enable caching for better performance'
    )
    
    config_matrix_debug_mode = fields.Boolean(
        string='Debug Mode',
        default=False,
        help='Enable debug mode for development'
    )
    
    config_matrix_max_fields = fields.Integer(
        string='Maximum Fields per Template',
        default=100,
        help='Maximum number of fields allowed per template'
    )
```

### 2. Monitoring and Logging
```python
import logging
_logger = logging.getLogger(__name__)

class ConfigMatrixTemplate(models.Model):
    _name = 'config.matrix.template'
    
    def generate_configuration(self, field_values):
        """Generate configuration with logging"""
        _logger.info(f"Generating configuration for template {self.name}")
        
        try:
            configuration = super().generate_configuration(field_values)
            _logger.info(f"Configuration generated successfully: {configuration.id}")
            return configuration
        except Exception as e:
            _logger.error(f"Error generating configuration: {e}")
            raise
```

### 3. Performance Monitoring
```python
import time
from contextlib import contextmanager

@contextmanager
def performance_monitor(operation_name):
    """Monitor performance of operations"""
    start_time = time.time()
    try:
        yield
    finally:
        duration = time.time() - start_time
        _logger.info(f"{operation_name} completed in {duration:.2f} seconds")
        if duration > 5.0:  # Log slow operations
            _logger.warning(f"Slow operation detected: {operation_name} took {duration:.2f} seconds")

# Usage
def generate_bom(self):
    with performance_monitor("BOM Generation"):
        return self._generate_bom_implementation()
```

## Best Practices Summary

### 1. Code Organization
- Separate business logic from UI logic
- Use computed fields for derived values
- Implement proper error handling
- Follow Odoo 18 standards and patterns

### 2. Performance
- Use caching for expensive operations
- Implement debouncing for UI updates
- Optimize database queries
- Monitor performance metrics

### 3. Security
- Validate all user inputs
- Use safe expression evaluation
- Implement proper access control
- Log security events

### 4. Testing
- Write comprehensive unit tests
- Test integration scenarios
- Validate performance under load
- Test security controls

### 5. Documentation
- Document all public APIs
- Provide usage examples
- Keep documentation updated
- Include troubleshooting guides

This developer guide provides a comprehensive foundation for implementing and extending the ConfigMatrix system. Follow these patterns and best practices to ensure maintainable, performant, and secure code.
