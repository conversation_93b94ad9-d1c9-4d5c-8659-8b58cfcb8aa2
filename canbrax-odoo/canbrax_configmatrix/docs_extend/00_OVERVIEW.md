# ConfigMatrix: Advanced Dynamic Product Configuration & BOM Generation

## Overview

ConfigMatrix is a comprehensive product configuration system for Odoo 18 that provides advanced dynamic configuration capabilities with automatic BOM generation, pricing matrices, visual previews, and seamless integration with sales and manufacturing workflows.

## Key Features

### Core Configuration System
- **Dynamic Configuration Interface**: Intuitive Q&A format with conditional visibility based on previous answers
- **Multi-Use Case Support**: Separate configurations for Check Measure, Sales/Quoting, and Online Sales
- **Real-time BOM Generation**: Automatic Bill of Materials creation based on configuration choices
- **Visual Product Previews**: SVG-based visual representations with conditional layers
- **Advanced Pricing System**: Multi-dimensional pricing matrices with labor time calculations
- **Component Mapping**: Flexible 1:many component relationships with dynamic conditions

### Advanced Features
- **Dynamic Help & Error Messages**: Context-sensitive help text and error messages using field placeholders
- **Dynamic Default Values**: Intelligent default values based on other field values
- **Range Validation**: Dynamic min/max values with expression-based constraints
- **Calculated Fields**: Formula-based computed fields with dependency tracking
- **Visibility Conditions**: Complex conditional logic for field and option visibility
- **Performance Optimization**: Caching, debouncing, and memory management for large configurations

### Integration & Workflows
- **Sales Integration**: Seamless integration with Odoo's sales workflow
- **Manufacturing Integration**: Configured BOMs flow directly to manufacturing orders
- **Website Portal**: Customer-facing configuration interface
- **Builder Portal**: Specialized interface for professional builders
- **Import/Export Tools**: Excel and JSON import/export capabilities
- **Template Management**: Comprehensive administrative interface

## Core Concepts

### 1. Configuration Templates
- Define the structure of configuration questions for each configurable product
- Support multiple use cases (Check Measure, Sales, Online)
- Include pricing matrices, labor calculations, and visual components
- Manage template lifecycle (Draft → Testing → Active → Archived)

### 2. Sections & Fields
- **Sections**: Organize questions into logical groups (Door, Hardware, Extrusions, etc.)
- **Fields**: Individual configuration questions with multiple types (text, number, selection, boolean, date)
- **Options**: Selection choices with individual component mappings and visibility rules
- **Calculated Fields**: Formula-based computed values with dependency tracking

### 3. Advanced Rules Engine
- **Visibility Conditions**: Show/hide fields and options based on complex conditions
- **Range Conditions**: Dynamic validation with expression-based min/max values
- **Dependencies**: Track field relationships for efficient updates
- **Dynamic Content**: Help text, error messages, and default values using field placeholders

### 4. Component Mapping System
- **Unified Component Mapping**: 1:many relationships between fields and components
- **Dynamic Conditions**: Include components based on configuration values
- **Quantity Formulas**: Calculate component quantities using expressions
- **Product Filtering**: Dynamic product selection based on reference values

### 5. Dynamic Pricing System
- **Dynamic Price Matrix Selection**: Automatically selects applicable price matrices based on conditions
- **Condition-Based Pricing**: Matrices with `is_sale_price_matrix=True` are evaluated against configuration values
- **Sales Fixed Pricing**: Additional pricing layer that runs as separate step
- **Multi-Matrix Support**: All applicable matrices contribute to total pricing
- **Labor Time Matrices**: Calculate labor costs and times
- **Matrix Categories**: Organize pricing matrices by type and purpose
- **Special Conditions**: Handle complex pricing scenarios (mid-rails, cross-braces, etc.)

### 6. Visual Components
- **SVG Components**: Vector-based visual representations
- **Conditional Layers**: Show/hide visual elements based on configuration
- **Instructional Images**: Guide users through measurement and selection processes
- **Preview Panels**: Real-time visual feedback during configuration

## User Workflows

### 1. Administrative Setup
1. Create configuration templates for configurable products
2. Define sections and fields with appropriate types and validation
3. Set up visibility conditions and dependencies
4. Configure component mappings and pricing matrices
5. Add visual components and instructional materials
6. Test configurations across all use cases

### 2. Sales Configuration
1. User selects a configurable product in the sales order
2. "Configure" button appears next to the product line
3. ConfigMatrix form opens with relevant questions for the use case
4. Questions appear/disappear based on previous answers and rules
5. Real-time validation and dynamic help text guide the user
6. Visual preview updates as configuration progresses
7. BOM and pricing calculated automatically
8. Configuration saved and applied to sales order

### 3. Manufacturing Integration
1. Configured products flow to manufacturing orders
2. BOM automatically generated with correct components and quantities
3. Labor times and costs calculated from matrices
4. Manufacturing orders include all configuration details
5. Quality control can reference original configuration

### 4. Online Sales
1. Customer-facing portal interface for product configuration
2. Simplified interface optimized for customer use
3. Real-time pricing and availability checking
4. Configuration saved for future reference
5. Integration with e-commerce workflow

## Technical Architecture

### Backend Models
- **Core Models**: Template, Section, Field, Option, Configuration
- **Advanced Models**: Visibility Conditions, Dependencies, Calculated Fields
- **Pricing Models**: Price Matrix, Labor Time Matrix, Matrix Categories
- **Component Models**: Component Mapping, Option Component Mapping
- **Visual Models**: SVG Components, Conditional Layers

### Frontend Components
- **OWL Components**: Modern JavaScript framework for UI components
- **Configurator Widget**: Main configuration interface
- **Visual Editor**: Matrix editing and management tools
- **Expression Builder**: Complex condition and formula creation
- **Performance Optimizations**: Caching, debouncing, memory management

### Integration Points
- **Sales Integration**: Sale Order Line extensions and workflow integration
- **Manufacturing Integration**: BOM generation and manufacturing order creation
- **Product Integration**: Product template extensions and configuration flags
- **Website Integration**: Portal controllers and customer-facing interfaces

## Implementation Status

The ConfigMatrix module is fully implemented with:
- ✅ Complete backend data models and business logic
- ✅ Advanced frontend components with OWL framework
- ✅ Comprehensive pricing and labor calculation system
- ✅ Visual components and SVG rendering
- ✅ Performance optimizations and caching
- ✅ Multi-use case support (Check Measure, Sales, Online)
- ✅ Import/export capabilities
- ✅ Administrative tools and wizards
- ✅ Security and access control
- ✅ Documentation and user guides

## Requirements

- **Odoo 18.0+**: Full compatibility with Odoo 18 features and syntax
- **Dependencies**: `base`, `product`, `sale_management`, `mrp`, `web`, `website_sale`
- **Performance**: Optimized for large configurations with hundreds of fields
- **Security**: Role-based access control with user, admin, and builder groups

## Quick Start

1. **Install the module** and configure security groups
2. **Create configuration templates** for configurable products
3. **Set up sections and fields** with appropriate types and validation
4. **Configure component mappings** and pricing matrices
5. **Test configurations** across all use cases
6. **Deploy to production** with appropriate user training

See the detailed documentation files in this directory for specific implementation guidance.

## Troubleshooting

For operation cost calculation issues and other common problems, see:
- **`OPERATION_COST_TROUBLESHOOTING.md`**: Comprehensive troubleshooting guide for operation cost inconsistencies
- **`LOGGING_IMPLEMENTATION_GUIDE.md`**: Logging and debugging implementation
- **`PERFORMANCE_TESTING_GUIDE.md`**: Performance testing and optimization
