<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- Sales Fixed Pricing List View -->
    <record id="view_config_matrix_sales_fixed_pricing_list" model="ir.ui.view">
        <field name="name">config.matrix.sales.fixed.pricing.list</field>
        <field name="model">config.matrix.sales.fixed.price</field>
        <field name="arch" type="xml">
            <list string="Sales Fixed Pricing">
                <field name="sequence" widget="handle"/>
                <field name="template_id"/>
                <field name="description"/>
                <field name="value_cost" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                <field name="condition"/>
            </list>
        </field>
    </record>

    <!-- Sales Fixed Pricing Form View -->
    <record id="view_config_matrix_sales_fixed_pricing_form" model="ir.ui.view">
        <field name="name">config.matrix.sales.fixed.pricing.form</field>
        <field name="model">config.matrix.sales.fixed.price</field>
        <field name="arch" type="xml">
            <form string="Sales Fixed Pricing">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="description" placeholder="Fixed Pricing Description"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="template_id" options="{'no_create': True}"/>
                            <field name="sequence"/>
                        </group>
                        <group>
                            <field name="value_cost" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                            <field name="currency_id" invisible="1"/>
                            <field name="active" invisible="1"/>
                        </group>
                    </group>
                    <group string="Conditions">
                        <field name="condition" placeholder="Primary condition expression (e.g., door_height >= 2000 and door_type == 'sliding')"/>
                    </group>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Sales Fixed Pricing Search View -->
    <record id="view_config_matrix_sales_fixed_pricing_search" model="ir.ui.view">
        <field name="name">config.matrix.sales.fixed.pricing.search</field>
        <field name="model">config.matrix.sales.fixed.price</field>
        <field name="arch" type="xml">
            <search string="Sales Fixed Pricing">
                <field name="description"/>
                <field name="template_id"/>
                <field name="condition"/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Template" name="group_template" context="{'group_by': 'template_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Sales Fixed Pricing Action -->
    <record id="action_config_matrix_sales_fixed_pricing" model="ir.actions.act_window">
        <field name="name">Sales Fixed Pricing</field>
        <field name="res_model">config.matrix.sales.fixed.price</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first sales fixed pricing rule!
            </p>
        </field>
    </record>
</odoo>
