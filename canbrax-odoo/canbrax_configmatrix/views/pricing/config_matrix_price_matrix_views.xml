<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Price Matrix Tree View -->
    <record id="view_config_matrix_price_matrix_tree" model="ir.ui.view">
        <field name="name">config.matrix.price.matrix.tree</field>
        <field name="model">config.matrix.price.matrix</field>
        <field name="arch" type="xml">
            <list string="Price Grid">
                <field name="name"/>
                <field name="product_template_id"/>
                <field name="matrix_type"/>
                <field name="currency_id"/>
                <field name="last_imported"/>
                <field name="active" invisible="1"/>
            </list>
        </field>
    </record>

    <!-- Price Matrix Form View -->
    <record id="view_config_matrix_price_matrix_form" model="ir.ui.view">
        <field name="name">config.matrix.price.matrix.form</field>
        <field name="model">config.matrix.price.matrix</field>
        <field name="arch" type="xml">
            <form string="Price Grid">
                <header>
                    <button name="export_to_csv" string="Export CSV" type="object" class="btn-secondary" icon="fa-download"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1><field name="name" placeholder="Matrix Name"/></h1>
                    </div>
                    <group>
                        <group>
                            <field name="product_template_id" options="{'no_create': True}"/>
                            <field name="matrix_id"/>
                            <field name="is_sale_price_matrix"/>
                            <field name="sales_price_application"/>
                            <field name="height_calculated_field_id" />
                            <field name="width_calculated_field_id" />
                        </group>
                        <group>
                        </group>
                    </group>
                    <notebook>
                        <page string="Matrix Configuration" name="configuration">
                            <!-- Dimension Configuration Toolbar -->
                            <div class="dimension-toolbar mb-3">
                                <div class="toolbar-actions">
                                    <button name="action_add_height_range" type="object" string="Add Height Range" class="btn btn-primary me-2" icon="fa-plus"/>
                                    <button name="action_add_width_range" type="object" string="Add Width Range" class="btn btn-primary me-2" icon="fa-plus"/>
                                    <button name="action_generate_standard_ranges" type="object" string="Generate Standard Ranges" class="btn btn-info me-2" icon="fa-magic"/>
                                </div>
                            </div>

                            <!-- Visual Dimension Editor -->
                            <div class="row">
                                <div class="col-md-6">
                                    <group string="Height Ranges (Y-Axis)">
                                        <field name="height_ranges" widget="dimension_ranges_editor" nolabel="1"
                                               context="{'dimension_type': 'height'}"/>
                                    </group>
                                </div>
                                <div class="col-md-6">
                                    <group string="Width Ranges (X-Axis)">
                                        <field name="width_ranges" widget="dimension_ranges_editor" nolabel="1"
                                               context="{'dimension_type': 'width'}"/>
                                    </group>
                                </div>
                            </div>

                            <!-- Advanced JSON Editor (Collapsible) -->
                            <div class="mt-4">
                                <details>
                                    <summary class="text-muted" style="cursor: pointer;">
                                        <i class="fa fa-code"/> Advanced: Edit JSON Data Directly
                                    </summary>
                                    <div class="mt-3">
                                        <group string="Raw JSON Data (Advanced Users Only)">
                                            <div class="alert alert-warning" role="alert">
                                                <p><strong>Warning:</strong> Only edit these fields if you understand the JSON format. Use the visual editors above for normal configuration.</p>
                                            </div>
                                            <field name="height_ranges" widget="code" options="{'mode': 'javascript'}" string="Height Ranges JSON"/>
                                            <field name="width_ranges" widget="code" options="{'mode': 'javascript'}" string="Width Ranges JSON"/>
                                            <field name="matrix_data" widget="code" options="{'mode': 'javascript'}" string="Matrix Data JSON"/>
                                        </group>
                                    </div>
                                </details>
                            </div>

                            <!-- Matrix Cells Section -->
                            <div class="mt-5">
                                <h3>Matrix Cells</h3>
                                <field name="matrix_data" invisible="1"/>

                                <!-- Visual Matrix Editor Integration -->
                                <div class="matrix-visual-editor-container">
                                    <div class="matrix-toolbar mb-3">
                                        <div class="toolbar-actions">
                                            <button name="action_export_to_csv" type="object" string="Export CSV" class="btn btn-outline-secondary me-2" icon="fa-download"/>
                                        </div>
                                    </div>

                                    <!-- Interactive Matrix Display -->
                                    <field name="matrix_data" widget="price_matrix_visual" nolabel="1"/>
                                </div>
                            </div>
                        </page>
                        <page string="Special Conditions" name="conditions">
                            <group string="Special Conditions">
                                <div class="alert alert-info" role="alert">
                                    <p><strong>Special Conditions</strong></p>
                                    <p>Define special conditions like mid-rail requirements, cross-brace options, etc. Format: {"height_width": ["condition1", "condition2"]}</p>
                                </div>
                                <field name="special_conditions" widget="code" options="{'mode': 'javascript'}" nolabel="1"/>
                            </group>
                        </page>
                        <page string="General" name="general">
                            <group>
                                <group>
                                    <field name="matrix_type"/>
                                    <field name="currency_id"/>
                                    <field name="sequence"/>
                                </group>
                                <group>
                                    <field name="active"/>
                                    <field name="last_imported"/>
                                    <field name="import_source"/>
                                </group>
                            </group>
                            <group string="Description">
                                <field name="description" nolabel="1"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Price Matrix Search View -->
    <record id="view_config_matrix_price_matrix_search" model="ir.ui.view">
        <field name="name">config.matrix.price.matrix.search</field>
        <field name="model">config.matrix.price.matrix</field>
        <field name="arch" type="xml">
            <search string="Search Price Grid">
                <field name="name"/>
                <field name="product_template_id"/>
                <separator/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <filter string="Price Grid" name="price_matrix" domain="[('matrix_type', '=', 'price')]"/>
                <filter string="Cost Grid" name="cost_matrix" domain="[('matrix_type', '=', 'cost')]"/>
                <group expand="0" string="Group By">
                    <filter string="Product" name="group_by_product" context="{'group_by': 'product_template_id'}"/>
                    <filter string="Matrix Type" name="group_by_type" context="{'group_by': 'matrix_type'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Price Matrix Action -->
    <record id="action_config_matrix_price_matrix" model="ir.actions.act_window">
        <field name="name">Price Grid</field>
        <field name="res_model">config.matrix.price.matrix</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first price grid!
            </p>
            <p>
                Price grids define sale prices based on product dimensions (height and width).
            </p>
        </field>
    </record>
</odoo>
