# -*- coding: utf-8 -*-
import logging
import math
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError

try:
    from odoo.tools.safe_eval import safe_eval
except ImportError:
    # Fallback if safe_eval is not available
    def safe_eval(expr, ctx):
        # Create a safe function to evaluate expressions
        # This is less secure than Odoo's safe_eval but works as a fallback
        return eval(expr, {"__builtins__": {}}, ctx)

_logger = logging.getLogger(__name__)


class ConfigMatrixSalesFixedPrice(models.Model):
    _name = 'config.matrix.sales.fixed.price'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Sales Fixed Pricing for Configurable Products'
    _order = 'sequence, description'

    # Basic Information
    sequence = fields.Integer('Sequence', default=10, help='Order of display')
    active = fields.Boolean('Active', default=True)

    # Template Link (as specified by user)
    template_id = fields.Many2one(
        'config.matrix.template',
        "Template",
        help="The configuration template this fixed pricing applies to"
    )

    # Description (as specified by user)
    description = fields.Text("Description", help="Detailed description of the fixed pricing")

    # Fixed Price (as specified by user - using value_cost field name)
    value_cost = fields.Float(
        "Fixed Price", 
        digits='Product Price',
        help="The fixed price to apply when conditions are met"
    )

    # Conditions (as specified by user - using code field name)
    code = fields.Char(
        "Conditions",
        help="Python expression to evaluate when determining if this fixed price should apply. Use field names from the configuration template."
    )

    # NEW: Additional condition field for evaluation (similar to operation mapping)
    condition = fields.Char(
        "Additional Condition",
        help="Extra condition for this operation (beyond target selection)"
    )

    # Company and Currency
    company_id = fields.Many2one(
        'res.company', 
        'Company',
        default=lambda self: self.env.company,
        help='Company this pricing applies to'
    )
    currency_id = fields.Many2one(
        'res.currency', 
        'Currency',
        related='company_id.currency_id', 
        readonly=True,
        help='Currency for pricing'
    )

    # Constraints
    _sql_constraints = [
        ('unique_code_per_template', 'unique(template_id, code)', 
         'Conditions code must be unique per template!')
    ]

    def evaluate_conditions(self, configuration_values):
        """
        Evaluate if the conditions are met for the given configuration values
        
        Args:
            configuration_values (dict): Configuration field values
            
        Returns:
            bool: True if conditions are met, False otherwise
        """
        self.ensure_one()
        
        # Use the new condition field if available, otherwise fall back to code field
        condition_to_evaluate = self.condition or self.code
        
        if not condition_to_evaluate:
            return False  # No conditions means always apply
        
        try:
            # Create safe context for evaluation
            python_formula = self.env['config.matrix.calculated.field']._convert_js_to_python(condition_to_evaluate)
            
            # Create safe evaluation context with configuration values
            ctx = dict(configuration_values)
            
            # Add math functions
            math_context = {
                'round': round,
                'ceil': math.ceil,
                'floor': math.floor,
                'abs': abs,
                'max': max,
                'min': min,
                'sum': sum,
                'parseFloat': float,
                'parseInt': int,
                'Number': float,
                'Math': type('Math', (), {
                    'max': max, 'min': min, 'abs': abs, 'ceil': math.ceil,
                    'floor': math.floor, 'sqrt': math.sqrt
                })()
            }
            bool_context = {
                'true': True,
                'false': False,
                'True': True,
                'False': False,
            }
            ctx.update(bool_context)
            ctx.update(math_context)
            
            # Evaluate the condition
            result = safe_eval(python_formula, ctx)
            return bool(result)
            
        except Exception as e:
            _logger.error(f"Error evaluating conditions for fixed pricing {self.id}: {e}")
            return False

    def get_applicable_fixed_price(self, configuration_values):
        """
        Get the fixed price if conditions are met
        
        Args:
            configuration_values (dict): Configuration field values
            
        Returns:
            float: Fixed price if conditions met, 0.0 otherwise
        """
        self.ensure_one()
        
        if self.evaluate_conditions(configuration_values):
            return self.value_cost
        return 0.0

    @api.constrains('value_cost')
    def _check_value_cost(self):
        """Validate fixed price value"""
        for record in self:
            # Allow negative values for discounts
            pass  # No validation needed for value_cost

    @api.constrains('code')
    def _check_code_security(self):
        """Validate conditions for security"""
        for record in self:
            if record.code:
                # Check for dangerous patterns
                dangerous_patterns = [
                    'import', 'exec', 'eval', '__', 'open', 'file',
                    'system', 'subprocess', 'os.', 'sys.', 'globals'
                ]
                code_lower = record.code.lower()
                for pattern in dangerous_patterns:
                    if pattern in code_lower:
                        raise ValidationError(
                            _("Conditions code contains invalid pattern: %s") % pattern
                        )

    @api.constrains('condition')
    def _check_condition_security(self):
        """Validate additional condition for security"""
        for record in self:
            if record.condition:
                # Check for dangerous patterns
                dangerous_patterns = [
                    'import', 'exec', 'eval', '__', 'open', 'file',
                    'system', 'subprocess', 'os.', 'sys.', 'globals'
                ]
                condition_lower = record.condition.lower()
                for pattern in dangerous_patterns:
                    if pattern in condition_lower:
                        raise ValidationError(
                            _("Additional condition contains invalid pattern: %s") % pattern
                        )

    def name_get(self):
        """Custom name display"""
        result = []
        for record in self:
            name = f"[{record.condition or record.code or 'No Conditions'}] {record.description or 'No description'}"
            if record.value_cost:
                name += f" - {record.currency_id.symbol}{record.value_cost}"
            result.append((record.id, name))
        return result

    def copy_data(self, default=None):
        """Override copy to add 'Copy' to the description"""
        default = dict(default or {})
        if 'description' not in default:
            default['description'] = _("%s (Copy)") % self.description
        return super().copy_data(default)

    @api.model
    def get_fixed_price_for_configuration(self, template_id, configuration_values):
        """
        Get the applicable fixed price for a configuration
        
        Args:
            template_id (int): Configuration template ID
            configuration_values (dict): Configuration field values
            
        Returns:
            dict: {
                'fixed_price': float,
                'pricing_entry': record,
                'conditions_met': bool
            }
        """
        try:
            # Find all fixed pricing entries for this template
            fixed_pricing_entries = self.search([
                ('template_id', '=', template_id),
                ('active', '=', True)
            ], order='sequence ASC')
            
            for entry in fixed_pricing_entries:
                if entry.evaluate_conditions(configuration_values):
                    return {
                        'fixed_price': entry.value_cost,
                        'pricing_entry': entry,
                        'conditions_met': True
                    }
            
            # No conditions met
            return {
                'fixed_price': 0.0,
                'pricing_entry': False,
                'conditions_met': False
            }
            
        except Exception as e:
            _logger.error(f"Error getting fixed price for template {template_id}: {e}")
            return {
                'fixed_price': 0.0,
                'pricing_entry': False,
                'conditions_met': False
            }
