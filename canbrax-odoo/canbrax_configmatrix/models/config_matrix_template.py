# -*- coding: utf-8 -*-

import json
import logging
from odoo import models, fields, api, tools, _
from .mesh_constants import MESH_SERIES_SELECTION
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)

class ConfigMatrixTemplate(models.Model):
    _name = 'config.matrix.template'
    _description = 'Configuration Template'
    _order = 'name'

    name = fields.Char("Template Name", required=True)
    product_template_id = fields.Many2one('product.template', "Product Template", required=True)
    description = fields.Text("Description")

    # Administrative fields
    active = fields.Boolean("Active", default=True)
    version = fields.Char("Version", default="1.0")
    code = fields.Char("Template Code", required=True)
    admin_notes = fields.Text("Administrative Notes")

    # Use case enablement
    enable_check_measure = fields.Boolean("Enable Check Measure", default=True,
                                       help="Enable the Check Measure use case for this template")
    enable_sales = fields.Boolean("Enable Sales/Quoting", default=True,
                               help="Enable the Sales/Quoting use case for this template")
    enable_online = fields.Boolean("Enable Online Sales", default=True,
                                help="Enable the Online Sales use case for this template")

    # Mesh hunting system (NEW)
    mesh_required = fields.Boolean("Mesh Required", default=False,
                                 help="Enable mesh hunting during Sales Order confirmation")
    mesh_series = fields.Selection(MESH_SERIES_SELECTION, string="Default Mesh Series",
       help="Default mesh series for this template")

    # Individual Panel Hunting Configuration (NEW)
    panel_quantity = fields.Selection([
        ('1', '1'),
        ('2', '2'),
        ('3', '3'),
        ('4', '4'),
        ('5', '5'),
        ('6', '6'),
        ('7', '7'),
        ('8', '8'),
    ], string="Panel Quantity", default='1',
       help="Number of panels required for this template")

    # TEMPORARY: Add missing field to prevent QWeb error
    mesh_panel_calculation_method = fields.Selection([
        ('simple_count', 'Simple Panel Count'),
        ('midrail_split', 'Midrail Split Calculation'),
        ('custom_formula', 'Custom Formula')
    ], string="Panel Calculation Method", default='midrail_split',
       help="Method to calculate mesh panel dimensions")

    # Calculated Field References for Panel Dimensions
    door_width_field_id = fields.Many2one(
        'config.matrix.calculated.field',
        domain="['|', ('template_ids', '=', False), ('name', 'ilike', 'width'),('template_ids', 'in', [id])]",
        string="Door Width Field",
        help="Name of the calculated field that contains the door width"
    )

    door_height_field_id = fields.Many2one(
        'config.matrix.calculated.field',
        domain="['|', ('template_ids', '=', False), ('name', 'ilike', 'height'),('template_ids', 'in', [id])]",
        string="Door Height Field",
        help="Name of the calculated field that contains the door height"
    )

    midrail_height_calculated_id = fields.Many2one(
        'config.matrix.calculated.field',
        domain="['|', ('template_ids', '=', False), ('template_ids', 'in', [id])]",
        string="Midrail Height Field",
        help="Select a calculated field for midrail height"
    )


    hunt_each_panel_separately = fields.Boolean(
        "Hunt Each Panel Separately",
        default=True,
        help="When enabled, each mesh panel will be hunted individually, allowing different sources for each panel"
    )

    def _get_calculated_fields(self):
        """Get calculated fields assigned to this template"""
        options = []

        # Get calculated fields assigned to this template (or global ones)
        if self.id:
            calc_fields = self.env['config.matrix.calculated.field'].search([
                '|',
                ('template_ids', '=', False),  # Global calculated fields
                ('template_ids', 'in', [self.id])  # Template-specific calculated fields
            ])
            for calc_field in calc_fields:
                options.append((calc_field.name, calc_field.description or calc_field.name))

        return options
    # Sections and fields
    section_ids = fields.One2many('config.matrix.section', 'matrix_id', "Sections")

    # Template management
    state = fields.Selection([
        ('draft', 'Draft'),
        ('testing', 'Testing'),
        ('active', 'Active'),
        ('archived', 'Archived')
    ], default='draft', string="Status")

    # Base products (always included)
    bom_product_ids = fields.Many2many(
        'product.product',
        'config_matrix_template_product_rel',
        'template_id', 'product_id',
        string="Base Components"
    )

    # Component mappings (TEMPORARILY DISABLED)
    # TODO: Fix the relationship with the unified component mapping model
    # component_mapping_ids = fields.One2many(
    #     'config.matrix.component.mapping',
    #     'matrix_id',
    #     string="Component Mappings",
    #     domain="[('target_model', '!=', False)]"
    # )



    # SVG Components for visualization
    svg_component_ids = fields.One2many(
        'config.matrix.svg.component',
        'template_id',
        string="SVG Components"
    )
    has_svg_preview = fields.Boolean(
        "Has SVG Preview",
        compute="_compute_has_svg_preview",
        store=True
    )

    # Combination-based component mappings (deprecated)
    # combination_mapping_ids = fields.One2many(
    #     'config.matrix.combination.mapping',
    #     'template_id',
    #     string="Combination Mappings"
    # )

    # Virtual field to display options with component products
    option_component_mapping_ids = fields.One2many(
        'config.matrix.option.component.mapping',
        'template_id',
        string="Option Component Mappings",
        readonly=True
    )

    # Statistics
    configuration_count = fields.Integer(
        "Configurations",
        compute='_compute_configuration_count'
    )

    # Pricing Matrix Assignments
    matrix_assignment_ids = fields.One2many(
        'config.matrix.template.matrix.assignment',
        'template_id',
        string="Matrix Assignments"
    )

    # Pricing Grid Assignments
    mesh_price_grid_id = fields.Many2one(
        'config.matrix.price.matrix',
        "Mesh Grid",
        help="Price grid for mesh components"
    )
    frame_price_grid_id = fields.Many2one(
        'config.matrix.price.matrix',
        "Frame Grid",
        help="Price grid for frame components"
    )
    mulion_mohair_price_grid_id = fields.Many2one(
        'config.matrix.price.matrix',
        "Mulion Mohair Grid",
        help="Price grid for mulion mohair components"
    )
    plugh_price_grid_id = fields.Many2one(
        'config.matrix.price.matrix',
        "Plugh Grid",
        help="Price grid for plugh components"
    )

    # Helper fields for pricing
    has_price_matrices = fields.Boolean(
        "Has Price Matrices",
        compute='_compute_matrix_info',
        store=True
    )
    price_matrix_count = fields.Integer(
        "Price Matrices",
        compute='_compute_matrix_info',
        store=True
    )



    # NEW: Visibility condition statistics
    visibility_condition_count = fields.Integer(
        "Visibility Conditions",
        compute='_compute_visibility_stats',
        store=False
    )
    complex_condition_count = fields.Integer(
        "Complex Conditions",
        compute='_compute_visibility_stats',
        store=False
    )
    range_condition_count = fields.Integer(
        "Range Conditions",
        compute='_compute_visibility_stats',
        store=False
    )

    # NEW: Virtual fields for visibility management
    field_visibility_conditions = fields.One2many(
        'config.matrix.visibility.condition',
        'matrix_id',
        string="Field Visibility Conditions",
        compute='_compute_visibility_conditions',
        store=False
    )
    option_visibility_conditions = fields.One2many(
        'config.matrix.option.visibility',
        'matrix_id',
        string="Option Visibility Conditions",
        compute='_compute_visibility_conditions',
        store=False
    )

    # Constraints
    _sql_constraints = [
        ('code_uniq', 'unique (code)', 'Template code must be unique!')
    ]

    # BOM constraint removed to make import easier
    # @api.constrains('product_template_id')
    # def _check_product_template(self):
    #     for template in self:
    #         # Ensure product is manufacturable
    #         if not template.product_template_id.bom_ids:
    #             raise ValidationError(_("The product must have at least one bill of materials."))

    # open import wizard, add current template id to context
    def action_open_import_wizard(self):
        return {
            'name': _('Import Config Matrix Template'),
            'type': 'ir.actions.act_window',
            'res_model': 'replace.text.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_template_id': self.id
            }
        }

    @api.model_create_multi
    def create(self, vals_list):
        # Auto-generate code if not provided
        for vals in vals_list:
            if not vals.get('code'):
                product_id = vals.get('product_template_id')
                if product_id:
                    product = self.env['product.template'].browse(product_id)
                    vals['code'] = self._generate_template_code(product)
                else:
                    vals['code'] = self._generate_template_code()

        return super(ConfigMatrixTemplate, self).create(vals_list)

    def _generate_template_code(self, product=None):
        """Generate a unique template code"""
        if product:
            base_code = product.default_code or product.name
            # Remove spaces and special characters
            base_code = ''.join(e for e in base_code if e.isalnum())
            # Limit length and convert to uppercase
            base_code = base_code[:10].upper()
        else:
            base_code = 'TEMPLATE'

        # Add sequence number to ensure uniqueness
        existing = self.search_count([('code', 'like', f"{base_code}%")])
        return f"{base_code}{existing + 1:03d}"



    @api.depends('svg_component_ids')
    def _compute_has_svg_preview(self):
        """Determine if the template has a base SVG component for preview"""
        for template in self:
            template.has_svg_preview = bool(template.svg_component_ids.filtered(
                lambda c: c.component_type == 'base'
            ))

    def _compute_configuration_count(self):
        """Count the number of configurations for this template"""
        for template in self:
            template.configuration_count = self.env['config.matrix.configuration'].search_count([
                ('template_id', '=', template.id)
            ])

    @api.depends('matrix_assignment_ids', 'matrix_assignment_ids.matrix_type', 'matrix_assignment_ids.active')
    def _compute_matrix_info(self):
        """Compute matrix assignment information"""
        for template in self:
            active_assignments = template.matrix_assignment_ids.filtered('active')
            price_assignments = active_assignments.filtered(lambda a: a.matrix_type == 'price')

            template.has_price_matrices = bool(price_assignments)
            template.price_matrix_count = len(price_assignments)



    def _compute_visibility_stats(self):
        """Compute visibility condition statistics"""
        for template in self:
            # Get all field visibility conditions
            field_conditions = self.env['config.matrix.visibility.condition'].search([
                ('field_id.matrix_id', '=', template.id)
            ])

            # Get all option visibility conditions
            option_conditions = self.env['config.matrix.option.visibility'].search([
                ('option_id.matrix_id', '=', template.id)
            ])

            total_conditions = len(field_conditions) + len(option_conditions)

            # Count complex conditions (expression type)
            complex_field_conditions = field_conditions.filtered(lambda c: c.condition_type == 'expression')
            complex_option_conditions = option_conditions.filtered(lambda c: c.condition_type == 'expression')
            complex_count = len(complex_field_conditions) + len(complex_option_conditions)

            # Count range conditions (conditions with ternary operators or math functions)
            range_count = 0
            for condition in field_conditions + option_conditions:
                if condition.condition_type == 'expression' and condition.expression:
                    if ('?' in condition.expression and ':' in condition.expression) or 'abs(' in condition.expression:
                        range_count += 1
                elif condition.generated_condition:
                    if ('?' in condition.generated_condition and ':' in condition.generated_condition) or 'abs(' in condition.generated_condition:
                        range_count += 1

            template.visibility_condition_count = total_conditions
            template.complex_condition_count = complex_count
            template.range_condition_count = range_count

    def _compute_visibility_conditions(self):
        """Compute visibility condition lists for display"""
        for template in self:
            # Get field visibility conditions
            field_conditions = self.env['config.matrix.visibility.condition'].search([
                ('field_id.matrix_id', '=', template.id)
            ])
            template.field_visibility_conditions = field_conditions

            # Get option visibility conditions
            option_conditions = self.env['config.matrix.option.visibility'].search([
                ('option_id.matrix_id', '=', template.id)
            ])
            template.option_visibility_conditions = option_conditions

    def action_view_configurations(self):
        """View configurations for this template"""
        self.ensure_one()

        return {
            'name': _('Configurations'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.configuration',
            'view_mode': 'list,form',
            'domain': [('template_id', '=', self.id)],
            'context': {'default_template_id': self.id},
        }

    def action_set_to_draft(self):
        """Set template to draft state"""
        self.ensure_one()
        self.state = 'draft'

    def action_set_to_testing(self):
        """Set template to testing state"""
        self.ensure_one()
        self.state = 'testing'

    def action_set_to_active(self):
        """Set template to active state"""
        self.ensure_one()
        self.state = 'active'

    def action_archive(self):
        """Archive template"""
        self.ensure_one()
        self.state = 'archived'
        self.active = False

    def action_test_template(self):
        """Open test interface for template"""
        self.ensure_one()

        return {
            'name': _('Test Configuration Template'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.test.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_template_id': self.id
            }
        }

    def action_convert_all_visibility_conditions(self):
        """Convert all string visibility conditions to visibility_condition_ids records for all fields in the template"""
        self.ensure_one()

        # Get all fields with visibility conditions
        fields_with_conditions = self.env['config.matrix.field'].search([
            ('matrix_id', '=', self.id),
            ('visibility_condition', '!=', False)
        ])

        if not fields_with_conditions:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('No Conditions'),
                    'message': _('No fields with visibility conditions found in this template.'),
                    'type': 'info',
                }
            }

        # Convert each field's visibility condition
        converted_count = 0
        for field in fields_with_conditions:
            try:
                # Check if it's a JSON condition (from import)
                if field.visibility_condition.startswith('__JSON__'):
                    # Extract the JSON part
                    json_str = field.visibility_condition[8:]  # Remove __JSON__ prefix
                    conditions = json.loads(json_str)

                    # Clear existing conditions
                    field.visibility_condition_ids.unlink()

                    # Create new condition records
                    for i, cond in enumerate(conditions):
                        condition_expr = cond.get('condition', '')
                        logic = cond.get('logic', 'and')

                        # Create a custom expression condition
                        self.env['config.matrix.visibility.condition'].create({
                            'field_id': field.id,
                            'condition_type': 'expression',
                            'expression': condition_expr,
                            'logic_operator': logic,
                            'sequence': (i + 1) * 10,
                        })

                    converted_count += 1
                else:
                    # For simple string conditions (not JSON)
                    # Clear existing conditions
                    field.visibility_condition_ids.unlink()

                    # Create a single expression condition
                    self.env['config.matrix.visibility.condition'].create({
                        'field_id': field.id,
                        'condition_type': 'expression',
                        'expression': field.visibility_condition,
                        'logic_operator': 'and',
                        'sequence': 10,
                    })

                    converted_count += 1
            except Exception as e:
                _logger.error(f"Failed to convert visibility condition for field {field.name}: {str(e)}")

        # Success message
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Conversion Successful'),
                'message': _('Converted visibility conditions for %s fields.') % converted_count,
                'type': 'success',
            }
        }



    def export_template(self):
        """Export template to JSON file"""
        self.ensure_one()

        # Store the simplified data in the context
        return {
            'type': 'ir.actions.act_url',
            'url': f'/config_matrix/export/{self.id}',
            'target': 'self',
        }

    def action_export_simple(self):
        """Export template to JSON file using the simplified export function"""
        self.ensure_one()

        return {
            'type': 'ir.actions.act_url',
            'url': f'/config_matrix/export_simple/{self.id}',
            'target': 'self',
        }

    def _prepare_export_data(self):
        """Prepare template data for export"""
        self.ensure_one()

        data = {
            'name': self.name,
            'code': self.code,
            'product_template': self.product_template_id.name,
            'version': self.version,
            'description': self.description or '',
            'admin_notes': self.admin_notes or '',
            'sections': [],
            'component_mappings': [],
            'combination_mappings': [],
            'base_components': []
        }

        # Export base components
        if self.bom_product_ids:
            for product in self.bom_product_ids:
                data['base_components'].append({
                    'product_code': product.default_code or '',
                    'product_name': product.name,
                    'product_id': product.id,
                })

        # Export sections and fields
        for section in self.section_ids:
            section_data = {
                'name': section.name,
                'sequence': section.sequence,
                'description': section.description or '',
                'fields': []
            }

            for field in section.field_ids:
                field_data = self._prepare_field_export_data(field)
                section_data['fields'].append(field_data)

            data['sections'].append(section_data)

        # Export component mappings (TEMPORARILY DISABLED)
        # TODO: Re-enable when component mapping relationship is fixed
        # if self.component_mapping_ids:
        #     for mapping in self.component_mapping_ids:
        #         mapping_data = {
        #             'component_product': mapping.component_product_id.name,
        #             'component_product_code': mapping.component_product_id.default_code or '',
        #             'component_product_id': mapping.component_product_id.id,
        #             'quantity_formula': mapping.quantity_formula or '1',
        #             'condition': mapping.condition or '',
        #         }
        #         data['component_mappings'].append(mapping_data)

        # Export option-specific component mappings
        _logger.info("Exporting option-specific component mappings")
        for section in self.section_ids:
            _logger.info(f"Processing section: {section.name}")
            for field in section.field_ids:
                _logger.info(f"Processing field: {field.name} (type: {field.field_type})")
                if field.field_type == 'selection':
                    for option in field.option_ids:
                        _logger.info(f"Processing option: {option.name} (value: {option.value})")
                        if option.component_product_id:
                            _logger.info(f"Option has component product: {option.component_product_id.name} (ID: {option.component_product_id.id})")

                            # Special handling for color fields
                            option_value = option.value
                            if field.name in ['Powder Coat Colour', 'Frame Colour']:
                                _logger.info(f"Special handling for color field '{field.name}' - option: {option.name}, value: {option.value}")
                                # Ensure the option value is properly formatted for this field
                                option_value = option.value.lower().replace(' ', '_').replace('-', '_')
                                _logger.info(f"Normalized option value: {option_value}")

                                # Extract color code from option name if present
                                import re
                                color_codes = re.findall(r'[A-Z]{2}\d{3}[A-Z]', option.name)
                                color_code = color_codes[0] if color_codes else None

                                if color_code:
                                    _logger.info(f"Found color code in option name: {color_code}")
                                    # Log the component product name to help with debugging
                                    _logger.info(f"Current component product: {option.component_product_id.name}")

                            # Make sure we have the component product code
                            component_product_code = option.component_product_id.default_code or ''
                            if not component_product_code and field.name in ['Powder Coat Colour', 'Frame Colour']:
                                # For color fields, try to generate a meaningful code if none exists
                                if color_code:
                                    base_code = 'CXDF-' + color_code  # Example: CXDF-GY276A
                                    _logger.info(f"Generated component product code: {base_code}")
                                    component_product_code = base_code

                            mapping_data = {
                                'component_product': option.component_product_id.name,
                                'component_product_code': component_product_code,
                                'component_product_id': option.component_product_id.id,
                                'quantity_formula': option.quantity_formula or '1',
                                'field_name': field.name,
                                'option_value': option_value,
                            }
                            data['component_mappings'].append(mapping_data)
                            _logger.info(f"Added option component mapping: {mapping_data}")
                        else:
                            _logger.info(f"Option has no component product")

        # Export combination mappings
        if self.combination_mapping_ids:
            for mapping in self.combination_mapping_ids:
                mapping_data = {
                    'name': mapping.name,
                    'component_product': mapping.component_product_id.name,
                    'component_product_code': mapping.component_product_id.default_code or '',
                    'component_product_id': mapping.component_product_id.id,
                    'quantity_formula': mapping.quantity_formula or '1',
                    'conditions': [],
                }

                # Add conditions
                if mapping.condition_ids:
                    for condition in mapping.condition_ids:
                        condition_data = {
                            'field_name': condition.field_id.name,
                            'field_technical_name': condition.field_id.technical_name,
                            'operator': condition.operator,
                            'value': condition.value,
                        }
                        mapping_data['conditions'].append(condition_data)

                data['combination_mappings'].append(mapping_data)

        return data

    # Removed duplicate method

    def _prepare_field_export_data(self, field):
        """Prepare field data for export"""
        field_data = {
            'name': field.name,
            'technical_name': field.technical_name,
            'field_type': field.field_type,
            'required': True,  # All fields are treated as required
            'default_value': field.check_measure_default_value or field.sales_default_value or field.online_default_value or '',
            'help_text': field.check_measure_help_text or field.sales_help_text or field.online_help_text or '',
            'visibility_condition': field.visibility_condition or '',
            'sequence': field.sequence,
        }

        # Add component product information if present
        if field.component_product_id:
            field_data.update({
                'component_product': field.component_product_id.name,
                'component_product_code': field.component_product_id.default_code or '',
                'quantity_formula': field.quantity_formula or '1',
            })

        # Add visibility conditions
        if hasattr(field, 'visibility_condition_ids') and field.visibility_condition_ids:
            field_data['visibility_conditions'] = []
            for condition in field.visibility_condition_ids:
                condition_data = {
                    'field_name': condition.field_id.name,
                    'field_technical_name': condition.field_id.technical_name,
                    'operator': condition.operator,
                    'value_type': condition.value_type,
                    'selection_value': condition.selection_value or '',
                    'numeric_value': condition.numeric_value or 0,
                    'text_value': condition.text_value or '',
                    'logic': condition.logic or 'and',
                }
                field_data['visibility_conditions'].append(condition_data)

        # Add type-specific properties
        if field.field_type == 'text':
            field_data.update({
                'min_length': field.min_length,
                'max_length': field.max_length,
                'pattern': field.pattern,
            })
        elif field.field_type == 'number':
            field_data.update({
                'decimal_precision': field.decimal_precision,
                'uom_id': field.uom_id.id if field.uom_id else False,
                'uom_name': field.uom_id.name if field.uom_id else '',
            })
        elif field.field_type == 'selection':
            field_data['options'] = []
            for option in field.option_ids:
                option_data = {
                    'value': option.value,
                    'name': option.name,
                    'visibility_condition': option.visibility_condition or '',
                }
                if option.component_product_id:
                    option_data['component_product'] = option.component_product_id.default_code or option.component_product_id.name
                    option_data['component_product_id'] = option.component_product_id.id
                if option.quantity_formula:
                    option_data['quantity_formula'] = option.quantity_formula

                # Add visibility conditions for options
                if hasattr(option, 'visibility_condition_ids') and option.visibility_condition_ids:
                    option_data['visibility_conditions'] = []
                    for condition in option.visibility_condition_ids:
                        condition_data = {
                            'field_name': condition.field_id.name,
                            'field_technical_name': condition.field_id.technical_name,
                            'operator': condition.operator,
                            'value_type': condition.value_type,
                            'selection_value': condition.selection_value or '',
                            'numeric_value': condition.numeric_value or 0,
                            'text_value': condition.text_value or '',
                            'logic': condition.logic or 'and',
                        }
                        option_data['visibility_conditions'].append(condition_data)

                field_data['options'].append(option_data)
        elif field.field_type == 'boolean':
            field_data.update({
                'boolean_true_label': field.boolean_true_label,
                'boolean_false_label': field.boolean_false_label,
            })

        # Add component mapping
        if field.component_product_id:
            field_data['component_product'] = field.component_product_id.default_code or field.component_product_id.name
            if field.quantity_formula:
                field_data['quantity_formula'] = field.quantity_formula

        return field_data

    @api.model
    def import_template(self, data):
        """Import template from JSON data"""
        if isinstance(data, str):
            data = json.loads(data)

        # Validate basic structure
        required_fields = ['name', 'code', 'product_template', 'sections']
        for field in required_fields:
            if field not in data:
                raise UserError(_("Invalid template data: Missing required field '%s'") % field)

        # Find product template
        product = self.env['product.template'].search([
            '|',
            ('name', '=', data['product_template']),
            ('default_code', '=', data['product_template'])
        ], limit=1)

        if not product:
            raise UserError(_("Product template '%s' not found") % data['product_template'])

        # Create template
        template_vals = {
            'name': data['name'],
            'code': data['code'],
            'product_template_id': product.id,
            'version': data.get('version', '1.0'),
            'description': data.get('description', ''),
            'state': 'draft',
        }

        template = self.create(template_vals)

        # Create sections and fields
        for section_data in data['sections']:
            section_vals = {
                'name': section_data['name'],
                'sequence': section_data.get('sequence', 10),
                'description': section_data.get('description', ''),
                'matrix_id': template.id,
            }

            section = self.env['config.matrix.section'].create(section_vals)

            # Create fields
            for field_data in section_data.get('fields', []):
                self._create_field_from_import(section, field_data)

        # Create component mappings
        for mapping_data in data.get('component_mappings', []):
            self._create_component_mapping_from_import(template, mapping_data)

        return template

    def _create_field_from_import(self, section, field_data):
        """Create field from import data"""
        # Find component product if specified
        component_product_id = False
        if field_data.get('component_product'):
            component_product = self.env['product.product'].search([
                '|',
                ('name', '=', field_data['component_product']),
                ('default_code', '=', field_data['component_product'])
            ], limit=1)
            if component_product:
                component_product_id = component_product.id

        # Prepare field values
        field_vals = {
            'name': field_data['name'],
            'technical_name': field_data['technical_name'],
            'field_type': field_data['field_type'],
            # Note: 'required' is not a field in the model - all fields are treated as required
            # Map default_value to all use-case specific default value fields
            'check_measure_default_value': field_data.get('default_value', ''),
            'sales_default_value': field_data.get('default_value', ''),
            'online_default_value': field_data.get('default_value', ''),
            # Map help_text to all use-case specific help text fields
            'check_measure_help_text': field_data.get('help_text', ''),
            'sales_help_text': field_data.get('help_text', ''),
            'online_help_text': field_data.get('help_text', ''),
            'visibility_condition': field_data.get('visibility_condition'),
            'section_id': section.id,
            'component_product_id': component_product_id,
            'quantity_formula': field_data.get('quantity_formula'),
        }

        # Add type-specific properties
        if field_data['field_type'] == 'text':
            field_vals.update({
                'min_length': field_data.get('min_length'),
                'max_length': field_data.get('max_length'),
                'pattern': field_data.get('pattern'),
            })
        elif field_data['field_type'] == 'number':
            field_vals.update({
                'min_value': field_data.get('min_value'),
                'max_value': field_data.get('max_value'),
                'decimal_precision': field_data.get('decimal_precision'),
                'unit_of_measure': field_data.get('unit_of_measure'),
            })
        elif field_data['field_type'] == 'boolean':
            field_vals.update({
                'boolean_true_label': field_data.get('boolean_true_label', 'Yes'),
                'boolean_false_label': field_data.get('boolean_false_label', 'No'),
            })

        # Create field
        field = self.env['config.matrix.field'].create(field_vals)

        # Create options for selection fields
        if field_data['field_type'] == 'selection' and field_data.get('options'):
            for option_data in field_data['options']:
                self._create_option_from_import(field, option_data)

        return field

    def _create_option_from_import(self, field, option_data):
        """Create field option from import data"""
        # Find component product if specified
        component_product_id = False
        if option_data.get('component_product'):
            component_product = self.env['product.product'].search([
                '|',
                ('name', '=', option_data['component_product']),
                ('default_code', '=', option_data['component_product'])
            ], limit=1)
            if component_product:
                component_product_id = component_product.id

        # Create option
        option_vals = {
            'field_id': field.id,
            'value': option_data['value'],
            'name': option_data['name'],
            'component_product_id': component_product_id,
            'quantity_formula': option_data.get('quantity_formula'),
        }

        return self.env['config.matrix.option'].create(option_vals)

    def _create_component_mapping_from_import(self, template, mapping_data):
        """Create component mapping from import data"""
        # Find component product
        component_product = self.env['product.product'].search([
            '|',
            ('name', '=', mapping_data['component_product']),
            ('default_code', '=', mapping_data['component_product'])
        ], limit=1)

        if not component_product:
            return False

        # Create mapping
        mapping_vals = {
            'template_id': template.id,
            'component_product_id': component_product.id,
            'quantity_formula': mapping_data.get('quantity_formula', '1'),
            'condition': mapping_data.get('condition'),
        }

        return self.env['config.matrix.component.mapping'].create(mapping_vals)

    @api.model
    def get_template_structure(self, template_id, use_case=None):
        """Get template structure for configurator UI with use case support"""
        # PERFORMANCE: Try cached version first, fallback to original if needed
        try:
            return self.get_template_structure_cached(template_id, use_case)
        except Exception as e:
            _logger.warning(f"Cached template loading failed, using original method: {e}")
            return self._get_template_structure_original(template_id, use_case)

    def _get_template_structure_original(self, template_id, use_case=None):
        """Original template structure method - preserved for compatibility"""
        template = self.browse(template_id)

        # Determine enabled use cases
        enabled_use_cases = []
        if template.enable_check_measure:
            enabled_use_cases.append('check_measure')
        if template.enable_sales:
            enabled_use_cases.append('sales')
        if template.enable_online:
            enabled_use_cases.append('online')

        # If no use cases are enabled, default to check_measure
        if not enabled_use_cases:
            enabled_use_cases = ['check_measure']

        # For sales order configurator, always use check_measure
        # This ensures we always show the full set of questions with check_measure defaults
        # when configuring from a sales order
        if self.env.context.get('from_sale_order'):
            active_use_case = 'check_measure'
        else:
            # Use provided use case or default to first enabled one
            active_use_case = use_case if use_case in enabled_use_cases else enabled_use_cases[0]

        # Get sections and fields based on the active use case
        sections = []
        for section in template.section_ids:
            # Get section data using the section's method which will handle use case visibility
            section_data = section.get_section_data(active_use_case)
            if section_data:  # Only include sections with visible fields
                sections.append(section_data)

        return {
            'template_id': template.id,
            'name': template.name,
            'product_id': template.product_template_id.product_variant_id.id,
            'sections': sections,
            'active_use_case': active_use_case,
            'enabled_use_cases': enabled_use_cases
        }

    @api.model
    def get_available_fields(self, template_id):
        """Get available fields for condition builder"""
        template = self.browse(template_id)

        fields = []
        for section in template.section_ids:
            for field in section.field_ids:
                field_data = {
                    'id': field.id,
                    'technical_name': field.technical_name,
                    'name': field.name,
                    'field_type': field.field_type,
                }

                if field.field_type == 'selection':
                    field_data['options'] = [
                        {'value': opt.value, 'name': opt.name}
                        for opt in field.option_ids
                    ]

                fields.append(field_data)

        return fields

    # PERFORMANCE: Optimized template loading methods
    @tools.ormcache('template_id', 'use_case')
    def get_template_structure_cached(self, template_id, use_case=None):
        """Cached version of get_template_structure"""
        return self._get_template_structure_uncached(template_id, use_case)

    def _get_template_structure_uncached(self, template_id, use_case=None):
        """Optimized template structure loading - compatible with original logic"""
        template = self.browse(template_id)

        # Determine enabled use cases (same logic as original)
        enabled_use_cases = []
        if template.enable_check_measure:
            enabled_use_cases.append('check_measure')
        if template.enable_sales:
            enabled_use_cases.append('sales')
        if template.enable_online:
            enabled_use_cases.append('online')

        # If no use cases are enabled, default to check_measure
        if not enabled_use_cases:
            enabled_use_cases = ['check_measure']

        # For sales order configurator, always use check_measure
        if self.env.context.get('from_sale_order'):
            active_use_case = 'check_measure'
        else:
            # Use provided use case or default to first enabled one
            active_use_case = use_case if use_case in enabled_use_cases else enabled_use_cases[0]

        # PERFORMANCE: Use optimized section loading, but fallback to original if needed
        try:
            sections_data = self._get_sections_optimized(template_id, active_use_case)
        except Exception as e:
            _logger.warning(f"Optimized section loading failed, using original: {e}")
            # Fallback to original section loading logic
            sections_data = []
            for section in template.section_ids:
                section_data = section.get_section_data(active_use_case)
                if section_data:  # Only include sections with visible fields
                    sections_data.append(section_data)

        return {
            'template_id': template.id,
            'name': template.name,
            'product_id': template.product_template_id.product_variant_id.id,
            'sections': sections_data,
            'active_use_case': active_use_case,
            'enabled_use_cases': enabled_use_cases
        }

    def _get_sections_optimized(self, template_id, use_case):
        """Optimized section and field loading with minimal queries"""
        # Load all sections and fields in one query with proper joins
        query = """
            SELECT
                s.id as section_id,
                s.name as section_name,
                s.sequence as section_sequence,
                s.description as section_description,
                f.id as field_id,
                f.name as field_name,
                f.technical_name as field_technical_name,
                f.field_type,
                f.sequence as field_sequence,
                f.visibility_condition,
                f.check_measure_visible,
                f.sales_visible,
                f.online_visible,
                f.check_measure_default_value,
                f.sales_default_value,
                f.online_default_value,
                f.check_measure_help_text,
                f.sales_help_text,
                f.online_help_text
            FROM config_matrix_section s
            LEFT JOIN config_matrix_field f ON f.section_id = s.id
            WHERE s.matrix_id = %s
            ORDER BY s.sequence, f.sequence
        """

        self.env.cr.execute(query, (template_id,))
        results = self.env.cr.dictfetchall()

        # Group results by section
        sections_dict = {}
        for row in results:
            section_id = row['section_id']

            if section_id not in sections_dict:
                sections_dict[section_id] = {
                    'id': section_id,
                    'name': row['section_name'],
                    'sequence': row['section_sequence'],
                    'description': row['section_description'] or '',
                    'fields': []
                }

            # Add field if it exists and is visible for the use case
            if row['field_id'] and self._is_field_visible_for_use_case(row, use_case):
                field_data = self._prepare_field_data_optimized(row, use_case)
                sections_dict[section_id]['fields'].append(field_data)

        # Convert to list and filter out empty sections
        sections = [section for section in sections_dict.values() if section['fields']]
        return sorted(sections, key=lambda s: s['sequence'])

    def _is_field_visible_for_use_case(self, field_row, use_case):
        """Check if field is visible for the given use case"""
        if use_case == 'check_measure':
            return field_row['check_measure_visible']
        elif use_case == 'sales':
            return field_row['sales_visible']
        elif use_case == 'online':
            return field_row['online_visible']
        return True

    def _prepare_field_data_optimized(self, field_row, use_case):
        """Prepare field data from database row"""
        # Get use case specific values
        if use_case == 'check_measure':
            default_value = field_row['check_measure_default_value']
            help_text = field_row['check_measure_help_text']
        elif use_case == 'sales':
            default_value = field_row['sales_default_value']
            help_text = field_row['sales_help_text']
        elif use_case == 'online':
            default_value = field_row['online_default_value']
            help_text = field_row['online_help_text']
        else:
            default_value = field_row['check_measure_default_value']
            help_text = field_row['check_measure_help_text']

        field_data = {
            'id': field_row['field_id'],
            'name': field_row['field_name'],
            'technical_name': field_row['field_technical_name'],
            'field_type': field_row['field_type'],
            'sequence': field_row['field_sequence'],
            'visibility_condition': field_row['visibility_condition'],
            'default_value': default_value,
            'help_text': help_text or '',
            'visible': True
        }

        # Load options for selection fields (separate query to avoid N+1)
        if field_row['field_type'] == 'selection':
            field_data['options'] = self._get_field_options_optimized(field_row['field_id'])

        return field_data

    @tools.ormcache('field_id')
    def _get_field_options_optimized(self, field_id):
        """Cached field options loading"""
        options = self.env['config.matrix.option'].search_read(
            [('field_id', '=', field_id)],
            ['value', 'name', 'sequence'],
            order='sequence'
        )
        return [{'value': opt['value'], 'name': opt['name']} for opt in options]

    @api.model
    def get_dependency_graph(self, template_id):
        """Pre-calculate dependency graph on backend for better performance"""
        template = self.browse(template_id)
        dependencies = {}

        # Load all fields with visibility conditions in one query
        fields = self.env['config.matrix.field'].search_read(
            [('matrix_id', '=', template_id), ('visibility_condition', '!=', False)],
            ['technical_name', 'visibility_condition']
        )

        for field in fields:
            if field['visibility_condition']:
                deps = self._extract_field_references(field['visibility_condition'])
                if deps:
                    dependencies[field['technical_name']] = list(deps)

        return dependencies

    def _extract_field_references(self, condition):
        """Extract field names from visibility condition"""
        import re

        # Handle JSON conditions
        if condition.startswith('__JSON__'):
            try:
                import json
                json_str = condition[8:]
                conditions = json.loads(json_str)
                field_names = set()
                for cond in conditions:
                    names = self._extract_field_names_from_expression(cond.get('condition', ''))
                    field_names.update(names)
                return field_names
            except:
                return set()
        else:
            return self._extract_field_names_from_expression(condition)

    def _extract_field_names_from_expression(self, expression):
        """Extract field names from an expression"""
        import re

        field_names = set()
        # Regular expression to find field names (alphanumeric + underscores)
        field_name_regex = r'\b([a-zA-Z][a-zA-Z0-9_]*)\b'
        matches = re.findall(field_name_regex, expression)

        if matches:
            # Filter out keywords and functions
            keywords = [
                'and', 'or', 'not', 'true', 'false', 'null', 'undefined',
                'Math', 'min', 'max', 'abs', 'round', 'floor', 'ceil',
                'sqrt', 'in', 'parseFloat', 'parseInt'
            ]

            for match in matches:
                if (match not in keywords and
                    not match.isdigit() and
                    len(match) > 1):
                    field_names.add(match)

        return field_names

    def get_configuration_price(self, configuration_values):
        """Get total price for a configuration using assigned price matrices and field-specific matrices

        Args:
            configuration_values: Dict of field technical names to values

        Returns:
            dict: {
                'price': float,
                'currency_id': int,
                'breakdown': [{
                    'matrix_name': str,
                    'price': float,
                    'dimensions': {'height': float, 'width': float},
                    'field_name': str (optional)
                }]
            }
        """
        self.ensure_one()
        _logger.info(f"[PRICE_MATRIX] get_configuration_price called for template {self.id} ({self.name})")
        _logger.info(f"[PRICE_MATRIX] Configuration values: {configuration_values}")

        total_price = 0.0
        breakdown = []
        currency = self.env.company.currency_id
        panel_quantity = self.panel_quantity and int(self.panel_quantity) or 1
        
        # Define price matrices with their corresponding dimension fields
        price_matrix_configs = []
        if self.mesh_price_grid_id:
            price_matrix_configs.append({
                'matrix': self.mesh_price_grid_id,
                'height_field': self.door_height_field_id.name,
                'width_field': self.door_width_field_id.name,
                'type': 'mesh'
            })
        if self.frame_price_grid_id:
            price_matrix_configs.append({
                'matrix': self.frame_price_grid_id,
                'height_field': self.door_height_field_id.name,
                'width_field': self.door_width_field_id.name,
                'type': 'frame'
            })
        if self.mulion_mohair_price_grid_id:
            price_matrix_configs.append({
                'matrix': self.mulion_mohair_price_grid_id,
                'height_field': self.door_height_field_id.name,
                'width_field': self.door_width_field_id.name,
                'type': 'mulion_mohair'
            })
        if self.plugh_price_grid_id:
            price_matrix_configs.append({
                'matrix': self.plugh_price_grid_id,
                'height_field': self.door_height_field_id.name,
                'width_field': self.door_width_field_id.name,
                'type': 'plugh'
            })
        
        if not price_matrix_configs:
            _logger.info(f"[PRICE_MATRIX] No price matrix found for template {self.id}")
            return {
                'price': 0.0,
                'currency_id': currency.id,
                'breakdown': []
            }

        # SPECIAL CONDITION EVALUATION: For mulion_mohair_price_grid_id cases,
        # evaluate special conditions on all price matrices with is_sale_price_matrix=True and sales_price_application='mullion_mohair'
        # and choose the first one that meets the conditions
        if self.mulion_mohair_price_grid_id:
            _logger.info(f"[PRICE_MATRIX] Evaluating special conditions for mulion_mohair_price_grid_id case")
            
            # Get all price matrices with is_sale_price_matrix=True
            sale_price_matrices = self.env['config.matrix.price.matrix'].search([
                ('is_sale_price_matrix', '=', True),
                ('sales_price_application', '=', 'mullion_mohair'),
                ('active', '=', True)
            ])
            
            _logger.info(f"[PRICE_MATRIX] Found {len(sale_price_matrices)} sale price matrices to evaluate")
            
            # Find the first matrix that meets special conditions
            selected_matrix = None
            for matrix in sale_price_matrices:
                try:
                    # Pass door height and width field names for proper dimension extraction
                    if matrix.evaluate_special_conditions(
                        configuration_values, 
                        door_height_field_name=self.door_height_field_id.name if self.door_height_field_id else None,
                        door_width_field_name=self.door_width_field_id.name if self.door_width_field_id else None
                    ):
                        selected_matrix = matrix
                        _logger.info(f"[PRICE_MATRIX] Selected matrix '{matrix.name}' (ID: {matrix.id}) based on special conditions")
                        break
                    else:
                        _logger.info(f"[PRICE_MATRIX] Matrix '{matrix.name}' (ID: {matrix.id}) failed special conditions")
                except Exception as e:
                    _logger.warning(f"[PRICE_MATRIX] Error evaluating special conditions for matrix '{matrix.name}': {str(e)}")
                    continue
            
            if selected_matrix:
                # Replace the mulion_mohair_price_grid_id with the selected matrix
                for config in price_matrix_configs:
                    if config['type'] == 'mulion_mohair':
                        config['matrix'] = selected_matrix
                        _logger.info(f"[PRICE_MATRIX] Replaced mulion_mohair_price_grid_id with selected matrix: {selected_matrix.name}")
                        break
            else:
                # No sale price matrix met special conditions, check the original mulion_mohair_price_grid_id
                _logger.info(f"[PRICE_MATRIX] No sale price matrix met special conditions, checking original mulion_mohair_price_grid_id")
                
                original_matrix = self.mulion_mohair_price_grid_id
                if original_matrix:
                    # Check if the original matrix has special_conditions
                    if original_matrix.special_conditions and original_matrix.special_conditions.strip():
                        _logger.info(f"[PRICE_MATRIX] Original matrix '{original_matrix.name}' has special_conditions, evaluating them")
                        
                        try:
                            # Evaluate the original matrix's special conditions
                            if original_matrix.evaluate_special_conditions(
                                configuration_values, 
                                door_height_field_name=self.door_height_field_id.name if self.door_height_field_id else None,
                                door_width_field_name=self.door_width_field_id.name if self.door_width_field_id else None
                            ):
                                # Original matrix conditions pass, use it for pricing
                                _logger.info(f"[PRICE_MATRIX] Original matrix '{original_matrix.name}' conditions passed, using it for pricing")
                                # Keep the original matrix in price_matrix_configs (no change needed)
                            else:
                                # Original matrix conditions failed, remove it from pricing
                                _logger.warning(f"[PRICE_MATRIX] Original matrix '{original_matrix.name}' conditions failed, removing from pricing")
                                # Remove the mulion_mohair config from price_matrix_configs
                                price_matrix_configs = [config for config in price_matrix_configs if config['type'] != 'mulion_mohair']
                        except Exception as e:
                            _logger.error(f"[PRICE_MATRIX] Error evaluating original matrix conditions: {str(e)}")
                            # On error, remove the matrix from pricing for safety
                            price_matrix_configs = [config for config in price_matrix_configs if config['type'] != 'mulion_mohair']
                    else:
                        # Original matrix has no special_conditions, use it for pricing
                        _logger.info(f"[PRICE_MATRIX] Original matrix '{original_matrix.name}' has no special_conditions, using it for pricing")
                        # Keep the original matrix in price_matrix_configs (no change needed)
                else:
                    _logger.warning(f"[PRICE_MATRIX] No original mulion_mohair_price_grid_id found")

        # NEW: SPECIAL CONDITION EVALUATION FOR PLUGH COMPONENTS
        # Evaluate special conditions on all price matrices with sales_price_application='plugh'
        # and select the first one that meets the conditions
        if self.plugh_price_grid_id:
            _logger.info(f"[PRICE_MATRIX] Evaluating special conditions for plugh components")
            
            # Get all price matrices with sales_price_application='plugh'
            plugh_sale_price_matrices = self.env['config.matrix.price.matrix'].search([
                ('sales_price_application', '=', 'plugh'),
                ('active', '=', True)
            ])
            
            _logger.info(f"[PRICE_MATRIX] Found {len(plugh_sale_price_matrices)} plugh sale price matrices to evaluate")
            
            # Find the first matrix that meets special conditions
            selected_plugh_matrix = None
            for matrix in plugh_sale_price_matrices:
                try:
                    # Pass door height and width field names for proper dimension extraction
                    if matrix.evaluate_special_conditions(
                        configuration_values, 
                        door_height_field_name=self.door_height_field_id.name if self.door_height_field_id else None,
                        door_width_field_name=self.door_width_field_id.name if self.door_width_field_id else None
                    ):
                        selected_plugh_matrix = matrix
                        _logger.info(f"[PRICE_MATRIX] Selected plugh matrix '{matrix.name}' (ID: {matrix.id}) based on special conditions")
                        break
                    else:
                        _logger.info(f"[PRICE_MATRIX] Plugh matrix '{matrix.name}' (ID: {matrix.id}) failed special conditions")
                except Exception as e:
                    _logger.warning(f"[PRICE_MATRIX] Error evaluating special conditions for plugh matrix '{matrix.name}': {str(e)}")
                    continue
            
            if selected_plugh_matrix:
                # Add the selected special condition matrix as a new entry in price_matrix_configs
                # Keep the original plugh_price_grid_id, add this as an additional plugh matrix
                new_plugh_config = {
                    'matrix': selected_plugh_matrix,
                    'height_field': self.door_height_field_id.name if self.door_height_field_id else None,
                    'width_field': self.door_width_field_id.name if self.door_width_field_id else None,
                    'type': 'plugh_extra'  # New type to distinguish from original plugh
                }
                price_matrix_configs.append(new_plugh_config)
                _logger.info(f"[PRICE_MATRIX] Added special condition plugh matrix: {selected_plugh_matrix.name} as new entry")
                _logger.info(f"[PRICE_MATRIX] Now processing both original plugh matrix and special condition matrix")
            else:
                # No plugh sale price matrix met special conditions, keep the original plugh_price_grid_id only
                _logger.info(f"[PRICE_MATRIX] No plugh sale price matrix met special conditions, using original plugh_price_grid_id only")


        for config in price_matrix_configs:
            price_matrix = config['matrix']
            height_field = config['height_field']
            width_field = config['width_field']
            matrix_type = config['type']
            
            _logger.info(f"[PRICE_MATRIX] Processing {matrix_type} matrix: {price_matrix.name} (ID: {price_matrix.id})")
            _logger.info(f"[PRICE_MATRIX] Using height field: {height_field}, width field: {width_field}")
            
            # Skip if required fields are not configured
            if not height_field or not width_field:
                _logger.info(f"[PRICE_MATRIX] Skipping {matrix_type} matrix - missing dimension fields")
                continue
            
            height = self._extract_dimension_value(configuration_values, [height_field], 'height')
            width = self._extract_dimension_value(configuration_values, [width_field], 'width')
            _logger.info(f"[PRICE_MATRIX] Extracted dimensions for {matrix_type} - Height: {height}, Width: {width}")
            
            if not height or not width:
                _logger.info(f"[PRICE_MATRIX] Missing dimensions for {matrix_type} - Height: {height}, Width: {width}")
                continue

            # Get price from matrix using dimensions
            price = price_matrix.get_price_for_dimensions(height, width) * panel_quantity
            _logger.info(f"[PRICE_MATRIX] Price from {matrix_type} matrix: {price}")

            # SPECIAL HANDLING FOR PLUGH SPECIAL CONDITION MATRIX: Check for fixed pricing fallback
            # Only apply fixed pricing fallback to the special condition matrix, not the original plugh matrix
            if matrix_type == 'plugh_extra' and not price:
                _logger.info(f"[PRICE_MATRIX] Grid lookup failed for plugh special condition matrix, checking fixed pricing fallback")
                
                # Get fixed pricing for this template
                fixed_pricing_result = self._get_plugh_fixed_pricing_fallback(configuration_values)
                
                if fixed_pricing_result['fixed_pricing_applied']:
                    # Use fixed pricing instead of grid pricing
                    price = fixed_pricing_result['total_price'] * panel_quantity
                    _logger.info(f"[PRICE_MATRIX] Using fixed pricing for plugh special condition matrix: {price}")
                    
                    # Add fixed pricing breakdown entries
                    for fixed_entry in fixed_pricing_result['breakdown']:
                        breakdown.append({
                            'matrix_name': f"Fixed Pricing - {fixed_entry['description']}",
                            'price': fixed_entry['price'] * panel_quantity,
                            'dimensions': {
                                'height': height,
                                'width': width
                            },
                            'matrix_type': 'plugh_extra_fixed',
                            'height_field': height_field,
                            'width_field': width_field,
                            'fixed_pricing_entry': fixed_entry['id'],
                            'pricing_source': 'fixed_pricing'
                        })
                    
                    # Use fixed pricing currency if available
                    if fixed_pricing_result['currency']:
                        currency = fixed_pricing_result['currency']
                        _logger.info(f"[PRICE_MATRIX] Using fixed pricing currency: {currency.name}")
                    
                    # Continue to next iteration since we've handled pricing
                    continue

            if price:
                total_price += price
                breakdown.append({
                    'matrix_name': price_matrix.name,
                    'price': price,
                    'dimensions': {
                        'height': height,
                        'width': width
                    },
                    'matrix_type': matrix_type,
                    'height_field': height_field,
                    'width_field': width_field,
                    'pricing_source': 'price_matrix'
                })
                _logger.info(f"[PRICE_MATRIX] Price from {matrix_type} matrix {price_matrix.name}: {price} for height={height}, width={width}")
            else:
                _logger.info(f"[PRICE_MATRIX] No price found for {matrix_type} matrix dimensions: height={height}, width={width}")

            # Use matrix currency if available
            if price_matrix.currency_id:
                currency = price_matrix.currency_id
                _logger.info(f"[PRICE_MATRIX] Using {matrix_type} matrix currency: {currency.name}")

        _logger.info(f"[PRICE_MATRIX] Final result - Total price: {total_price}, Currency: {currency.name}")
        _logger.info(f"[PRICE_MATRIX] Breakdown: {breakdown}")

        return {
            'price': total_price,
            'currency_id': currency.id,
            'breakdown': breakdown
        }

    def _extract_dimension_value(self, configuration_values, field_specific_matrices, dimension_type):
        """Extract height or width value from configuration values

        Args:
            configuration_values: Dict of field technical names to values
            dimension_type: 'height' or 'width'

        Returns:
            float: Dimension value or False if not found
        """
        for field in field_specific_matrices:
            if dimension_type.lower() in field.lower() and field in configuration_values:
                try:
                    return float(configuration_values[field])
                except (ValueError, TypeError):
                    continue
        return False

    def _get_plugh_fixed_pricing_fallback(self, configuration_values):
        """
        Get fixed pricing for plugh components when grid lookup fails
        
        Args:
            configuration_values: Dict of field technical names to values
            
        Returns:
            dict: {
                'fixed_pricing_applied': bool,
                'total_price': float,
                'breakdown': list,
                'currency': record or None
            }
        """
        try:
            # Find all fixed pricing entries for this template
            fixed_pricing_entries = self.env['config.matrix.sales.fixed.price'].search([
                ('template_id', '=', self.id),
                ('active', '=', True)
            ], order='sequence ASC')
            
            if not fixed_pricing_entries:
                return {
                    'fixed_pricing_applied': False,
                    'total_price': 0.0,
                    'breakdown': [],
                    'currency': None
                }
            
            _logger.info(f"[FIXED_PRICING] Found {len(fixed_pricing_entries)} fixed pricing entries for template {self.id}")
            
            # Check each entry in sequence order (first match takes precedence)
            total_price = 0.0
            breakdown = []
            currency = None
            domain_helper = self.env['config.matrix.calculated.field'].sudo()

            for entry in fixed_pricing_entries:
                try:
                    if entry.evaluate_conditions(configuration_values):
                        _logger.info(f"[FIXED_PRICING] Fixed pricing condition met for entry: {entry.description}")
                        
                        # Add to breakdown
                        breakdown.append({
                            'id': entry.id,
                            'description': entry.description,
                            'price': entry.value_cost,
                            'condition': entry.condition or entry.code
                        })
                        
                        # Add to total price
                        total_price += entry.value_cost
                        
                        # Use entry currency if available
                        if entry.currency_id:
                            currency = entry.currency_id
                        
                        _logger.info(f"[FIXED_PRICING] Added fixed pricing: {entry.description} = {entry.value_cost}")
                        
                except Exception as e:
                    _logger.error(f"[FIXED_PRICING] Error evaluating conditions for entry {entry.id}: {str(e)}")
                    continue
            
            if breakdown:
                _logger.info(f"[FIXED_PRICING] Total fixed pricing applied: {total_price}")
                return {
                    'fixed_pricing_applied': True,
                    'total_price': total_price,
                    'breakdown': breakdown,
                    'currency': currency
                }
            else:
                _logger.info(f"[FIXED_PRICING] No fixed pricing conditions met for template {self.id}")
                return {
                    'fixed_pricing_applied': False,
                    'total_price': 0.0,
                    'breakdown': [],
                    'currency': None
                }
                
        except Exception as e:
            _logger.error(f"[FIXED_PRICING] Error getting fixed price for template {self.id}: {str(e)}")
            return {
                'fixed_pricing_applied': False,
                'total_price': 0.0,
                'breakdown': [],
                'currency': None
            }

    def action_export_all_matrices(self):
        """Export all matrices for this template"""
        self.ensure_one()

        return {
            'name': _('Export All Matrices'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.excel.exporter',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_export_type': 'template',
                'default_template_id': self.id,
                'default_name': f'Export - {self.name}',
            },
        }

    def action_export_template_config(self):
        """Export template configuration including matrices"""
        self.ensure_one()

        return {
            'name': _('Export Template Configuration'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.excel.exporter',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_export_type': 'template',
                'default_template_id': self.id,
                'default_include_metadata': True,
                'default_name': f'Template Config - {self.name}',
            },
        }

    def action_open_matrix_editor(self):
        """Open the matrix editor for price matrices"""
        self.ensure_one()

        # This method should be defined in the matrix model
        # For now, return an action to view the matrix
        return {
            'name': _('Matrix Editor'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.price.matrix',
            'view_mode': 'list,form',
            'domain': [('product_template_id', '=', self.product_template_id.id)],
            'context': {
                'default_product_template_id': self.product_template_id.id,
            },
        }

    def action_view_visibility_conditions(self):
        """View all visibility conditions for this template"""
        self.ensure_one()

        return {
            'name': _('Visibility Conditions'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.visibility.condition',
            'view_mode': 'list,form',
            'domain': [('field_id.matrix_id', '=', self.id)],
            'context': {'default_matrix_id': self.id},
        }

    def action_view_range_conditions(self):
        """View range conditions for this template"""
        self.ensure_one()

        # Get conditions that contain range patterns
        field_conditions = self.env['config.matrix.visibility.condition'].search([
            ('field_id.matrix_id', '=', self.id),
            '|',
            ('expression', 'ilike', '?'),
            ('expression', 'ilike', 'abs(')
        ])

        return {
            'name': _('Range Conditions'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.visibility.condition',
            'view_mode': 'list,form',
            'domain': [('id', 'in', field_conditions.ids)],
            'context': {'default_matrix_id': self.id},
        }

    def action_test_visibility_conditions(self):
        """Test all visibility conditions with sample data"""
        self.ensure_one()

        return {
            'name': _('Test Visibility Conditions'),
            'type': 'ir.actions.act_window',
            'res_model': 'config.matrix.test.visibility.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_template_id': self.id,
            },
        }

    # def action_add_combination_mapping(self):
    #     """Open form to add a new combination mapping"""
    #     self.ensure_one()
    #
    #     return {
    #         'name': _('Add Combination Mapping'),
    #         'type': 'ir.actions.act_window',
    #         'res_model': 'config.matrix.combination.mapping',
    #         'view_mode': 'form',
    #         'target': 'new',
    #         'context': {
    #             'default_template_id': self.id,
    #             'hide_template': True
    #         }
    #     }

    def copy(self, default=None):
        self.ensure_one()
        default = dict(default or {})

        # Generate a new name for the template
        if not default.get('name'):
            default['name'] = _('%s (Copy)') % self.name

        # Generate a unique code for the new template
        base_code = self.code or ''
        i = 1
        new_code = f"{base_code}_copy"
        while self.search([('code', '=', new_code)]):
            i += 1
            new_code = f"{base_code}_copy{i}"
        default['code'] = new_code

        # Copy the template itself
        new_template = super(ConfigMatrixTemplate, self).copy(default)

        # Copy all sections (fields/options/component mappings are handled by their own copy methods)
        for section in self.section_ids:
            section.copy({'matrix_id': new_template.id})

        # Copy template-level component mappings (TEMPORARILY DISABLED)
        # TODO: Re-enable when component mapping relationship is fixed
        # if self.component_mapping_ids:
        #     for mapping in self.component_mapping_ids:
        #         mapping.copy({'template_id': new_template.id})

        # Copy SVG components
        if self.svg_component_ids:
            for svg in self.svg_component_ids:
                svg.copy({'template_id': new_template.id})



        # Copy matrix assignments
        if self.matrix_assignment_ids:
            for assignment in self.matrix_assignment_ids:
                assignment.copy({'template_id': new_template.id})

        # Copy base components (many2many)
        if self.bom_product_ids:
            new_template.bom_product_ids = [(6, 0, self.bom_product_ids.ids)]

        return new_template

    def test_special_condition_evaluation(self, configuration_values):
        """Test special condition evaluation for price matrices
        
        This method helps debug and test the special condition evaluation
        specifically for mulion_mohair_price_grid_id cases.
        
        Args:
            configuration_values: Dict of field technical names to values
            
        Returns:
            dict: Test results for debugging
        """
        self.ensure_one()
        
        result = {
            'template_id': self.id,
            'template_name': self.name,
            'has_mulion_mohair_price_grid': bool(self.mulion_mohair_price_grid_id),
            'mulion_mohair_price_grid_id': self.mulion_mohair_price_grid_id.id if self.mulion_mohair_price_grid_id else False,
            'available_sale_price_matrices': [],
            'evaluation_results': [],
            'selected_matrix': None
        }
        
        if not self.mulion_mohair_price_grid_id:
            result['error'] = "Template does not have mulion_mohair_price_grid_id configured"
            return result
        
        # Get all available sale price matrices
        sale_price_matrices = self.env['config.matrix.price.matrix'].search([
            ('is_sale_price_matrix', '=', True),
            ('active', '=', True)
        ])
        
        result['available_sale_price_matrices'] = [
            {
                'id': matrix.id,
                'name': matrix.name,
                'has_special_conditions': bool(matrix.special_conditions),
                'special_conditions': matrix.special_conditions
            }
            for matrix in sale_price_matrices
        ]
        
        # Test each matrix's special conditions
        for matrix in sale_price_matrices:
            try:
                test_result = matrix.test_special_conditions(configuration_values)
                result['evaluation_results'].append(test_result)
                
                # Check if this matrix meets all conditions
                if test_result['evaluation_result']:
                    result['selected_matrix'] = {
                        'id': matrix.id,
                        'name': matrix.name
                    }
                    break
                    
            except Exception as e:
                result['evaluation_results'].append({
                    'matrix_id': matrix.id,
                    'matrix_name': matrix.name,
                    'error': f"Exception during testing: {str(e)}"
                })
        
        return result

class ConfigMatrixComponentMapping(models.Model):
    _name = 'config.matrix.component.mapping'
    _description = 'Component Mapping'

    template_id = fields.Many2one('config.matrix.template', "Template", required=True, ondelete='cascade')
    component_product_id = fields.Many2one('product.product', "Component Product", required=True)
    quantity_formula = fields.Char("Quantity Formula", default="1", required=True)
    condition = fields.Char("Condition", help="Optional condition for when to include this component")

    @api.constrains('quantity_formula')
    def _check_quantity_formula(self):
        for mapping in self:
            if not mapping.quantity_formula:
                raise ValidationError(_("Quantity formula cannot be empty"))

            # Basic validation of formula
            try:
                # Compile the formula to check syntax
                compile(mapping.quantity_formula, '<string>', 'eval')
            except Exception as e:
                raise ValidationError(_("Invalid quantity formula: %s") % str(e))
