# -*- coding: utf-8 -*-

import json
import logging
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError


_logger = logging.getLogger(__name__)



class ConfigMatrixConfiguration(models.Model):
    _name = 'config.matrix.configuration'
    _inherit = ['config.matrix.formula.helper']
    _description = 'Product Configuration'
    _order = 'create_date desc'

    name = fields.Char("Configuration Name", compute='_compute_name', store=True)
    product_id = fields.Many2one('product.product', "Product", required=True)
    template_id = fields.Many2one('config.matrix.template', "Configuration Template", required=True, ondelete='cascade')
    sale_order_line_id = fields.Many2one('sale.order.line', "Sales Order Line")
    create_date = fields.Datetime("Created On", readonly=True)

    # Store configuration as JSON
    config_data = fields.Text("Configuration Data", help="JSON string with all configuration values")

    # Generated BOM
    bom_id = fields.Many2one('mrp.bom', "Generated BOM")
    price_matrix = fields.Float("Price Matrix", digits='Product Price')
    price_component = fields.Float("Price Component", digits='Product Price')
    price_operations = fields.Float("Price Operations", digits='Product Price')
    price_total = fields.Float("Total Aggregated Price", compute='_compute_price_total', store=True, digits='Product Price')
    price = fields.Float("Calculated Price", digits='Product Price')

    # Configuration state
    state = fields.Selection([
        ('draft', 'Draft'),
        ('configured', 'Configured'),
        ('applied', 'Applied')
    ], default='draft', string="Status")

    # Computed fields
    configuration_summary = fields.Text("Configuration Summary", compute="_compute_configuration_summary")
    bom_line_ids = fields.One2many('mrp.bom.line', compute='_compute_bom_line_ids', string="BOM Lines")

    # Related fields
    sale_order_id = fields.Many2one('sale.order', related='sale_order_line_id.order_id',
                                  string="Sales Order", store=True)
    customer_id = fields.Many2one('res.partner', related='sale_order_id.partner_id',
                                string="Customer", store=True)

    # Fields for tracking related configurations
    parent_config_id = fields.Many2one('config.matrix.configuration', "Parent Configuration",
                                     copy=False, ondelete="set null",
                                     help="Reference to the original configuration this was copied from")
    child_config_ids = fields.One2many('config.matrix.configuration', 'parent_config_id',
                                     string="Child Configurations",
                                     help="Configurations that were copied from this one")

    # Builder portal fields
    project_id = fields.Many2one('builder.project', "Builder Project",
                               ondelete="set null", copy=True)
    configuration_slot = fields.Integer("Configuration Slot Number", default=0)
    product_category = fields.Selection([
        ('door', 'Door'),
        ('screen', 'Screen')
    ], string="Product Category")

    @api.depends('product_id', 'create_date')
    def _compute_name(self):
        """
        Compute a descriptive name for the configuration based on the product and creation date.

        The name format is: "Product Name (Creation Date)"
        If no product is set, the name will be "New Configuration"
        """
        for config in self:
            if config.product_id:
                date_str = fields.Datetime.to_string(config.create_date or fields.Datetime.now())
                config.name = f"{config.product_id.name} ({date_str})"
            else:
                config.name = "New Configuration"

    def generate_bom(self):
        """
        Generate a Bill of Materials (BOM) from the configuration data.

        This method creates or updates a BOM based on the configuration values.
        It first creates the BOM header, then generates BOM lines by calling
        _generate_bom_lines() with the configuration values.

        Returns:
            mrp.bom: The generated BOM record, or False if no configuration data exists
        """
        self.ensure_one()

        if not self.config_data:
            return False

        try:
            # Load configuration values
            config_values = json.loads(self.config_data)

            if self.template_id:
                calc_field_model = self.env['config.matrix.calculated.field']
                calculated_results = calc_field_model.calculate_fields(config_values, self.template_id.id)
                # Add calculated fields to the context
                config_values.update(calculated_results)

            # Create BOM values
            bom_vals = {
                'product_tmpl_id': self.product_id.product_tmpl_id.id,
                'product_id': self.product_id.id,
                'code': f'CONFIG-{self.id}',
                'type': 'normal',
                'config_id': self.id,  # Ensure config_id is set
            }

            # Update existing or create new BOM
            if self.bom_id:
                bom = self.bom_id
                # Clear existing BOM lines
                bom.bom_line_ids.unlink()
                bom.write(bom_vals)
                _logger.info(f"[BYPRODUCT_DEBUG] Updated existing BOM {bom.code} with config_id {bom.config_id.id if bom.config_id else 'None'}")
            else:
                bom = self.env['mrp.bom'].create(bom_vals)
                self.bom_id = bom.id
                _logger.info(f"[BYPRODUCT_DEBUG] Created new BOM {bom.code} with config_id {bom.config_id.id if bom.config_id else 'None'}")

            # Generate BOM lines
            bom_lines = self._generate_bom_lines(bom, config_values)

            # Generate routing operations (optional - don't fail if routing not available)
            try:
                routing_operations = self._generate_routing_operations(bom, config_values)
            except Exception as e:
                _logger.warning(f"Could not generate routing operations for configuration {self.id}: {str(e)}")
                routing_operations = []

            # Handle mesh operations (create mesh cut operations and add components)
            try:
                mesh_operations = self._handle_mesh_operations(bom, config_values)
            except Exception as e:
                _logger.warning(f"Could not handle mesh operations for configuration {self.id}: {str(e)}")
                mesh_operations = []

            # Add mesh byproducts to BOM
            try:
                _logger.info(f"[BYPRODUCT_DEBUG] Calling _add_mesh_byproducts_to_bom for config {self.id}, BOM {bom.code}")
                self._add_mesh_byproducts_to_bom(bom)
                _logger.info(f"[BYPRODUCT_DEBUG] Completed byproduct integration for BOM {bom.code}")
            except Exception as e:
                _logger.error(f"[BYPRODUCT_DEBUG] ✗ Could not add mesh byproducts to BOM for configuration {self.id}: {str(e)}")

            # ADDITIONAL: Create mesh panel operations if template requires mesh
            try:
                if self.template_id.mesh_required:
                    _logger.info(f"Template {self.template_id.name} requires mesh - creating panel operations")
                    panel_result = self.create_mesh_panel_operations()
                    if panel_result.get('success', True):  # success=True or no success key means success
                        _logger.info(f"Successfully created mesh panel operations for config {self.id}")
                    else:
                        _logger.warning(f"Failed to create mesh panel operations: {panel_result.get('error', 'Unknown error')}")
            except Exception as e:
                _logger.warning(f"Could not create mesh panel operations for configuration {self.id}: {str(e)}")

            # Return action to reload the form view
            return {
                'type': 'ir.actions.act_window',
                'res_model': 'config.matrix.configuration',
                'res_id': self.id,
                'view_mode': 'form',
                'target': 'current',
            }
        except Exception as e:
            _logger.error(f"Error generating BOM for configuration {self.id}: {str(e)}")
            raise UserError(_(
                "Failed to generate Bill of Materials: %s\n\n"
                "Please check your configuration data and try again."
            ) % str(e))

    def _generate_bom_lines(self, bom, config_values):
        """
        Create BOM lines based on configuration values - Enhanced for 1:many mappings.

        This method processes the configuration values and generates BOM lines for:
        1. Base products defined in the template
        2. Components linked to fields (both legacy and new 1:many mappings)
        3. Components linked to selected options (both legacy and new 1:many mappings)
        4. Components from template-level component mappings

        Args:
            bom (mrp.bom): The BOM record to add lines to
            config_values (dict): The configuration values from config_data

        Returns:
            list: List of created BOM line records
        """
        BOMLine = self.env['mrp.bom.line']
        bom_lines = []

        # Add base products (unchanged)
        for product in self.template_id.bom_product_ids:
            bom_lines.append({
                'bom_id': bom.id,
                'product_id': product.id,
                'product_qty': 1.0,
            })
            # _logger.debug(f"Added base product {product.name} to BOM")

        # Check if we're dealing with numeric field IDs
        using_numeric_ids = all(key.isdigit() for key in config_values.keys() if key)
        _logger.info(f"Configuration data format: {'Numeric IDs' if using_numeric_ids else 'Technical names'}")

        # CRITICAL: Map field values by technical name for dynamic component matching
        # _logger.info("Mapping field values by technical name for dynamic components")
        # for section in self.template_id.section_ids:
        #     for field in section.field_ids:
        #         if field.technical_name and field.technical_name in config_values:
        #             # Field value is already mapped by technical name
        #             # _logger.debug(f"Field {field.id} ({field.name}) already mapped by technical name: {field.technical_name} = {config_values[field.technical_name]}")
        #             pass
        #         elif field.technical_name and str(field.id) in config_values:
        #             # Map field ID to technical name
        #             field_value = config_values[str(field.id)]
        #             config_values[field.technical_name] = field_value
        #             # _logger.debug(f"Mapped field {field.id} ({field.name}) -> {field.technical_name} = {field_value}")

        _logger.info(f"Final config values with technical names: {config_values}")

        # Process all sections and fields
        for section in self.template_id.section_ids:
            for field in section.field_ids:
                # Get field value based on the format of config_values
                field_value = None
                if using_numeric_ids:
                    field_value = config_values.get(str(field.id))
                    # _logger.debug(f"Looking for field ID {field.id} in config values: {'Found' if field_value else 'Not found'}")
                else:
                    field_value = config_values.get(field.technical_name)

                # Skip fields with no value
                if not field_value and field.field_type != 'boolean':
                    # _logger.debug(f"Skipping field {field.name} - no value")
                    continue

                # Process field component mappings (1:many support)
                field_mappings = field.get_component_mappings()
                for mapping in field_mappings:
                    # Check additional condition if specified
                    if mapping.get('condition') and not self._evaluate_condition(mapping['condition'], config_values):
                        continue

                    # Get the component product (static or dynamic)
                    component_product = None
                    if mapping.get('mapping_type') == 'dynamic_match' and mapping.get('mapping_obj'):
                        # Use the find_matching_product method for dynamic field matching
                        component_product = mapping['mapping_obj'].find_matching_product(config_values)
                        if not component_product:
                            _logger.warning(f"No matching product found for dynamic field mapping in field {field.name}")
                            continue
                    else:
                        # Use the static product
                        component_product = mapping['component_product_id']

                    # Calculate quantity
                    qty = self._calculate_component_quantity(
                        mapping['quantity_formula'],
                        config_values,
                        1.0,
                        field.technical_name
                    )

                    if qty > 0 and component_product:  # Only add if quantity is positive and product exists
                        bom_lines.append({
                            'bom_id': bom.id,
                            'product_id': component_product.id,
                            'product_qty': qty,
                        })
                        _logger.debug(f"Added component {component_product.name} from field {field.name} with qty {qty} ({'Legacy' if mapping.get('is_legacy') else 'New'} mapping)")

                # For selection fields, process selected option's component mappings
                if field.field_type == 'selection':
                    # Find the option that matches the field value
                    options = self.env['config.matrix.option'].search([('field_id', '=', field.id)])
                    # _logger.debug(f"Found {len(options)} options for field {field.name}")

                    # Try to find the option by value
                    option = options.filtered(lambda o: o.value == field_value)
                    if not option and options:
                        # If not found, try to find by name (sometimes the value stored is the name)
                        option = options.filtered(lambda o: o.name == field_value)

                    # If still not found, try by ID if value is numeric
                    if not option:
                        _logger.warning(f"No option found for field {field.name} with value {field_value}")
                        if str(field_value).isdigit():
                            option = self.env['config.matrix.option'].browse(int(field_value))
                            if option.exists() and option.field_id.id == field.id:
                                _logger.info(f"Found option by ID: {option.name} (ID: {option.id})")
                            else:
                                option = False

                        if not option:
                            continue

                    if len(option) > 1:
                        option = option[0]  # Take the first one if multiple matches

                    # _logger.debug(f"Found option: {option.name} (ID: {option.id})")

                    # Process option component mappings (1:many support)
                    option_mappings = option.get_component_mappings()
                    for mapping in option_mappings:
                        # Check additional condition if specified
                        if mapping.get('condition') and not self._evaluate_condition(mapping['condition'], config_values):
                            continue

                        # Get the component product (static or dynamic)
                        component_product = None
                        if mapping.get('mapping_type') == 'dynamic_match' and mapping.get('mapping_obj'):
                            # Use the find_matching_product method for dynamic field matching
                            component_product = mapping['mapping_obj'].find_matching_product(config_values)
                            if not component_product:
                                _logger.warning(f"No matching product found for dynamic field mapping in option {option.name}")
                                continue
                        else:
                            # Use the static product
                            component_product = mapping['component_product_id']

                        # Calculate quantity
                        qty = self._calculate_component_quantity(
                            mapping['quantity_formula'],
                            config_values,
                            1.0,
                            field.technical_name
                        )

                        if qty > 0 and component_product:  # Only add if quantity is positive and product exists
                            # Check if this component is already added (to avoid duplicates)
                            existing = next((bl for bl in bom_lines
                                           if bl['product_id'] == component_product.id), None)

                            if existing:
                                # Add to existing quantity
                                existing['product_qty'] += qty
                                _logger.debug(f"Updated existing component {component_product.name} qty to {existing['product_qty']}")
                            else:
                                # Add new line
                                bom_lines.append({
                                    'bom_id': bom.id,
                                    'product_id': component_product.id,
                                    'product_qty': qty,
                                })
                                _logger.debug(f"Added component {component_product.name} from option {option.name} with qty {qty} ({'Legacy' if mapping.get('is_legacy') else 'New'} mapping)")

        # Process component mappings from template (if any exist)
        if hasattr(self.template_id, 'component_mapping_ids'):
            _logger.debug(f"Processing {len(self.template_id.component_mapping_ids)} template component mappings")
            for mapping in self.template_id.component_mapping_ids:
                # Skip if no component product
                if not mapping.component_product_id:
                    _logger.debug(f"Skipping template mapping - no component product")
                    continue

                # Check condition if present
                if mapping.condition and not self._evaluate_condition(mapping.condition, config_values):
                    _logger.debug(f"Skipping template component {mapping.component_product_id.name} - condition not met: {mapping.condition}")
                    continue

                # Calculate quantity
                qty = self._calculate_component_quantity(
                    mapping.quantity_formula or '1',
                    config_values,
                    1.0,
                    None  # Template-level mappings don't have field names
                )

                if qty > 0:  # Only add if quantity is positive
                    bom_lines.append({
                        'bom_id': bom.id,
                        'product_id': mapping.component_product_id.id,
                        'product_qty': qty,
                    })
                    _logger.debug(f"Added component {mapping.component_product_id.name} from template mapping with qty {qty}")
                else:
                    _logger.debug(f"Skipping template component {mapping.component_product_id.name} - quantity is zero or negative: {qty}")

        # Create all BOM lines
        created_lines = []
        for line in bom_lines:
            created_line = BOMLine.create(line)
            created_lines.append(created_line)

        _logger.info(f"Created {len(created_lines)} BOM lines for configuration {self.id}")
        return created_lines

    def _generate_routing_operations(self, bom, config_values):
        """
        Create BOM operations based on configuration values.

        This method processes the configuration values and generates BOM operations for:
        1. Operations linked to fields (both legacy and new 1:many mappings)
        2. Operations linked to selected options (both legacy and new 1:many mappings)

        Args:
            bom (mrp.bom): The BOM record to add operations to
            config_values (dict): The configuration values from config_data

        Returns:
            list: List of created BOM operation records
        """
        # Clear existing operations on the BOM
        try:
            bom.operation_ids.unlink()
        except Exception as e:
            _logger.error(f"Error clearing existing operations for BOM {bom.code}: {str(e)}")
            return []

        operations = []
        sequence = 10

        # Check if we're dealing with numeric field IDs
        using_numeric_ids = all(key.isdigit() for key in config_values.keys() if key)

        # Process all sections and fields
        for section in self.template_id.section_ids:
            for field in section.field_ids:
                # Skip invisible fields
                if not self._is_field_visible(field, config_values):
                    continue

                # Get field value
                field_value = None
                if using_numeric_ids:
                    field_value = config_values.get(str(field.id))
                else:
                    field_value = config_values.get(field.technical_name or f"field_{field.id}")

                if field_value is None:
                    continue

                # Process field operation mappings
                operation_mappings = field.get_operation_mappings()
                _logger.info(f"Field {field.name} has {len(operation_mappings)} operation mappings")

                for operation_mapping in operation_mappings:
                    # Check condition if exists
                    if operation_mapping.get('condition') and not self._evaluate_condition(operation_mapping['condition'], config_values):
                        continue

                    # Calculate duration using operation template if available
                    try:
                        if operation_mapping.get('operation_template_id'):
                            template = operation_mapping['operation_template_id']

                            if operation_mapping.get('custom_duration_formula') and operation_mapping['custom_duration_formula'] != '60.0':
                                # Use custom formula override (but ignore hardcoded 60.0)
                                duration = self._calculate_operation_duration(
                                    operation_mapping['custom_duration_formula'],
                                    config_values,
                                    template.default_duration if template else 60.0
                                )
                            else:
                                # Use template's duration calculation
                                if template and hasattr(template, 'get_calculated_duration'):
                                    duration = template.get_calculated_duration(config_values)
                                else:
                                    duration = 60.0  # Default fallback
                        else:
                            # Fallback for legacy mappings
                            duration = self._calculate_operation_duration(
                                operation_mapping.get('duration_formula', '60.0'),
                                config_values,
                                60.0  # Default 1 hour
                            )
                    except Exception as duration_error:
                        _logger.warning(f"Error calculating operation duration: {str(duration_error)}")
                        duration = 60.0  # Safe fallback

                    # Get setup and teardown times from template if available
                    template = operation_mapping.get('operation_template_id')
                    operation_data = {
                        'name': operation_mapping['operation_name'],
                        'workcenter_id': operation_mapping['workcenter_id'].id,
                        'time_cycle': duration,
                        'time_cycle_manual': duration,
                        'sequence': sequence,
                        'worksheet_type': operation_mapping.get('worksheet_type', 'text'),
                        'worksheet': operation_mapping.get('worksheet_content', ''),
                        'note': operation_mapping.get('notes', ''),
                        # Config Matrix specific fields
                        'is_config_generated': True,
                        'config_operation_template_id': template.id if template else False,
                    }
                    operations.append(operation_data)
                    sequence += 10

                # Process option operation mappings for selected options
                if field.field_type == 'selection':
                    for option in field.option_ids:
                        if str(option.value) == str(field_value):
                            # Check if option has a quantity formula and evaluate it
                            option_quantity = 1.0  # Default quantity
                            if option.quantity_formula:
                                try:
                                    option_quantity = self._calculate_component_quantity(
                                        option.quantity_formula,
                                        config_values,
                                        1.0,
                                        field.technical_name
                                    )
                                except Exception as e:
                                    _logger.warning(f"Error evaluating option quantity formula for '{option.name}': {str(e)}")
                                    option_quantity = 1.0

                            # Skip operations if option quantity is 0 or negative
                            if option_quantity <= 0:
                                _logger.info(f"Skipping operations for option '{option.name}' - quantity is zero or negative: {option_quantity}")
                                continue

                            # Get option operation mappings
                            option_operation_mappings = option.get_operation_mappings()
                            _logger.info(f"Option '{option.name}' has {len(option_operation_mappings)} operation mappings")

                            # Debug: Check if option has legacy operation mapping
                            if hasattr(option, 'workcenter_id') and option.workcenter_id and hasattr(option, 'operation_name') and option.operation_name:
                                _logger.info(f"Option '{option.name}' has legacy operation mapping: {option.operation_name} at {option.workcenter_id.name}")

                            # Debug: Check if option has new operation mappings
                            if hasattr(option, 'operation_mapping_ids'):
                                _logger.info(f"Option '{option.name}' has {len(option.operation_mapping_ids)} new operation mappings")

                            for operation_mapping in option_operation_mappings:
                                # Check condition if exists
                                if operation_mapping.get('condition') and not self._evaluate_condition(operation_mapping['condition'], config_values):
                                    continue

                                # Calculate duration using operation template if available
                                try:
                                    if operation_mapping.get('operation_template_id'):
                                        template = operation_mapping['operation_template_id']
                                        if operation_mapping.get('custom_duration_formula') and operation_mapping['custom_duration_formula'] != '60.0':
                                            # Use custom formula override (but ignore hardcoded 60.0)
                                            duration = self._calculate_operation_duration(
                                                operation_mapping['custom_duration_formula'],
                                                config_values,
                                                template.default_duration if template else 60.0
                                            )
                                        else:
                                            # Use template's duration calculation
                                            if template and hasattr(template, 'get_calculated_duration'):
                                                duration = template.get_calculated_duration(config_values)
                                                _logger.info(f"[ROUTING_DEBUG] Option template {template.name} calculated duration: {duration} minutes")
                                            else:
                                                duration = 60.0  # Default fallback
                                                _logger.info(f"[ROUTING_DEBUG] Option using default duration: {duration} minutes")
                                    else:
                                        # Fallback for legacy mappings
                                        duration = self._calculate_operation_duration(
                                            operation_mapping.get('duration_formula', '60.0'),
                                            config_values,
                                            60.0  # Default 1 hour
                                        )
                                except Exception as duration_error:
                                    _logger.warning(f"Error calculating option operation duration: {str(duration_error)}")
                                    duration = 60.0  # Safe fallback

                                # Get setup and teardown times from template if available
                                template = operation_mapping.get('operation_template_id')
                                operation_data = {
                                    'name': operation_mapping['operation_name'],
                                    'workcenter_id': operation_mapping['workcenter_id'].id,
                                    'time_cycle': duration,
                                    'time_cycle_manual': duration,
                                    'sequence': sequence,
                                    'worksheet_type': operation_mapping.get('worksheet_type', 'text'),
                                    'worksheet': operation_mapping.get('worksheet_content', ''),
                                    'note': operation_mapping.get('notes', ''),
                                    # Config Matrix specific fields
                                    'is_config_generated': True,
                                    'config_operation_template_id': template.id if template else False,
                                }
                                operations.append(operation_data)
                                sequence += 10

        # Create all BOM operations
        created_operations = []

        if len(operations) == 0:
            return created_operations

        try:
            # Determine the correct model for BOM operations
            operation_model = None

            # Check what model is used for BOM operations
            if hasattr(bom, 'operation_ids') and bom.operation_ids:
                # Get model from existing operations
                operation_model = bom.operation_ids[0]._name
            else:
                # Try common BOM operation models
                if 'mrp.routing.workcenter' in self.env:
                    operation_model = 'mrp.routing.workcenter'
                elif 'mrp.bom.operation' in self.env:
                    operation_model = 'mrp.bom.operation'

            if operation_model:
                for operation in operations:
                    # Add bom_id to the operation data
                    operation['bom_id'] = bom.id

                    # Remove invalid fields that don't exist on mrp.routing.workcenter
                    invalid_fields = ['setup_time', 'teardown_time', 'cleanup_time']
                    for field in invalid_fields:
                        operation.pop(field, None)

                    created_operation = self.env[operation_model].create(operation)
                    created_operations.append(created_operation)

        except Exception as e:
            _logger.error(f"Error creating BOM operations: {str(e)}")
            # Don't fail the entire BOM generation if operations fail

        return created_operations

    def _handle_mesh_operations(self, bom, config_values):
        """
        Handle mesh cut operations based on calculated fields.

        This method checks if mesh operations are required and creates
        mesh cut operation records and BOM components accordingly.

        Args:
            bom (mrp.bom): The BOM record to add components to
            config_values (dict): The configuration values from config_data

        Returns:
            list: List of created mesh operation results
        """
        mesh_results = []

        # Check if mesh operation is required
        mesh_operation_required = config_values.get('_CALCULATED_mesh_operation_required', False)
        if not mesh_operation_required:
            _logger.debug("Mesh operation not required for this configuration")
            return mesh_results

        # Add configuration context to config_values
        config_values['config_id'] = self.id
        if self.sale_order_line_id:
            config_values['sale_order_id'] = self.sale_order_line_id.order_id.id

        # Find mesh operation templates
        mesh_templates = self.env['config.matrix.operation.template'].search([
            ('is_mesh_operation', '=', True),
            ('active', '=', True)
        ])

        if not mesh_templates:
            _logger.warning("No mesh operation templates found")
            return mesh_results

        # Determine which template to use based on complexity
        mesh_complexity = config_values.get('_CALCULATED_mesh_complexity', 'simple')
        mesh_operation_type = config_values.get('_CALCULATED_mesh_operation_type', 'standard')

        # Select appropriate template
        selected_template = None
        if mesh_operation_type == 'precision' or mesh_complexity == 'complex':
            # Look for precision template first
            selected_template = mesh_templates.filtered(
                lambda t: t.mesh_operation_type == 'precision'
            )[:1]

        if not selected_template:
            # Fall back to standard or auto template
            selected_template = mesh_templates.filtered(
                lambda t: t.mesh_operation_type in ('standard', 'auto')
            )[:1]

        if not selected_template:
            # Use first available template
            selected_template = mesh_templates[:1]

        if selected_template:
            _logger.info(f"Using mesh operation template: {selected_template.name}")

            # Execute mesh operation
            try:
                result = selected_template.execute_mesh_operation(config_values, bom)
                if result.get('success'):
                    mesh_results.append(result)
                    _logger.info(f"Successfully created mesh operation: {result.get('mesh_operation_name')}")
                else:
                    _logger.error(f"Failed to create mesh operation: {result.get('error')}")
            except Exception as e:
                _logger.error(f"Error executing mesh operation template: {str(e)}")

        return mesh_results

    def _calculate_mesh_panel_dimensions(self, config_values):
        """Calculate dimensions for each mesh panel based on configuration"""
        # Check template mesh requirement
        if not self.template_id.mesh_required:
            return []

        # Check dynamic mesh requirement (calculated field)
        mesh_required = config_values.get('_CALCULATED_mesh_required', True)
        if not mesh_required:
            _logger.info("[MESH-DEBUG] Dynamic mesh requirement is False - no panels needed")
            return []

        method = getattr(self.template_id, 'mesh_panel_calculation_method', 'midrail_split')

        if method == 'midrail_split':
            return self._calculate_midrail_split_panels(config_values)
        elif method == 'custom_formula':
            return self._calculate_custom_formula_panels(config_values)
        else:
            return self._calculate_simple_count_panels(config_values)

    def _calculate_midrail_split_panels(self, config_values):
        """Calculate panel dimensions based on template panel quantity and midrail configuration"""
        panels = []

        # Get door dimensions
        door_width_field = self.template_id.door_width_field_id.name or '_CALCULATED_largest_door_width'
        door_height_field = self.template_id.door_height_field_id.name or '_CALCULATED_largest_door_height'

        door_width = config_values.get(door_width_field, 0)
        door_height = config_values.get(door_height_field, 0)

        if not door_width or not door_height:
            _logger.warning(f"Missing door dimensions: width={door_width}, height={door_height}")
            return panels

        # Get panel quantity from template
        panel_quantity = int(self.template_id.panel_quantity or '1')

        # Check for midrail
        has_midrail = self._has_midrail(config_values)

        if has_midrail:
            # Get midrail position
            midrail_position = self._get_midrail_position(config_values, door_height)

            # Calculate panel heights
            top_height = midrail_position
            bottom_height = door_height - midrail_position

            # With midrail, each door gets split into 2 panels
            # So total panels = panel_quantity (doors) × 2 (top/bottom per door)
            total_panels_with_midrail = panel_quantity * 2

            _logger.info(f"[MIDRAIL-PANELS] Template quantity: {panel_quantity}, With midrail: {total_panels_with_midrail} panels")

            # Create panels for each door, split at midrail
            for door_num in range(panel_quantity):
                door_label = f"Door {door_num + 1}" if panel_quantity > 1 else "Door"

                # Top panel
                panels.append({
                    'width': door_width,
                    'height': top_height,
                    'door_reference': door_label,
                    'panel_position': 'top',
                    'name': f'{door_label} Top Panel ({door_width}x{int(top_height)}mm)'
                })

                # Bottom panel
                panels.append({
                    'width': door_width,
                    'height': bottom_height,
                    'door_reference': door_label,
                    'panel_position': 'bottom',
                    'name': f'{door_label} Bottom Panel ({door_width}x{int(bottom_height)}mm)'
                })
        else:
            # No midrail - create panels based on template quantity
            if panel_quantity == 1:
                panels.append({
                    'width': door_width,
                    'height': door_height,
                    'door_reference': 'Door',
                    'panel_position': 'full',
                    'name': f'Door Full Panel ({door_width}x{door_height}mm)'
                })
            elif panel_quantity == 2:
                # Two doors, no midrail
                for door_num in range(2):
                    door_label = f"Door {door_num + 1}"
                    panels.append({
                        'width': door_width,
                        'height': door_height,
                        'door_reference': door_label,
                        'panel_position': 'full',
                        'name': f'{door_label} Full Panel ({door_width}x{door_height}mm)'
                    })
            else:
                # Multiple panels
                doors_count = min(panel_quantity, 4)  # Max 4 doors
                panels_per_door = panel_quantity // doors_count
                extra_panels = panel_quantity % doors_count

                panel_index = 0
                for door_num in range(doors_count):
                    door_label = f"Door {door_num + 1}" if doors_count > 1 else "Door"
                    panels_for_this_door = panels_per_door + (1 if door_num < extra_panels else 0)

                    for panel_in_door in range(panels_for_this_door):
                        panel_index += 1
                        panels.append({
                            'width': door_width,
                            'height': door_height,
                            'door_reference': door_label,
                            'panel_position': f'panel_{panel_in_door + 1}',
                            'name': f'({door_width}x{door_height}mm)'
                        })

        # Add panel numbering (1 of X, 2 of X, etc.)
        total_panels = len(panels)
        for i, panel in enumerate(panels, 1):
            panel['panel_number'] = i
            panel['total_panels'] = total_panels
            # Update name to include panel numbering
            panel['name'] = f"Panel {i} of {total_panels}: {panel['name']}"

        return panels



    def _has_midrail(self, config_values):
        """Check if configuration has a midrail based on conditional cases"""
        _logger.info(f"[MIDRAIL-DEBUG] Checking midrail detection...")

        # Check explicit midrail selection
        if config_values.get('midrail_selected', False):
            _logger.info(f"[MIDRAIL-DEBUG] ✅ Has midrail: midrail_selected = True")
            return True

        if config_values.get('has_midrail', False):
            _logger.info(f"[MIDRAIL-DEBUG] ✅ Has midrail: has_midrail = True")
            return True

        # Check midrail position
        midrail_position = config_values.get('midrail_position', 0)
        if midrail_position > 0:
            _logger.info(f"[MIDRAIL-DEBUG] ✅ Has midrail: midrail_position = {midrail_position}")
            return True

        # Check template-specific calculated midrail height field
        if hasattr(self.template_id, 'midrail_height_calculated_id') and self.template_id.midrail_height_calculated_id:
            midrail_field = self.template_id.midrail_height_calculated_id.name
            if midrail_field:
                midrail_value = config_values.get(midrail_field, 0)
                if midrail_value and midrail_value > 0:
                    _logger.info(f"[MIDRAIL-DEBUG] ✅ Has midrail: {midrail_field} = {midrail_value}")
                    return True
                else:
                    _logger.info(f"[MIDRAIL-DEBUG] Template midrail field {midrail_field} = {midrail_value}")

        # Check for midrail conditional cases using calculated fields
        # Conditional Case 1: (height > 1310 && width > 1310) OR height > 2610
        midrail_case1 = config_values.get('_CALCULATED_midrail_case1', False)
        if midrail_case1:
            _logger.info(f"[MIDRAIL-DEBUG] ✅ Has midrail: _CALCULATED_midrail_case1 = {midrail_case1}")
            return True

        # Check our universal midrail height field
        midrail_height = config_values.get('_CALCULATED_midrail_height', 0)
        if midrail_height and midrail_height > 0:
            _logger.info(f"[MIDRAIL-DEBUG] ✅ Has midrail: _CALCULATED_midrail_height = {midrail_height}")
            return True

        # Check for even split indicator (legacy support)
        if config_values.get('_CALCULATED_is_even_split', False):
            _logger.info(f"[MIDRAIL-DEBUG] ✅ Has midrail: _CALCULATED_is_even_split = True")
            return True

        _logger.info(f"[MIDRAIL-DEBUG] ❌ No midrail detected")
        return False

    def _get_midrail_position(self, config_values, door_height):
        """Get the midrail position in mm from bottom of door"""
        _logger.info(f"[MIDRAIL-DEBUG] Getting midrail position for door height {door_height}")

        # Check explicit position first
        midrail_position = config_values.get('midrail_position', 0)
        if midrail_position > 0:
            _logger.info(f"[MIDRAIL-DEBUG] Using explicit midrail_position: {midrail_position}")
            return midrail_position

        # Check our universal calculated midrail height field FIRST
        midrail_height = config_values.get('_CALCULATED_midrail_height', 0)
        if midrail_height and midrail_height > 0:
            _logger.info(f"[MIDRAIL-DEBUG] ✅ Using _CALCULATED_midrail_height: {midrail_height}")
            return midrail_height

        # Check template-specific calculated midrail height field
        if hasattr(self.template_id, 'midrail_height_calculated_id') and self.template_id.midrail_height_calculated_id:
            midrail_field = self.template_id.midrail_height_calculated_id.name
            if midrail_field:
                template_midrail_height = config_values.get(midrail_field, 0)
                if template_midrail_height > 0:
                    _logger.info(f"[MIDRAIL-DEBUG] Using template field {midrail_field}: {template_midrail_height}")
                    return template_midrail_height

        # Check for even split
        if config_values.get('_CALCULATED_is_even_split', False):
            position = door_height / 2
            _logger.info(f"[MIDRAIL-DEBUG] Using even split: {position}")
            return position

        # Default to middle if midrail detected but no position specified
        position = door_height / 2
        _logger.info(f"[MIDRAIL-DEBUG] Using default middle position: {position}")
        return position

    def _calculate_simple_count_panels(self, config_values):
        """Calculate panels using template panel quantity"""
        panels = []

        # Get door dimensions
        door_width_field = self.template_id.door_width_field_id.name or '_CALCULATED_largest_door_width'
        door_height_field = self.template_id.door_height_field_id.name or '_CALCULATED_largest_door_height'

        door_width = config_values.get(door_width_field, 0)
        door_height = config_values.get(door_height_field, 0)

        if not door_width or not door_height:
            return panels

        # Get panel count from template
        panel_count = int(self.template_id.panel_quantity or '1')

        # Create identical panels
        for i in range(panel_count):
            panels.append({
                'width': door_width,
                'height': door_height,
                'door_reference': f'Door {((i // 2) + 1)}' if panel_count > 2 else 'Door',
                'panel_position': f'panel_{i+1}',
                'name': f'({door_width}x{door_height}mm)'
            })

        # Add panel numbering
        total_panels = len(panels)
        for i, panel in enumerate(panels, 1):
            panel['panel_number'] = i
            panel['total_panels'] = total_panels
            panel['name'] = f"Panel {i} of {total_panels}: {panel['name']}"

        return panels

    def _calculate_custom_formula_panels(self, config_values):
        """Calculate panels using custom formula method"""
        panels = []

        # Check if template has custom formula
        if not hasattr(self.template_id, 'mesh_panel_custom_formula'):
            return self._calculate_simple_count_panels(config_values)

        try:
            formula = self.template_id.mesh_panel_custom_formula
            if formula:
                # Execute custom formula with configuration context using helper
                custom_functions = {
                    'template': self.template_id,
                    'config': self,
                }
                panels = self.evaluate_formula_with_custom_context(formula, config_values, custom_functions, default_value=[])

                # Ensure panels is a list of dictionaries
                if not isinstance(panels, list):
                    panels = []
        except Exception as e:
            _logger.error(f"Error executing custom panel formula: {str(e)}")
            # Fall back to simple count method
            panels = self._calculate_simple_count_panels(config_values)

        return panels

    def action_test_panel_calculation(self):
        """Test panel calculation and show results"""
        self.ensure_one()

        if not self.template_id.mesh_required:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Panel Calculation Test',
                    'message': 'Mesh is not required for this template.',
                    'type': 'warning'
                }
            }

        # Get configuration values
        config_values = json.loads(self.config_data or '{}')

        # Calculate panels
        panels = self._calculate_mesh_panel_dimensions(config_values)

        if not panels:
            message = 'No panels calculated. Check door dimensions and configuration.'
        else:
            message = f'Calculated {len(panels)} panels:\n\n'
            for panel in panels:
                message += f"{panel['panel_number']} of {panel['total_panels']}: {panel['name']}\n"
                message += f"   Size: {panel['width']}mm x {panel['height']}mm\n"
                message += f"   Door: {panel['door_reference']}, Position: {panel['panel_position']}\n\n"

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Panel Calculation Results',
                'message': message,
                'type': 'success' if panels else 'warning',
                'sticky': True
            }
        }

    def action_create_mesh_operations(self):
        """Create mesh cut operations for each calculated panel"""
        self.ensure_one()

        # Get configuration values first
        config_values = json.loads(self.config_data or '{}')

        # Check template mesh requirement
        if not self.template_id.mesh_required:
            raise UserError(_("Mesh operations are not required for this template."))

        # Check dynamic mesh requirement (calculated field)
        mesh_required = config_values.get('_CALCULATED_mesh_required', True)
        if not mesh_required:
            raise UserError(_("No mesh required for this door configuration (dimensions too small)."))

        # Calculate panels
        panels = self._calculate_mesh_panel_dimensions(config_values)

        if not panels:
            raise UserError(_("No panels calculated. Check door dimensions and configuration."))

        # Create mesh cut operations for each panel
        operations_created = 0
        for panel in panels:
            try:
                operation_data = {
                    'name': panel['name'],
                    'required_width': panel['width'],
                    'required_height': panel['height'],
                    'panel_number': panel['panel_number'],
                    'total_panels': panel['total_panels'],
                    'panel_position': panel['panel_position'],
                    'door_reference': panel['door_reference'],
                    'configuration_id': self.id,
                    'sale_order_line_id': self.sale_order_line_id.id if self.sale_order_line_id else False,
                    'mesh_series': config_values.get('_CALCULATED_mesh_series') or self.template_id.mesh_series,
                    'state': 'draft'
                }

                # Create the mesh cut operation
                operation = self.env['mesh.cut.operation'].create(operation_data)
                operations_created += 1
                _logger.info(f"Created mesh operation: {operation.name}")

            except Exception as e:
                _logger.error(f"Error creating mesh operation for panel {panel['name']}: {str(e)}")

        if operations_created > 0:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Mesh Operations Created',
                    'message': f'Successfully created {operations_created} mesh cut operations.',
                    'type': 'success'
                }
            }
        else:
            raise UserError(_("Failed to create any mesh operations."))

    def get_mesh_panel_requirements(self):
        """Get mesh panel requirements for Product Configurator"""
        self.ensure_one()

        # Get configuration values first
        config_values = json.loads(self.config_data or '{}')

        # Check template mesh requirement
        if not self.template_id.mesh_required:
            return {'required': False, 'panels': []}

        # Check dynamic mesh requirement (calculated field)
        mesh_required = config_values.get('_CALCULATED_mesh_required', True)
        if not mesh_required:
            return {'required': False, 'panels': [], 'reason': 'Door dimensions too small'}

        # Calculate panels
        panels = self._calculate_mesh_panel_dimensions(config_values)

        if not panels:
            return {'required': False, 'panels': []}

        # Get mesh series
        mesh_series = config_values.get('_CALCULATED_mesh_series', self.template_id.mesh_series)

        return {
            'required': True,
            'total_panels': len(panels),
            'mesh_series': mesh_series,
            'panels': panels
        }

    def create_mesh_panel_operations(self):
        """Create mesh cut operations for each panel from Product Configurator"""
        self.ensure_one()

        if not self.template_id.mesh_required:
            return {'success': False, 'error': 'This configuration does not require mesh.'}

        # Get configuration values
        config_values = json.loads(self.config_data or '{}')

        # Calculate panels
        panels = self._calculate_mesh_panel_dimensions(config_values)

        if not panels:
            return {'success': False, 'error': 'No panels calculated. Check door dimensions and configuration.'}

        # Delete existing operations for this configuration
        existing_operations = self.env['mesh.cut.operation'].search([
            ('config_id', '=', self.id)
        ])
        if existing_operations:
            _logger.info(f"Deleting {len(existing_operations)} existing mesh operations for config {self.id}")
            existing_operations.unlink()

        # Create mesh cut operations for each panel
        operations_created = []
        calculated_mesh_series = config_values.get('_CALCULATED_mesh_series')
        template_mesh_series = self.template_id.mesh_series

        # Use calculated value if it exists and is not None/empty, otherwise use template default
        if calculated_mesh_series:
            mesh_series = calculated_mesh_series
        else:
            mesh_series = template_mesh_series

        _logger.info(f"[MESH_DEBUG] Creating mesh operations with mesh_series: {mesh_series}")
        _logger.info(f"[MESH_DEBUG] _CALCULATED_mesh_series from config: {calculated_mesh_series}")
        _logger.info(f"[MESH_DEBUG] template_id.mesh_series fallback: {template_mesh_series}")
        _logger.info(f"[MESH_DEBUG] Final mesh_series used: {mesh_series}")
        _logger.info(f"[MESH_DEBUG] Available calculated fields in config: {[k for k in config_values.keys() if k.startswith('_CALCULATED')]}")

        for panel in panels:
            try:
                operation_data = {
                    'name': panel['name'],
                    'required_width': panel['width'],
                    'required_height': panel['height'],
                    'panel_number': panel['panel_number'],
                    'total_panels': panel['total_panels'],
                    'panel_position': panel['panel_position'],
                    'door_reference': panel['door_reference'],
                    'config_id': self.id,
                    'sale_order_line_id': self.sale_order_line_id.id if self.sale_order_line_id else False,
                    'mesh_series': mesh_series,
                    'state': 'review'  # Set to review state when created from Product Configurator
                }

                # Create the mesh cut operation
                operation = self.env['mesh.cut.operation'].create(operation_data)

                # Auto-hunt for mesh using individual panel hunting
                if self.template_id.hunt_each_panel_separately:
                    try:
                        # Use individual panel hunting with priority order
                        hunt_result = operation.hunt_individual_panel(
                            width=panel['width'],
                            height=panel['height'],
                            series=mesh_series,
                            priority_order='unplanned_planned_master'
                        )
                        if hunt_result:
                            # Add mesh to BOM after successful hunt
                            operation._add_mesh_to_bom()
                            _logger.info(f"Successfully hunted mesh for panel {panel['panel_number']}: {hunt_result.get('details')} - Added to BOM")
                    except Exception as e:
                        _logger.warning(f"Could not hunt mesh for panel {panel['panel_number']}: {str(e)}")
                else:
                    # Fallback to standard mesh finding
                    try:
                        operation.action_find_mesh()
                        # Add mesh to BOM after successful find
                        operation._add_mesh_to_bom()
                    except Exception as e:
                        _logger.warning(f"Could not auto-find mesh for panel {panel['panel_number']}: {str(e)}")

                operations_created.append({
                    'id': operation.id,
                    'name': operation.name,
                    'panel_number': operation.panel_number,
                    'state': operation.state,
                    'mesh_found': bool(operation.source_product_id)
                })

            except Exception as e:
                _logger.error(f"Error creating mesh operation for panel {panel['name']}: {str(e)}")
                return {'success': False, 'error': f"Error creating operation: {str(e)}"}

        return {
            'success': True,
            'operations_created': len(operations_created),
            'operations': operations_created
        }

    def action_clear_mesh_operations(self):
        """Clear all mesh operations for this configuration"""
        self.ensure_one()

        existing_operations = self.env['mesh.cut.operation'].search([
            ('config_id', '=', self.id)
        ])

        if existing_operations:
            count = len(existing_operations)
            existing_operations.unlink()
            _logger.info(f"Cleared {count} mesh operations for config {self.id}")
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Mesh Operations Cleared',
                    'message': f'Successfully cleared {count} mesh operations.',
                    'type': 'success'
                }
            }
        else:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'No Operations Found',
                    'message': 'No mesh operations found to clear.',
                    'type': 'info'
                }
            }

    def action_hunt_all_panels(self):
        """Hunt mesh for all panels in priority order (for Sales Order Manage Mesh)"""
        self.ensure_one()

        if not self.template_id.mesh_required:
            raise UserError(_("This configuration does not require mesh."))

        # Get all mesh operations for this configuration
        operations = self.env['mesh.cut.operation'].search([
            ('config_id', '=', self.id)
        ], order='panel_number')

        if not operations:
            raise UserError(_("No mesh operations found. Please create mesh operations first."))

        hunt_results = []
        successful_hunts = 0
        failed_hunts = 0

        _logger.info(f"[HUNT-ALL] Starting hunt for {len(operations)} panels in configuration {self.name}")

        # Hunt each panel individually in sequence
        for operation in operations:
            _logger.info(f"[HUNT-ALL] Hunting panel {operation.panel_number} of {operation.total_panels}")

            try:
                # Hunt with priority order: unplanned → planned → master
                hunt_result = operation.hunt_individual_panel(
                    priority_order='unplanned_planned_master'
                )

                if hunt_result and hunt_result.get('product'):
                    successful_hunts += 1
                    hunt_results.append({
                        'panel_number': operation.panel_number,
                        'status': 'success',
                        'details': hunt_result.get('details', 'Mesh found'),
                        'source_type': hunt_result.get('source_type', 'unknown'),
                        'efficiency': hunt_result.get('efficiency', 0)
                    })
                    # Add mesh to BOM after successful hunt
                    operation._add_mesh_to_bom()
                    _logger.info(f"[HUNT-ALL] ✓ Panel {operation.panel_number}: {hunt_result.get('details')} - Added to BOM")
                else:
                    failed_hunts += 1
                    hunt_results.append({
                        'panel_number': operation.panel_number,
                        'status': 'failed',
                        'details': 'No suitable mesh found',
                        'source_type': 'not_found',
                        'efficiency': 0
                    })
                    _logger.warning(f"[HUNT-ALL] ✗ Panel {operation.panel_number}: No mesh found")

            except Exception as e:
                failed_hunts += 1
                error_msg = f"Hunt error: {str(e)}"
                hunt_results.append({
                    'panel_number': operation.panel_number,
                    'status': 'error',
                    'details': error_msg,
                    'source_type': 'error',
                    'efficiency': 0
                })
                _logger.error(f"[HUNT-ALL] ✗ Panel {operation.panel_number}: {error_msg}")

        # Create summary message
        if successful_hunts == len(operations):
            message_type = 'success'
            title = 'All Panels Hunted Successfully'
            message = f'Successfully found mesh for all {successful_hunts} panels.'
        elif successful_hunts > 0:
            message_type = 'warning'
            title = 'Partial Hunt Success'
            message = f'Found mesh for {successful_hunts} of {len(operations)} panels. {failed_hunts} panels need attention.'
        else:
            message_type = 'danger'
            title = 'Hunt Failed'
            message = f'No mesh found for any of the {len(operations)} panels. Check inventory.'

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _(title),
                'message': message,
                'type': message_type,
            }
        }

    def _calculate_operation_duration(self, formula, config_values, default=60.0):
        """Calculate operation duration using formula"""
        if not formula:
            return default

        try:
            # Use the formula helper for duration calculation
            result = self.evaluate_formula(formula, config_values, default_value=default)
            return float(result)
        except Exception as e:
            _logger.error(f"Error evaluating duration formula '{formula}': {str(e)}")
            return default

    def _evaluate_condition(self, condition, config_values, default_on_error=False):
        """Evaluate an additional condition for component mapping with intelligent missing field handling"""
        if not condition:
            return True

        try:
            # Use the formula helper for condition evaluation
            return self.evaluate_condition(condition, config_values, default_result=default_on_error)
        except Exception as e:
            _logger.error(f"Error evaluating component condition '{condition}': {str(e)}")
            return default_on_error  # Default to include if evaluation fails

    def _get_quantity_multiplier(self, config_values):
        """Get the single quantity multiplier from config_values if it exists"""
        # Look for any field that ends with '_quantity' in the config
        for key, value in config_values.items():
            if key.endswith('_quantity'):
                try:
                    multiplier = float(value)
                    if multiplier > 0:
                        return multiplier
                except (ValueError, TypeError) as e:
                    _logger.warning(f"Error parsing quantity multiplier {key}={value}: {e}")
                    pass
        
        return 1.0

    def _get_quantity_multiplier_from_config(self):
        """Get the global quantity multiplier from stored configuration data"""
        if not self.config_data:
            return 1.0
        
        try:
            config_values = json.loads(self.config_data)
            return self._get_quantity_multiplier(config_values)
        except Exception as e:
            _logger.warning(f"Error parsing config_data for quantity multiplier: {str(e)}")
            return 1.0

    def _calculate_component_quantity(self, formula, config_values, default=1.0, field_name=None):
        """Calculate component quantity using formula with support for single _quantity multiplier"""
        if not formula:
            return default

        try:
            # Get the quantity multiplier first
            multiplier = self._get_quantity_multiplier(config_values)
            qty = self.calculate_component_quantity(formula, config_values, default)

            base_qty = float(qty)
            
            # CRITICAL CHANGE: When saving configuration, use base quantity 1 instead of applying multiplier
            # The multiplier will be applied to product_uom_qty in the sale order line instead
            # This ensures BOM, operations, and pricing use base quantities
            
            # Check if we're in a configuration saving context
            # We can detect this by checking if we have a sale_order_line_id (indicating we're saving a configuration)
            is_saving_configuration = bool(self.sale_order_line_id)
            
            if is_saving_configuration:
                # When saving configuration, use base quantity without multiplier
                _logger.info(f"[BOM_QUANTITY] Saving configuration - using base quantity {base_qty} (multiplier {multiplier} will be applied to product_uom_qty)")
                return base_qty
            else:
                # For other contexts (preview, etc.), apply multiplier as before
                if multiplier != 1.0:
                    base_qty *= multiplier
                    _logger.info(f"[BOM_QUANTITY] Preview context - applied multiplier {multiplier}, final quantity: {base_qty}")
                return base_qty
            
        except Exception as e:
            _logger.error(f"Error evaluating quantity formula '{formula}': {str(e)}")
            return default

    def _is_field_visible(self, field, config_values):
        """
        Check if a field is visible based on configuration values.

        This method evaluates the visibility condition of a field using the
        current configuration values. If the field has no visibility condition,
        it is always visible.

        Args:
            field (config.matrix.field): The field to check visibility for
            config_values (dict): The configuration values from config_data

        Returns:
            bool: True if the field should be visible, False otherwise
        """
        if not field.visibility_condition:
            return True

        # For visibility conditions, we'll just return True since we're only concerned with
        # including components in the BOM, not with field visibility in the UI
        return True

    def calculate_price(self, include_components=True):
        """
        Calculate the total price based on the product and its components with quantity multiplier.

        CRITICAL FIX: This method now properly handles price matrix vs BOM pricing:
        - If price_matrix is already set (from matrix lookup), use it as the full price
        - Otherwise, calculate from base price + components + operations
        - Apply quantity multiplier to all price components

        Args:
            include_components (bool): Whether to include component prices in calculation.
                                     Set to False when components are already included in the base price.

        Returns:
            float: The calculated price
        """
        self.ensure_one()

        try:
            # Get quantity multiplier from configuration data
            quantity_multiplier = self._get_quantity_multiplier_from_config()
            _logger.info(f"[CALCULATE_PRICE] Found quantity multiplier: {quantity_multiplier}")

            # CRITICAL CHANGE: When saving configuration, use base quantity 1 instead of applying multiplier
            # The multiplier will be applied to product_uom_qty in the sale order line instead
            is_saving_configuration = self.env.context.get('save_config', False)
            
            if is_saving_configuration and quantity_multiplier:
                # When saving configuration, use base price without multiplier
                sale_price_total = self.price_matrix/quantity_multiplier
                _logger.info(f"[CALCULATE_PRICE] Saving configuration - using base price {sale_price_total} (multiplier {quantity_multiplier} will be applied to product_uom_qty)")
            else:
                # For other contexts (preview, etc.), apply multiplier as before
                sale_price_total = (self.price_matrix or 0.0)
                _logger.info(f"[CALCULATE_PRICE] Preview context - price matrix with multiplier: {self.price_matrix or 0.0} * {quantity_multiplier} = {sale_price_total}")

            # UPDATED APPROACH: Calculate components + operations + use existing price_matrix
            component_total = 0
            operation_total = 0

            # Calculate component prices if BOM exists and components should be included
            if self.bom_id and include_components:
                for line in self.bom_id.bom_line_ids:
                    line_price = line.product_id.list_price * line.product_qty
                    component_total += line_price
                _logger.info(f"[CALCULATE_PRICE] Component total (BOM lines already include multiplier): {component_total}")

            # Calculate operation prices if routing exists
            if self.bom_id:
                operation_total = self._calculate_operation_prices()
                _logger.info(f"[CALCULATE_PRICE] Operation total (already includes multiplier): {operation_total}")

            total_price = component_total + operation_total + sale_price_total

            _logger.info(f"[CALCULATE_PRICE] Configuration {self.id} price calculation:")
            _logger.info(f"[CALCULATE_PRICE] - Components: ${component_total}")
            _logger.info(f"[CALCULATE_PRICE] - Operations: ${operation_total}")
            _logger.info(f"[CALCULATE_PRICE] - Sale Price Matrices (with multiplier): ${sale_price_total}")
            _logger.info(f"[CALCULATE_PRICE] - Total: ${total_price}")
            _logger.info(f"[CALCULATE_PRICE] - Quantity Multiplier: {quantity_multiplier}")
            _logger.info(f"[CALCULATE_PRICE] - BOM ID: {self.bom_id.id if self.bom_id else 'None'}")
            _logger.info(f"[CALCULATE_PRICE] - BOM lines count: {len(self.bom_id.bom_line_ids) if self.bom_id else 0}")

            # Update price fields (keep existing price_matrix, just update others)
            self.write({
                'price_component': component_total,
                'price_operations': operation_total,
                'price': total_price,
            })

            return total_price
        except Exception as e:
            _logger.error(f"Error calculating price for configuration {self.id}: {str(e)}")
            # Return current price or base product price as fallback
            return self.price or self.product_id.list_price

    def _calculate_operation_prices(self):
        """
        Calculate the total price for all operations using field/option mapping (same as UI)

        Returns:
            float: Total operation price
        """
        self.ensure_one()

        _logger.info(f"[OPERATION_COSTS_BUG] ===== MODEL OPERATION COST CALCULATION =====")
        _logger.info(f"[OPERATION_COSTS_BUG] Configuration ID: {self.id}")
        _logger.info(f"[OPERATION_COSTS_BUG] Template ID: {self.template_id.id}")

        config_values = {}

        # Load configuration values
        if self.config_data:
            try:
                config_values = json.loads(self.config_data)
                _logger.info(f"[OPERATION_COSTS_BUG] Loaded {len(config_values)} config values")

                # Log key field values for debugging
                hinge_pickup = config_values.get('bx_dbl_hinge_num_sec_hinges_pickup', 'NOT_SET')
                hinge_deliver = config_values.get('bx_dbl_hinge_num_sec_hinges_deliver', 'NOT_SET')
                _logger.info(f"[OPERATION_COSTS_BUG] MODEL: Config data field values - hinge_pickup: {hinge_pickup}, hinge_deliver: {hinge_deliver}")
            except Exception as e:
                _logger.warning(f"[OPERATION_COSTS_BUG] Could not parse config_data: {str(e)}")

        # Get quantity multiplier
        quantity_multiplier = self._get_quantity_multiplier(config_values)
        _logger.info(f"[OPERATION_COSTS_BUG] Found quantity multiplier: {quantity_multiplier}")

        # Use the same field/option mapping approach as the UI
        try:
            operation_result = self._calculate_operation_costs_field_option_mapping(config_values, quantity_multiplier)

            if operation_result.get('success'):
                total_cost = operation_result.get('total_cost', 0.0)
                operation_count = len(operation_result.get('operations', []))
                _logger.info(f"[OPERATION_COSTS_BUG] ✓ Model operation cost: ${total_cost} from {operation_count} operations")
                return total_cost
            else:
                _logger.warning(f"[OPERATION_COSTS_BUG] ✗ Model operation calculation failed: {operation_result.get('error')}")
                return 0.0

        except Exception as e:
            _logger.error(f"[OPERATION_COSTS_BUG] Error in model operation calculation: {e}")
            return 0.0

    def _calculate_operation_costs_field_option_mapping(self, field_values, quantity_multiplier):
        """
        Calculate operation costs using field/option mapping approach (same as UI)

        Args:
            field_values (dict): Field values for calculation
            quantity_multiplier (float): Quantity multiplier

        Returns:
            dict: Operation costs response
        """
        try:
            _logger.info(f"[OPERATION_COSTS_BUG] ===== MODEL FIELD/OPTION MAPPING CALCULATION =====")

            # Log key field values for debugging
            hinge_pickup = field_values.get('bx_dbl_hinge_num_sec_hinges_pickup', 'NOT_SET')
            hinge_deliver = field_values.get('bx_dbl_hinge_num_sec_hinges_deliver', 'NOT_SET')
            _logger.info(f"[OPERATION_COSTS_BUG] MODEL: Key field values - hinge_pickup: {hinge_pickup}, hinge_deliver: {hinge_deliver}")

            operations = []
            total_cost = 0.0
            template = self.template_id

            # Get field operation mappings through sections and fields
            for section in template.section_ids:
                for field in section.field_ids:
                    if not self._evaluate_visibility_condition(field.visibility_condition, field_values):
                        continue

                    # Check for legacy single operation mapping first
                    if field.workcenter_id and field.operation_name:
                        try:
                            # Calculate duration using legacy formula
                            duration_value = 0.0
                            if field.duration_formula:
                                duration_value = self._evaluate_formula(field.duration_formula, field_values, template)
                            else:
                                duration_value = 60.0  # Default

                            # For legacy fields, assume cost equals duration (backward compatibility)
                            cost_value = duration_value

                            # Apply quantity multiplier to operation cost
                            if quantity_multiplier != 1.0:
                                cost_value = cost_value * quantity_multiplier

                            if duration_value > 0:
                                operation_data = {
                                    'name': field.operation_name,
                                    'workcenter': field.workcenter_id.name if field.workcenter_id else '',
                                    'cost': cost_value,
                                    'duration': duration_value,
                                    'formula': field.duration_formula or '60.0',
                                    'question_number': field.question_number if field.question_number else None,
                                    'field_name': field.name,
                                    'source_type': 'field_legacy'
                                }

                                operations.append(operation_data)
                                total_cost += cost_value
                                _logger.info(f"[OPERATION_COSTS_BUG] MODEL: Added field legacy operation '{field.operation_name}' from field '{field.name}' - Cost: ${cost_value}")
                        except Exception as e:
                            _logger.warning(f"[OPERATION_COSTS_BUG] Error calculating cost for legacy field operation {field.operation_name}: {e}")

                    # Get new field operation mappings
                    _logger.info(f"[OPERATION_COSTS_BUG] MODEL: Checking field '{field.name}' - Found {len(field.operation_mapping_ids)} operation mappings")
                    for mapping in field.operation_mapping_ids:
                        try:
                            _logger.info(f"[OPERATION_COSTS_BUG] MODEL: Found field operation mapping: '{mapping.operation_name}' for field '{field.name}'")
                            # Check if this operation should be included based on conditions
                            if mapping.condition:
                                # Evaluate condition with current field values
                                condition_result = self._evaluate_condition(mapping.condition, field_values)
                                if not condition_result:
                                    continue

                            # Calculate duration and cost using the operation template
                            duration_value = 0.0
                            cost_value = 0.0

                            if mapping.operation_template_id:
                                # Use the operation template's calculation methods
                                duration_value = mapping.operation_template_id.get_calculated_duration(field_values)
                                cost_value = mapping.operation_template_id.get_calculated_cost(field_values)
                            elif hasattr(mapping, 'default_duration') and mapping.default_duration:
                                duration_value = mapping.default_duration
                                cost_value = mapping.default_duration  # Fallback: assume cost equals duration

                            # Apply quantity multiplier to operation cost
                            if quantity_multiplier != 1.0:
                                cost_value = cost_value * quantity_multiplier

                            if duration_value > 0:
                                operation_data = {
                                    'name': mapping.operation_name,
                                    'workcenter': mapping.workcenter_id.name if mapping.workcenter_id else '',
                                    'cost': cost_value,
                                    'duration': duration_value,
                                    'formula': mapping.duration_formula or 'Default value',
                                    'question_number': field.question_number if field.question_number else None,
                                    'field_name': field.name,
                                    'source_type': 'field'
                                }

                                operations.append(operation_data)
                                total_cost += cost_value
                                _logger.info(f"[OPERATION_COSTS_BUG] MODEL: Added field mapping operation '{mapping.operation_name}' from field '{field.name}' - Cost: ${cost_value}")

                        except Exception as e:
                            _logger.warning(f"[OPERATION_COSTS_BUG] Error calculating cost for field operation {mapping.operation_name}: {e}")
                            continue

                    # Get option operation mappings (same filtering as controller)
                    field_value = field_values.get(field.technical_name)
                    for option in field.option_ids.filtered(lambda o: o.value == field_value):

                        # Check for legacy single operation mapping first (same as controller)
                        if option.workcenter_id and option.operation_name:
                            try:
                                # Calculate duration using legacy formula
                                duration_value = 0.0
                                if option.duration_formula:
                                    duration_value = self._evaluate_formula(option.duration_formula, field_values, template)
                                else:
                                    duration_value = 60.0  # Default

                                # For legacy options, assume cost equals duration (backward compatibility)
                                cost_value = duration_value

                                # Apply quantity multiplier to operation cost
                                if quantity_multiplier != 1.0:
                                    cost_value = cost_value * quantity_multiplier

                                if duration_value > 0:
                                    operation_data = {
                                        'name': option.operation_name,
                                        'workcenter': option.workcenter_id.name if option.workcenter_id else '',
                                        'cost': cost_value,
                                        'duration': duration_value,
                                        'formula': option.duration_formula or '60.0',
                                        'question_number': field.question_number if field.question_number else None,
                                        'field_name': field.name,
                                        'option_name': option.name,
                                        'source_type': 'option_legacy'
                                    }

                                    operations.append(operation_data)
                                    total_cost += cost_value
                                    _logger.info(f"[OPERATION_COSTS_BUG] MODEL: Added option legacy operation '{option.operation_name}' from option '{option.name}' in field '{field.name}' - Cost: ${cost_value}")
                            except Exception as e:
                                _logger.warning(f"[OPERATION_COSTS_BUG] Error calculating cost for legacy option operation {option.operation_name}: {e}")

                        # Get new option operation mappings
                        for mapping in option.operation_mapping_ids:
                            try:
                                # Check if this operation should be included based on conditions
                                if mapping.condition:
                                    condition_result = self._evaluate_condition(mapping.condition, field_values)
                                    if not condition_result:
                                        continue

                                # Calculate duration and cost using the operation template
                                duration_value = 0.0
                                cost_value = 0.0

                                if mapping.operation_template_id:
                                    # Use the operation template's calculation methods
                                    duration_value = mapping.operation_template_id.get_calculated_duration(field_values)
                                    cost_value = mapping.operation_template_id.get_calculated_cost(field_values)
                                elif hasattr(mapping, 'default_duration') and mapping.default_duration:
                                    duration_value = mapping.default_duration
                                    cost_value = mapping.default_duration  # Fallback: assume cost equals duration

                                # Apply quantity multiplier to operation cost
                                if quantity_multiplier != 1.0:
                                    cost_value = cost_value * quantity_multiplier

                                if duration_value > 0:
                                    operation_data = {
                                        'name': mapping.operation_name,
                                        'workcenter': mapping.workcenter_id.name if mapping.workcenter_id else '',
                                        'cost': cost_value,
                                        'duration': duration_value,
                                        'formula': mapping.duration_formula or 'Default value',
                                        'question_number': field.question_number if field.question_number else None,
                                        'field_name': field.name,
                                        'option_name': option.name,
                                        'source_type': 'option'
                                    }

                                    operations.append(operation_data)
                                    total_cost += cost_value
                                    _logger.info(f"[OPERATION_COSTS_BUG] MODEL: Added option mapping operation '{mapping.operation_name}' from option '{option.name}' in field '{field.name}' - Cost: ${cost_value}")

                            except Exception as e:
                                _logger.warning(f"[OPERATION_COSTS_BUG] Error calculating cost for option operation {mapping.operation_name}: {e}")
                                continue

            _logger.info(f"[OPERATION_COSTS_BUG] ===== MODEL FIELD/OPTION MAPPING RESULT =====")
            _logger.info(f"[OPERATION_COSTS_BUG] Operations found: {len(operations)}")
            _logger.info(f"[OPERATION_COSTS_BUG] Total cost: ${total_cost}")
            _logger.info(f"[OPERATION_COSTS_BUG] Quantity multiplier: {quantity_multiplier}")

            return {
                'success': True,
                'operations': operations,
                'total_cost': total_cost,
                'quantity_multiplier': quantity_multiplier
            }

        except Exception as e:
            _logger.error(f"[OPERATION_COSTS_BUG] Error in model field/option mapping calculation: {e}")
            import traceback
            _logger.error(f"[OPERATION_COSTS_BUG] Traceback: {traceback.format_exc()}")
            return {'success': False, 'error': str(e)}

    def _evaluate_visibility_condition(self, condition, field_values):
        """Evaluate visibility condition using the same formula helper as controller"""
        if not condition:
            return True
        try:
            # Use the same formula helper as the controller for consistency
            formula_helper = self.env['config.matrix.formula.helper'].sudo()
            return formula_helper.evaluate_visibility_condition(condition, field_values, default_result=True)
        except Exception as e:
            _logger.error(f"[OPERATION_COSTS_BUG] Error evaluating visibility condition '{condition}': {e}")
            # Default to visible if evaluation fails (same as controller)
            return True

    def _evaluate_condition(self, condition, field_values):
        """Evaluate operation condition using the same formula helper as controller"""
        if not condition:
            return True
        try:
            # Use the same formula helper as the controller for consistency
            formula_helper = self.env['config.matrix.formula.helper'].sudo()
            return formula_helper.evaluate_condition(condition, field_values, default_result=False)
        except Exception as e:
            _logger.warning(f"[OPERATION_COSTS_BUG] Error evaluating condition '{condition}': {e}")
            return False

    def _evaluate_formula(self, formula, field_values, template):
        """Evaluate formula using the same formula helper as controller"""
        if not formula:
            return 60.0  # Default duration
        try:
            # Use the same formula helper as the controller for consistency
            formula_helper = self.env['config.matrix.formula.helper'].sudo()
            return formula_helper.evaluate_formula(formula, field_values, default_result=60.0)
        except Exception as e:
            _logger.warning(f"[OPERATION_COSTS_BUG] Error evaluating formula '{formula}': {e}")
            return 60.0


    def _add_operations_to_bom(self):
        """
        Add operations to the BOM for manufacturing purposes.
        This method only adds operations to the BOM, it doesn't change pricing.
        """
        self.ensure_one()

        if not self.bom_id:
            _logger.warning(f"No BOM found for configuration {self.id}, cannot add operations")
            return False

        _logger.info(f"Adding operations to BOM {self.bom_id.code} for configuration {self.id}")

        # This method is similar to the operation creation part of generate_bom()
        # but only focuses on adding operations, not changing prices
        try:
            # Get operation templates
            operation_templates = self.template_id.operation_template_ids
            if not operation_templates:
                _logger.info(f"No operation templates found for template {self.template_id.name}")
                # Don't return True here - we still need to check field/option mappings

            # Load configuration values
            config_values = {}
            if self.config_data:
                try:
                    config_values = json.loads(self.config_data)
                except Exception as e:
                    _logger.warning(f"Could not parse config_data: {str(e)}")

            # Create operations
            operations_created = 0
            for template in operation_templates:
                try:
                    # Calculate duration
                    duration = template.get_calculated_duration(config_values)

                    # Create operation
                    operation_data = {
                        'name': template.name,
                        'workcenter_id': template.workcenter_id.id,
                        'time_cycle': duration,
                        'time_cycle_manual': duration,
                        'sequence': template.sequence,
                        'worksheet_type': 'text',
                        'worksheet': False,
                        'note': False,
                        'is_config_generated': True,
                        'config_operation_template_id': template.id,
                        'bom_id': self.bom_id.id,
                    }

                    operation = self.env['mrp.routing.workcenter'].create(operation_data)
                    operations_created += 1
                    _logger.info(f"Added operation {template.name} to BOM (Duration: {duration} min)")
                    

                except Exception as e:
                    _logger.error(f"Error creating operation for template {template.name}: {str(e)}")
                    continue

            # Now add operations from field/option operation mappings
            
            # Get field operation mappings through sections and fields
            for section in self.template_id.section_ids:
                for field in section.field_ids:
                    # Check field visibility
                    if not self._evaluate_field_visibility(field, config_values):
                        continue
                    
                    # Process field operation mappings
                    for mapping in field.operation_mapping_ids:
                        try:
                            # Check if this operation should be included based on conditions
                            if mapping.condition:
                                condition_result = self._evaluate_condition(mapping.condition, config_values)
                                if not condition_result:
                                    continue
                            
                            # Calculate duration and cost using the operation template
                            duration_value = 0.0
                            if mapping.operation_template_id:
                                duration_value = mapping.operation_template_id.get_calculated_duration(config_values)
                            elif hasattr(mapping, 'default_duration') and mapping.default_duration:
                                duration_value = mapping.default_duration
                            
                            if duration_value > 0:
                                # Create operation data
                                operation_data = {
                                    'name': mapping.operation_name,
                                    'workcenter_id': mapping.workcenter_id.id if mapping.workcenter_id else False,
                                    'time_cycle': duration_value,
                                    'time_cycle_manual': duration_value,
                                    'sequence': mapping.sequence or 100,  # Default sequence
                                    'worksheet_type': 'text',
                                    'worksheet': False,
                                    'note': False,
                                    'is_config_generated': True,
                                    'config_operation_template_id': mapping.operation_template_id.id if mapping.operation_template_id else False,
                                    'bom_id': self.bom_id.id,
                                }
                                
                                operation = self.env['mrp.routing.workcenter'].create(operation_data)
                                operations_created += 1
                                
                        except Exception as e:
                            _logger.error(f"Error creating field operation for mapping {mapping.operation_name}: {str(e)}")
                            continue
                    
                    # Process option operation mappings
                    field_value = config_values.get(field.technical_name)
                    for option in field.option_ids.filtered(lambda o: o.value == field_value):
                        for mapping in option.operation_mapping_ids:
                            try:
                                # Check if this operation should be included based on conditions
                                if mapping.condition:
                                    condition_result = self._evaluate_condition(mapping.condition, config_values)
                                    if not condition_result:
                                        continue
                                
                                # Calculate duration and cost using the operation template
                                duration_value = 0.0
                                if mapping.operation_template_id:
                                    duration_value = mapping.operation_template_id.get_calculated_duration(config_values)
                                elif hasattr(mapping, 'default_duration') and mapping.default_duration:
                                    duration_value = mapping.default_duration
                                
                                if duration_value > 0:
                                    # Create operation data
                                    operation_data = {
                                        'name': mapping.operation_name,
                                        'workcenter_id': mapping.workcenter_id.id if mapping.workcenter_id else False,
                                        'time_cycle': duration_value,
                                        'time_cycle_manual': duration_value,
                                        'sequence': mapping.sequence or 100,  # Default sequence
                                        'worksheet_type': 'text',
                                        'worksheet': False,
                                        'note': False,
                                        'is_config_generated': True,
                                        'config_operation_template_id': mapping.operation_template_id.id if mapping.operation_template_id else False,
                                        'bom_id': self.bom_id.id,
                                    }
                                    
                                    operation = self.env['mrp.routing.workcenter'].create(operation_data)
                                    operations_created += 1
                                    
                            except Exception as e:
                                _logger.error(f"Error creating option operation for mapping {mapping.operation_name}: {str(e)}")
                                continue
            
            _logger.info(f"Successfully added {operations_created} operations to BOM {self.bom_id.code}")
            
            # DEBUG: Summary of operations added to BOM
            _logger.info(f"[BOM_OPERATION_SUMMARY] Added {operations_created} operations to BOM {self.bom_id.id}")
            _logger.info(f"[BOM_OPERATION_SUMMARY] BOM now has {len(self.bom_id.operation_ids)} total operations")
            
            return True

        except Exception as e:
            _logger.error(f"Error adding operations to BOM: {str(e)}")
            return False

    def _find_operation_template_for_operation(self, operation):
        """
        Try to find the operation template that was used to create this BOM operation

        Args:
            operation: The BOM operation record

        Returns:
            config.matrix.operation.template or False
        """
        
        # This is a best-effort match based on operation name and work center
        # In the future, we could store a direct reference during BOM generation
        templates = operation.config_operation_template_id
        if not templates:
            templates = self.env['config.matrix.operation.template'].search([
                ('name', '=', operation.name),
                ('workcenter_id', '=', operation.workcenter_id.id)
            ])

        result = templates[0] if templates else False
        if not result:
            _logger.warning(f"NO MATCH FOUND for operation: '{operation.name}'")
            
        return result

    def _evaluate_field_visibility(self, field, config_values):
        """Evaluate field visibility condition"""
        if not field.visibility_condition:
            return True
        
        try:
            # Use the formula helper model for evaluation
            formula_helper = self.env['config.matrix.formula.helper'].sudo()
            return formula_helper.evaluate_visibility_condition(field.visibility_condition, config_values, default_result=True)
        except Exception as e:
            _logger.error(f"Error evaluating field visibility condition '{field.visibility_condition}': {e}")
            return True

    def _evaluate_condition(self, condition, config_values):
        """Evaluate a condition string with field values"""
        if not condition or condition.strip() == 'true':
            return True

        try:
            # Use the formula helper model for evaluation
            formula_helper = self.env['config.matrix.formula.helper'].sudo()
            return formula_helper.evaluate_condition(condition, config_values, default_result=False)
        except Exception as e:
            _logger.warning(f"Error evaluating condition '{condition}': {e}")
            return False

    def save_calculated_fields_to_config_data(self):
        """
        Calculate and save calculated fields to config_data for mesh hunting.

        This ensures calculated fields are available when mesh hunting runs
        during order confirmation.
        """
        self.ensure_one()
        _logger.info(f"[CALC-FIELDS-DEBUG] save_calculated_fields_to_config_data called for config {self.id}")

        if not self.config_data:
            _logger.warning(f"[CALC-FIELDS-DEBUG] No config_data found for config {self.id}, cannot save calculated fields")
            return

        try:
            # Load existing config data
            config_values = json.loads(self.config_data)
            _logger.info(f"[CALC-FIELDS-DEBUG] Loaded config_values with {len(config_values)} fields")

            # Calculate all calculated fields
            if self.template_id:
                _logger.info(f"[CALC-FIELDS-DEBUG] Calculating fields for template {self.template_id.id}")
                calc_field_model = self.env['config.matrix.calculated.field']
                calculated_results = calc_field_model.calculate_fields(config_values, self.template_id.id)
                _logger.info(f"[CALC-FIELDS-DEBUG] Calculated {len(calculated_results)} fields: {list(calculated_results.keys())}")

                # Log mesh-specific calculated fields
                mesh_fields = {k: v for k, v in calculated_results.items() if 'mesh' in k.lower()}
                _logger.info(f"[CALC-FIELDS-DEBUG] Mesh-related calculated fields: {mesh_fields}")
                _logger.info(f"[MESH_DEBUG] Template mesh_series: {self.template_id.mesh_series}")
                _logger.info(f"[MESH_DEBUG] Calculated mesh series: {calculated_results.get('_CALCULATED_mesh_series')}")

                # Add calculated fields to config_values
                config_values.update(calculated_results)

                # Save back to config_data
                self.config_data = json.dumps(config_values)

                # Force commit to database
                self.env.cr.commit()

                _logger.info(f"[CALC-FIELDS-DEBUG] Successfully saved calculated fields to config {self.id}")
                _logger.info(f"[CALC-FIELDS-DEBUG] Final config_data length: {len(self.config_data)}")
                _logger.info(f"Saved calculated fields to config {self.id}: {list(calculated_results.keys())}")
            else:
                _logger.warning(f"[CALC-FIELDS-DEBUG] No template_id found for config {self.id}")

        except Exception as e:
            _logger.error(f"Error saving calculated fields to config {self.id}: {str(e)}")
            _logger.error(f"[CALC-FIELDS-DEBUG] Full error details: {str(e)}", exc_info=True)

    @api.model
    def fix_mesh_calculated_fields(self):
        """
        Fix mesh calculated field formulas - call this from the Odoo interface
        """
        _logger.info("Fixing mesh calculated field formulas...")

        # Update mesh_required formula
        mesh_required_field = self.env['config.matrix.calculated.field'].search([
            ('name', '=', '_CALCULATED_mesh_required')
        ])
        if mesh_required_field:
            mesh_required_field.write({
                'formula': '(_CALCULATED_largest_door_width > 0) && (_CALCULATED_largest_door_height > 0)',
                'depends_on': '_CALCULATED_largest_door_width,_CALCULATED_largest_door_height'
            })
            _logger.info("✓ Updated _CALCULATED_mesh_required formula")
        else:
            _logger.warning("✗ _CALCULATED_mesh_required field not found")

        # Update mesh_series formula
        mesh_series_field = self.env['config.matrix.calculated.field'].search([
            ('name', '=', '_CALCULATED_mesh_series')
        ])
        if mesh_series_field:
            mesh_series_field.write({
                'formula': "_CALCULATED_mesh_required ? (mesh_series || template_mesh_series || 'saltwaterseries') : ''",
                'depends_on': '_CALCULATED_mesh_required,mesh_series,template_mesh_series'
            })
            _logger.info("✓ Updated _CALCULATED_mesh_series formula")
        else:
            _logger.warning("✗ _CALCULATED_mesh_series field not found")

        # Test the fix with an existing configuration
        _logger.info("Testing mesh series calculation fix...")
        test_config = self.search([
            ('template_id.mesh_series', '!=', False),
            ('config_data', '!=', False)
        ], limit=1)

        if test_config:
            _logger.info(f"Testing with configuration {test_config.id} using template {test_config.template_id.name}")
            _logger.info(f"Template mesh series: {test_config.template_id.mesh_series}")

            # Recalculate fields
            test_config.save_calculated_fields_to_config_data()

            # Check the result
            config_data = json.loads(test_config.config_data or '{}')
            calculated_mesh_series = config_data.get('_CALCULATED_mesh_series')
            _logger.info(f"Calculated mesh series: {calculated_mesh_series}")

            if calculated_mesh_series == test_config.template_id.mesh_series:
                _logger.info("✓ Mesh series calculation fix working correctly")
            else:
                _logger.warning(f"✗ Expected {test_config.template_id.mesh_series}, got {calculated_mesh_series}")
        else:
            _logger.info("No test configuration found with mesh series")

    def debug_mesh_series_calculation(self):
        """Debug method to test mesh series calculation"""
        self.ensure_one()
        _logger.info(f"[MESH_DEBUG] === DEBUGGING MESH SERIES CALCULATION FOR CONFIG {self.id} ===")

        # Check template mesh series
        _logger.info(f"[MESH_DEBUG] Template: {self.template_id.name}")
        _logger.info(f"[MESH_DEBUG] Template mesh_series: {self.template_id.mesh_series}")
        _logger.info(f"[MESH_DEBUG] Template mesh_series type: {type(self.template_id.mesh_series)}")
        _logger.info(f"[MESH_DEBUG] Template mesh_series bool: {bool(self.template_id.mesh_series)}")

        # Load current config data
        config_values = json.loads(self.config_data or '{}')
        _logger.info(f"[MESH_DEBUG] Current config has {len(config_values)} fields")

        # Check for existing mesh fields
        mesh_fields = {k: v for k, v in config_values.items() if 'mesh' in k.lower()}
        _logger.info(f"[MESH_DEBUG] Existing mesh fields in config: {mesh_fields}")

        # Test manual calculation of mesh series
        _logger.info(f"[MESH_DEBUG] Testing manual mesh series calculation...")
        calc_field_model = self.env['config.matrix.calculated.field']

        # Test the specific mesh series field
        mesh_series_field = calc_field_model.search([('name', '=', '_CALCULATED_mesh_series')], limit=1)
        if mesh_series_field:
            _logger.info(f"[MESH_DEBUG] Found mesh series field: {mesh_series_field.name}")
            _logger.info(f"[MESH_DEBUG] Field formula: {mesh_series_field.formula}")
            _logger.info(f"[MESH_DEBUG] Field depends_on: {mesh_series_field.depends_on}")
            _logger.info(f"[MESH_DEBUG] Field active: {mesh_series_field.active}")

            # Test calculation with manual context
            test_context = {
                '_CALCULATED_mesh_required': True,
                'mesh_series': None,
                'template_mesh_series': self.template_id.mesh_series
            }

            try:
                # Use the formula helper for evaluation
                result = self.evaluate_formula(mesh_series_field.formula, test_context)
                _logger.info(f"[MESH_DEBUG] Formula evaluation result: {result}")
            except Exception as e:
                _logger.error(f"[MESH_DEBUG] Formula evaluation failed: {e}")
        else:
            _logger.error(f"[MESH_DEBUG] Mesh series field not found!")

        # Force recalculation
        _logger.info(f"[MESH_DEBUG] Forcing recalculation of fields...")
        self.save_calculated_fields_to_config_data()

        # Check results
        updated_config_values = json.loads(self.config_data or '{}')
        updated_mesh_fields = {k: v for k, v in updated_config_values.items() if 'mesh' in k.lower()}
        _logger.info(f"[MESH_DEBUG] Updated mesh fields: {updated_mesh_fields}")

        _logger.info(f"[MESH_DEBUG] === END DEBUG ===")

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Debug Complete',
                'message': f'Check logs for mesh series debug info for config {self.id}',
                'type': 'info',
            }
        }

    def apply_to_sale_order_line(self):
        """
        Apply the configuration to the linked sales order line.

        This method:
        1. Generates a BOM if one doesn't exist
        2. Calculates the price based on the configuration
        3. Updates the sales order line with the configuration and price
        4. Optionally creates detailed line items based on template setting
        5. Updates the configuration state to 'applied'

        Returns:
            bool: True if successful

        Raises:
            UserError: If the configuration is not linked to a sales order line
        """
        self.ensure_one()

        if not self.sale_order_line_id:
            raise UserError(_("This configuration is not linked to a sales order line."))

        try:
            # Save calculated fields to config_data for mesh hunting
            self.save_calculated_fields_to_config_data()

            # Generate BOM if not already generated
            if not self.bom_id:
                _logger.info(f"Generating BOM for configuration {self.id} before applying to order line")
                self.generate_bom()

            # SIMPLE FIX: Just calculate the price and use it
            total_price = self.calculate_price()
            _logger.info(f"[APPLY_TO_SALE_ORDER] Applying configuration {self.id} to order line {self.sale_order_line_id.id}")
            _logger.info(f"[APPLY_TO_SALE_ORDER] Current line price: ${self.sale_order_line_id.price_unit}")
            _logger.info(f"[APPLY_TO_SALE_ORDER] Calculated total price: ${total_price}")
            _logger.info(f"[APPLY_TO_SALE_ORDER] Breakdown - Matrix: ${self.price_matrix}, Components: ${self.price_component}, Operations: ${self.price_operations}")
            _logger.info(f"[APPLY_TO_SALE_ORDER] Configuration price_total: ${self.price_total}")

            # Always use aggregated pricing (no detailed line items)
            _logger.info(f"[APPLY_TO_SALE_ORDER] Using aggregated pricing for configuration {self.id}")

            # Update the order line
            self.sale_order_line_id.write({
                'config_id': self.id,
                'price_unit': total_price,
            })

            _logger.info(f"[APPLY_TO_SALE_ORDER] FINAL: Order line {self.sale_order_line_id.id} price set to ${total_price}")

            # Force recompute of configuration_summary
            self.sale_order_line_id._compute_configuration_summary()

            # Update state
            self.state = 'applied'

            return True
        except Exception as e:
            _logger.error(f"Error applying configuration {self.id} to order line: {str(e)}")
            raise UserError(_(
                "Failed to apply configuration to sales order line: %s\n\n"
                "Please check your configuration and try again."
            ) % str(e))



    def _compute_configuration_summary(self):
        """
        Compute the configuration_summary field.

        This method calls get_configuration_summary() to generate the summary text.
        """
        for config in self:
            config.configuration_summary = config.get_configuration_summary()

    def _compute_bom_line_ids(self):
        """
        Compute the bom_line_ids field.

        This method retrieves the BOM lines from the linked BOM.
        """
        for config in self:
            if config.bom_id:
                config.bom_line_ids = config.bom_id.bom_line_ids
            else:
                config.bom_line_ids = self.env['mrp.bom.line']

    @api.depends('price_component', 'price_operations', 'price_matrix')
    def _compute_price_total(self):
        """Compute total aggregated price including price matrix"""
        for config in self:
            # When saving configuration, use base prices (without multiplier)
            # The multiplier will be applied to product_uom_qty in the sale order line
            config.price_total = config.price_component + config.price_operations + config.price_matrix

    def action_view_bom(self):
        """
        Open the BOM form view.

        This method opens the BOM form view in a new window.
        If no BOM exists, it shows a warning message.

        Returns:
            dict: Action to open the BOM form view or a warning message
        """
        self.ensure_one()

        if not self.bom_id:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('No BOM Available'),
                    'message': _('No Bill of Materials has been generated for this configuration yet.'),
                    'type': 'warning',
                }
            }

        return {
            'name': _('Bill of Materials'),
            'type': 'ir.actions.act_window',
            'res_model': 'mrp.bom',
            'view_mode': 'form',
            'res_id': self.bom_id.id,
            'target': 'current',
        }

    def get_configuration_summary(self):
        """
        Get a human-readable summary of the configuration.

        This method generates a formatted text summary of the configuration,
        organized by sections and showing only visible fields with their values.

        Returns:
            str: A formatted summary of the configuration
        """
        self.ensure_one()

        if not self.config_data:
            return "No configuration data available."

        try:
            config_values = json.loads(self.config_data)
            summary_lines = []

            # Add header
            product_name = self.product_id.name or "Unknown Product"
            summary_lines.append(f"Configuration Summary for {product_name}")
            summary_lines.append("=" * 50)
            summary_lines.append("")

            # Process all sections and fields
            for section in self.template_id.section_ids:
                section_lines = []

                for field in section.field_ids:
                    # Skip fields that aren't visible based on configuration
                    if not self._is_field_visible(field, config_values):
                        continue

                    # Get field value
                    field_value = config_values.get(field.technical_name)
                    if field_value is None:
                        continue

                    # Format value based on field type
                    if field.field_type == 'boolean':
                        display_value = field.boolean_true_label if field_value else field.boolean_false_label
                    elif field.field_type == 'selection':
                        option = field.option_ids.filtered(lambda o: o.value == field_value)
                        display_value = option.name if option else field_value
                    elif field.field_type == 'number':
                        # Format number with appropriate precision
                        display_value = f"{field_value:g}"
                        if field.uom_id:
                            display_value += f" {field.uom_id.name}"
                    else:
                        display_value = str(field_value)

                    section_lines.append(f"{field.name}: {display_value}")

                if section_lines:
                    summary_lines.append(f"=== {section.name} ===")
                    summary_lines.extend(section_lines)
                    summary_lines.append("")

            # Add price information if available
            if self.price:
                summary_lines.append("-" * 30)
                summary_lines.append(f"Total Price: {self.price:.2f}")

            # Add BOM information if available
            if self.bom_id:
                summary_lines.append("-" * 30)
                summary_lines.append(f"BOM Reference: {self.bom_id.code or self.bom_id.display_name}")
                summary_lines.append(f"Component Count: {len(self.bom_id.bom_line_ids)}")

            return "\n".join(summary_lines)
        except Exception as e:
            _logger.error(f"Error generating configuration summary: {str(e)}")
            return f"Error generating configuration summary: {str(e)}"
    
    def _is_field_checked(self, field, config_values, active_use_case):
        """Helper method to determine if a checkbox field should be checked"""
        # Check if there's a saved value
        if config_values.get(str(field.id)):
            return True
            
        # Check default values based on use case
        if active_use_case == 'check_measure':
            default_value = field.check_measure_default_value
        elif active_use_case == 'sales':
            default_value = field.sales_default_value
        elif active_use_case == 'online':
            default_value = field.online_default_value
        else:
            return False
            
        # Check if default value indicates checked state
        if default_value in ['true', 'True', 'on', True, '1', 1]:
            return True
            
        return False
    
    def _has_default_value(self, field, config_values, active_use_case):
        """Helper method to determine if a field has a default value"""
        # Check if there's a saved value
        if config_values.get(str(field.id)):
            return True
            
        # Check default values based on use case
        if active_use_case == 'check_measure':
            return bool(field.check_measure_default_value or field.check_measure_default_option_id)
        elif active_use_case == 'sales':
            return bool(field.sales_default_value or field.sales_default_option_id)
        elif active_use_case == 'online':
            return bool(field.online_default_value or field.online_default_option_id)
        else:
            return False
    
    def _get_field_default_value(self, field, active_use_case):
        """Helper method to get the default value for a field"""
        if active_use_case == 'check_measure':
            return field.check_measure_default_value or ''
        elif active_use_case == 'sales':
            return field.sales_default_value or ''
        elif active_use_case == 'online':
            return field.online_default_value or ''
        else:
            return ''
    
    def _is_option_selected(self, option, field, config_values, active_use_case):
        """Helper method to determine if an option should be selected"""
        # Check if there's a saved value
        saved_value = config_values.get(str(field.id))
        if saved_value and str(option.value) == str(saved_value):
            return True
            
        # Check default values based on use case
        if not saved_value:
            if active_use_case == 'check_measure':
                if field.check_measure_default_option_id and option.id == field.check_measure_default_option_id.id:
                    return True
                if field.check_measure_default_value and str(option.value) == str(field.check_measure_default_value):
                    return True
            elif active_use_case == 'sales':
                if field.sales_default_option_id and option.id == field.sales_default_option_id.id:
                    return True
                if field.sales_default_value and str(option.value) == str(field.sales_default_value):
                    return True
            elif active_use_case == 'online':
                if field.online_default_option_id and option.id == field.online_default_option_id.id:
                    return True
                if field.online_default_value and str(option.value) == str(field.online_default_value):
                    return True
        
        return False

    def _add_mesh_byproducts_to_bom(self, bom):
        """Add byproducts from mesh cut operations to the BOM"""
        self.ensure_one()

        _logger.info(f"[BYPRODUCT_DEBUG] Starting byproduct integration for config {self.id}, BOM {bom.code}")

        # Find all mesh operations for this configuration
        all_mesh_operations = self.env['mesh.cut.operation'].search([
            ('config_id', '=', self.id)
        ])
        _logger.info(f"[BYPRODUCT_DEBUG] Found {len(all_mesh_operations)} total mesh operations for config {self.id}")

        for op in all_mesh_operations:
            _logger.info(f"[BYPRODUCT_DEBUG] Operation {op.id}: state={op.state}, cut_plan_id={op.cut_plan_id.id if op.cut_plan_id else 'None'}, source_type={op.source_type}")

        # Find mesh operations with cut plans
        mesh_operations = self.env['mesh.cut.operation'].search([
            ('config_id', '=', self.id),
            ('cut_plan_id', '!=', False)
        ])

        _logger.info(f"[BYPRODUCT_DEBUG] Found {len(mesh_operations)} mesh operations with cut plans")

        if not mesh_operations:
            _logger.warning(f"[BYPRODUCT_DEBUG] No mesh operations with cut plans found for configuration {self.id}")
            return

        byproducts_added = 0
        for mesh_operation in mesh_operations:
            cut_plan = mesh_operation.cut_plan_id
            _logger.info(f"[BYPRODUCT_DEBUG] Processing operation {mesh_operation.id} with cut plan {cut_plan.name} (ID: {cut_plan.id})")
            _logger.info(f"[BYPRODUCT_DEBUG] Cut plan has {len(cut_plan.byproduct_ids)} byproducts")

            if cut_plan and cut_plan.byproduct_ids:
                for byproduct in cut_plan.byproduct_ids:
                    _logger.info(f"[BYPRODUCT_DEBUG] Processing byproduct: {byproduct.product_id.name if byproduct.product_id else 'No Product'} (qty: {byproduct.quantity})")

                    if not byproduct.product_id:
                        _logger.warning(f"[BYPRODUCT_DEBUG] Skipping byproduct with no product")
                        continue

                    # Check if byproduct already exists in BOM
                    existing_byproduct = bom.byproduct_ids.filtered(
                        lambda b: b.product_id.id == byproduct.product_id.id
                    )

                    if not existing_byproduct:
                        # Create BOM byproduct record
                        try:
                            new_byproduct = self.env['mrp.bom.byproduct'].create({
                                'bom_id': bom.id,
                                'product_id': byproduct.product_id.id,
                                'product_qty': byproduct.quantity,
                                'product_uom_id': byproduct.product_id.uom_id.id,
                            })
                            byproducts_added += 1
                            _logger.info(f"[BYPRODUCT_DEBUG] ✓ Created BOM byproduct {new_byproduct.id}: {byproduct.product_id.name} (qty: {byproduct.quantity})")
                        except Exception as e:
                            _logger.error(f"[BYPRODUCT_DEBUG] ✗ Failed to create BOM byproduct: {e}")
                    else:
                        # Update quantity if byproduct already exists
                        old_qty = existing_byproduct.product_qty
                        existing_byproduct.product_qty += byproduct.quantity
                        _logger.info(f"[BYPRODUCT_DEBUG] ✓ Updated existing BOM byproduct {byproduct.product_id.name}: {old_qty} → {existing_byproduct.product_qty}")

        _logger.info(f"[BYPRODUCT_DEBUG] Completed: Added {byproducts_added} new byproducts to BOM {bom.code}")
        _logger.info(f"[BYPRODUCT_DEBUG] BOM {bom.code} now has {len(bom.byproduct_ids)} total byproducts")
